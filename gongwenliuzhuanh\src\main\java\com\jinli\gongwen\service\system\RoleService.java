package com.jinli.gongwen.service.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinli.gongwen.dto.system.RoleDTO;
import com.jinli.gongwen.dto.system.RoleQueryDTO;
import com.jinli.gongwen.entity.system.SysRole;
import com.jinli.gongwen.vo.system.RoleVO;

import java.util.List;

/**
 * 角色服务接口
 */
public interface RoleService {

    /**
     * 获取角色列表
     */
    IPage<RoleVO> getRoleList(Page<SysRole> page, RoleQueryDTO queryDTO);

    /**
     * 根据ID获取角色
     */
    RoleVO getRoleById(String roleId);

    /**
     * 创建角色
     */
    String createRole(RoleDTO roleDTO);

    /**
     * 更新角色
     */
    void updateRole(String roleId, RoleDTO roleDTO);

    /**
     * 删除角色
     */
    void deleteRole(String roleId);

    /**
     * 批量删除角色
     */
    void batchDeleteRoles(List<String> roleIds);

    /**
     * 启用/禁用角色
     */
    void toggleRoleStatus(String roleId, Boolean isEnabled);

    /**
     * 获取所有启用的角色
     */
    List<RoleVO> getAllEnabledRoles();

    /**
     * 分配角色菜单
     */
    void assignRoleMenus(String roleId, List<String> menuIds);

    /**
     * 获取角色菜单
     */
    List<String> getRoleMenus(String roleId);
}
