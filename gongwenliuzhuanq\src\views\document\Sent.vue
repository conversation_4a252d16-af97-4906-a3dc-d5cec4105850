<template>
  <div class="sent-documents">
    <div class="page-header">
      <h1 class="page-title">已发送公文</h1>
      <div class="page-actions">
        <el-button type="primary" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="公文标题">
          <el-input
            v-model="searchForm.title"
            placeholder="请输入公文标题"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        
        <el-form-item label="发送时间">
          <el-date-picker
            v-model="searchForm.sendTime"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="docId" label="公文编号" width="120" />
        
        <el-table-column prop="docTitle" label="公文标题" min-width="200" show-overflow-tooltip />
        
        <el-table-column prop="docType" label="公文类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getDocTypeColor(row.docType)" size="small">
              {{ getDocTypeName(row.docType) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="recipients" label="接收人" width="150" show-overflow-tooltip />
        
        <el-table-column prop="sendTime" label="发送时间" width="160">
          <template #default="{ row }">
            {{ formatTime(row.sendTime) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="readStatus" label="阅读状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getReadStatusColor(row.readStatus)" size="small">
              {{ row.readStatus }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="replyStatus" label="回复状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getReplyStatusColor(row.replyStatus)" size="small">
              {{ row.replyStatus }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="info" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button type="primary" size="small" @click="handleTrack(row)">
              跟踪
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getDocumentPage } from '@/api/document'
import dayjs from 'dayjs'
import { Refresh, Search } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const tableData = ref([])

// 搜索表单
const searchForm = reactive({
  title: '',
  sendTime: null
})

// 分页数据
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 方法
const formatTime = (timestamp) => {
  return timestamp ? dayjs(timestamp).format('YYYY-MM-DD HH:mm') : '-'
}

const getDocTypeName = (type) => {
  const typeMap = {
    'NOTICE': '通知',
    'REQUEST': '请示',
    'REPORT': '报告',
    'OPINION': '意见'
  }
  return typeMap[type] || type
}

const getDocTypeColor = (type) => {
  const colorMap = {
    'NOTICE': 'primary',
    'REQUEST': 'warning',
    'REPORT': 'success',
    'OPINION': 'info'
  }
  return colorMap[type] || 'info'
}

const getReadStatusColor = (status) => {
  const colorMap = {
    '已读': 'success',
    '未读': 'warning',
    '部分已读': 'info'
  }
  return colorMap[status] || 'info'
}

const getReplyStatusColor = (status) => {
  const colorMap = {
    '已回复': 'success',
    '未回复': 'danger',
    '部分回复': 'warning'
  }
  return colorMap[status] || 'info'
}

const loadData = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: pagination.current,
      pageSize: pagination.size,
      status: 'SENT',
      ...searchForm
    }
    
    const response = await getDocumentPage(params)
    if (response.code === 200) {
      // 模拟已发送公文数据
      const sentData = (response.data.records || []).map(item => ({
        ...item,
        recipients: ['张部长', '李主任', '王科长'][Math.floor(Math.random() * 3)],
        sendTime: Date.now() - Math.random() * 15 * 24 * 60 * 60 * 1000,
        readStatus: ['已读', '未读', '部分已读'][Math.floor(Math.random() * 3)],
        replyStatus: ['已回复', '未回复', '部分回复'][Math.floor(Math.random() * 3)]
      }))
      
      tableData.value = sentData
      pagination.total = response.data.total || 0
    }
  } catch (error) {
    console.error('加载已发送公文失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  loadData()
}

const handleSearch = () => {
  pagination.current = 1
  loadData()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    title: '',
    sendTime: null
  })
  pagination.current = 1
  loadData()
}

const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  loadData()
}

const handleCurrentChange = (current) => {
  pagination.current = current
  loadData()
}

const handleView = (row) => {
  ElMessage.info(`查看公文: ${row.docTitle}`)
}

const handleTrack = (row) => {
  ElMessage.info(`跟踪公文: ${row.docTitle}`)
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.sent-documents {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .page-title {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .search-card {
    margin-bottom: 20px;
    
    .search-form {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }
  
  .table-card {
    .pagination-container {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }
  }
}
</style>
