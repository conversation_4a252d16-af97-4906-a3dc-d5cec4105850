package com.jinli.gongwen.controller.system;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinli.gongwen.common.Result;
import com.jinli.gongwen.dto.system.RoleDTO;
import com.jinli.gongwen.dto.system.RoleQueryDTO;
import com.jinli.gongwen.entity.system.SysRole;
import com.jinli.gongwen.service.system.RoleService;
import com.jinli.gongwen.vo.system.RoleVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 角色管理控制器
 * 系统管理员专用接口
 */
@Api(tags = "角色管理")
@RestController
@RequestMapping("/api/system/role")
@PreAuthorize("hasRole('ADMIN')")
public class RoleController {

    @Autowired
    private RoleService roleService;

    /**
     * 角色列表查询（分页）
     */
    @ApiOperation("角色列表查询")
    @GetMapping("/list")
    public Result<IPage<RoleVO>> list(RoleQueryDTO queryDTO) {
        Page<SysRole> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        QueryWrapper<SysRole> queryWrapper = new QueryWrapper<>();
        
        // 构建查询条件
        if (queryDTO.getRoleName() != null && !queryDTO.getRoleName().trim().isEmpty()) {
            queryWrapper.like("role_name", queryDTO.getRoleName().trim());
        }
        if (queryDTO.getRoleCode() != null && !queryDTO.getRoleCode().trim().isEmpty()) {
            queryWrapper.like("role_code", queryDTO.getRoleCode().trim());
        }
        if (queryDTO.getIsEnabled() != null) {
            queryWrapper.eq("is_enabled", queryDTO.getIsEnabled());
        }

        queryWrapper.orderByAsc("sort_order");

        IPage<RoleVO> result = roleService.getRoleList(page, queryDTO);
        return Result.success(result);
    }

    /**
     * 获取所有角色（不分页）
     */
    @ApiOperation("获取所有角色")
    @GetMapping("/all")
    public Result<List<RoleVO>> getAllRoles() {
        List<RoleVO> roles = roleService.getAllEnabledRoles();
        return Result.success(roles);
    }

    /**
     * 获取角色详情
     */
    @ApiOperation("获取角色详情")
    @GetMapping("/{roleId}")
    public Result<RoleVO> getRole(@PathVariable String roleId) {
        RoleVO roleVO = roleService.getRoleById(roleId);
        return Result.success(roleVO);
    }

    /**
     * 新增角色
     */
    @ApiOperation("新增角色")
    @PostMapping
    public Result<Void> add(@Valid @RequestBody RoleDTO roleDTO) {
        roleService.createRole(roleDTO);
        return Result.success();
    }

    /**
     * 修改角色
     */
    @ApiOperation("修改角色")
    @PutMapping("/{roleId}")
    public Result<Void> update(@PathVariable String roleId, @Valid @RequestBody RoleDTO roleDTO) {
        roleService.updateRole(roleId, roleDTO);
        return Result.success();
    }

    /**
     * 删除角色
     */
    @ApiOperation("删除角色")
    @DeleteMapping("/{roleId}")
    public Result<Void> delete(@PathVariable String roleId) {
        roleService.deleteRole(roleId);
        return Result.success();
    }

    /**
     * 启用/停用角色
     */
    @ApiOperation("启用/停用角色")
    @PutMapping("/{roleId}/status")
    public Result<Void> changeStatus(@PathVariable String roleId, @RequestParam Boolean isEnabled) {
        roleService.toggleRoleStatus(roleId, isEnabled);
        return Result.success();
    }

    /**
     * 获取角色权限菜单
     */
    @ApiOperation("获取角色权限菜单")
    @GetMapping("/{roleId}/menus")
    public Result<List<String>> getRoleMenus(@PathVariable String roleId) {
        List<String> menuIds = roleService.getRoleMenus(roleId);
        return Result.success(menuIds);
    }

    /**
     * 分配角色权限
     */
    @ApiOperation("分配角色权限")
    @PutMapping("/{roleId}/menus")
    public Result<Void> assignMenus(@PathVariable String roleId, @RequestBody List<String> menuIds) {
        roleService.assignRoleMenus(roleId, menuIds);
        return Result.success();
    }
}
