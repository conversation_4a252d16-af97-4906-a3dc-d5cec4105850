package com.jinli.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jinli.dto.DepartmentQueryDTO;
import com.jinli.entity.SysDepartment;

import java.util.List;

/**
 * 部门服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-30
 */
public interface ISysDepartmentService extends IService<SysDepartment> {

    /**
     * 查询部门列表
     * 
     * @param queryDTO 查询条件
     * @return 部门列表
     */
    List<SysDepartment> selectDepartmentList(DepartmentQueryDTO queryDTO);

    /**
     * 查询部门树形结构
     * 
     * @param queryDTO 查询条件
     * @return 部门树
     */
    List<SysDepartment> selectDepartmentTree(DepartmentQueryDTO queryDTO);

    /**
     * 根据部门ID查询部门详情
     * 
     * @param deptId 部门ID
     * @return 部门信息
     */
    SysDepartment selectDepartmentById(String deptId);

    /**
     * 新增部门
     * 
     * @param department 部门信息
     * @return 操作结果
     */
    boolean insertDepartment(SysDepartment department);

    /**
     * 更新部门
     * 
     * @param department 部门信息
     * @return 操作结果
     */
    boolean updateDepartment(SysDepartment department);

    /**
     * 删除部门
     * 
     * @param deptId 部门ID
     * @return 操作结果
     */
    boolean deleteDepartment(String deptId);

    /**
     * 批量删除部门
     * 
     * @param deptIds 部门ID列表
     * @return 操作结果
     */
    boolean batchDeleteDepartments(List<String> deptIds);

    /**
     * 更新部门状态
     * 
     * @param deptId 部门ID
     * @param status 状态
     * @return 操作结果
     */
    boolean updateDepartmentStatus(String deptId, String status);

    /**
     * 检查部门编码是否存在
     * 
     * @param deptCode 部门编码
     * @param excludeDeptId 排除的部门ID
     * @return 是否存在
     */
    boolean checkDeptCodeExists(String deptCode, String excludeDeptId);

    /**
     * 检查部门名称是否存在（同一父部门下）
     * 
     * @param deptName 部门名称
     * @param parentId 父部门ID
     * @param excludeDeptId 排除的部门ID
     * @return 是否存在
     */
    boolean checkDeptNameExists(String deptName, String parentId, String excludeDeptId);

    /**
     * 查询子部门列表
     * 
     * @param parentId 父部门ID
     * @return 子部门列表
     */
    List<SysDepartment> selectChildrenByParentId(String parentId);

    /**
     * 查询部门下的用户数量
     * 
     * @param deptId 部门ID
     * @return 用户数量
     */
    Integer selectUserCountByDeptId(String deptId);

    /**
     * 查询所有祖先部门
     * 
     * @param deptId 部门ID
     * @return 祖先部门列表
     */
    List<SysDepartment> selectAncestorDepartments(String deptId);

    /**
     * 查询所有后代部门
     * 
     * @param deptId 部门ID
     * @return 后代部门列表
     */
    List<SysDepartment> selectDescendantDepartments(String deptId);

    /**
     * 查询部门树形结构（用于下拉选择）
     * 
     * @param excludeDeptId 排除的部门ID
     * @return 部门树
     */
    List<SysDepartment> selectDepartmentTreeForSelect(String excludeDeptId);

    /**
     * 查询根部门列表
     * 
     * @return 根部门列表
     */
    List<SysDepartment> selectRootDepartments();

    /**
     * 查询部门路径
     * 
     * @param deptId 部门ID
     * @return 部门路径
     */
    String selectDepartmentPath(String deptId);

    /**
     * 查询部门层级
     * 
     * @param deptId 部门ID
     * @return 部门层级
     */
    Integer selectDepartmentLevel(String deptId);

    /**
     * 查询启用的部门列表
     * 
     * @return 启用的部门列表
     */
    List<SysDepartment> selectEnabledDepartments();

    /**
     * 查询部门统计信息
     * 
     * @return 统计信息
     */
    List<SysDepartment> selectDepartmentStatistics();

    /**
     * 构建部门树形结构
     * 
     * @param departments 部门列表
     * @return 部门树
     */
    List<SysDepartment> buildDepartmentTree(List<SysDepartment> departments);

    /**
     * 构建部门树形结构（指定根节点）
     * 
     * @param departments 部门列表
     * @param rootId 根节点ID
     * @return 部门树
     */
    List<SysDepartment> buildDepartmentTree(List<SysDepartment> departments, String rootId);

    /**
     * 验证部门数据
     * 
     * @param department 部门信息
     * @return 验证结果
     */
    String validateDepartment(SysDepartment department);

    /**
     * 检查是否可以删除部门
     * 
     * @param deptId 部门ID
     * @return 检查结果
     */
    String checkCanDeleteDepartment(String deptId);

    /**
     * 获取部门的完整信息（包含统计数据）
     *
     * @param deptId 部门ID
     * @return 部门完整信息
     */
    SysDepartment getDepartmentFullInfo(String deptId);
}
