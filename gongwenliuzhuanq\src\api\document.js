import request from '@/utils/request'

// 分页查询公文列表
export function getDocumentPage(params) {
  return request({
    url: '/api/document/page',
    method: 'get',
    params
  })
}

// 根据ID查询公文详情
export function getDocumentById(docId) {
  return request({
    url: `/api/document/${docId}`,
    method: 'get'
  })
}

// 创建公文
export function createDocument(data) {
  return request({
    url: '/document',
    method: 'post',
    data
  })
}

// 更新公文
export function updateDocument(data) {
  return request({
    url: '/document',
    method: 'put',
    data
  })
}

// 删除公文
export function deleteDocument(docId) {
  return request({
    url: `/document/${docId}`,
    method: 'delete'
  })
}

// 批量删除公文
export function deleteDocuments(docIds) {
  return request({
    url: '/document/batch',
    method: 'delete',
    data: docIds
  })
}

// 提交公文
export function submitDocument(docId, userId) {
  return request({
    url: `/document/${docId}/submit`,
    method: 'post',
    params: { userId }
  })
}

// 审核公文
export function reviewDocument(docId, data) {
  return request({
    url: `/document/${docId}/review`,
    method: 'post',
    data
  })
}

// 发送公文
export function sendDocument(docId, data) {
  return request({
    url: `/document/${docId}/send`,
    method: 'post',
    data
  })
}

// 签收公文
export function receiveDocument(docId, userId) {
  return request({
    url: `/document/${docId}/receive`,
    method: 'post',
    params: { userId }
  })
}

// 查询待处理公文
export function getPendingDocuments(params) {
  return request({
    url: '/api/document/pending',
    method: 'get',
    params
  })
}

// 查询已处理公文
export function getProcessedDocuments(params) {
  return request({
    url: '/document/processed',
    method: 'get',
    params
  })
}

// 搜索公文
export function searchDocuments(params) {
  return request({
    url: '/document/search',
    method: 'get',
    params
  })
}

// 获取公文统计信息
export function getDocumentStatistics(params) {
  return request({
    url: '/api/document/statistics',
    method: 'get',
    params
  })
}
