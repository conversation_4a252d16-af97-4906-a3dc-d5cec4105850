<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinli.mapper.SysDepartmentMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.jinli.entity.SysDepartment">
        <id column="DEPT_ID" property="deptId" />
        <result column="DEPT_NAME" property="deptName" />
        <result column="DEPT_CODE" property="deptCode" />
        <result column="PARENT_ID" property="parentId" />
        <result column="LEADER" property="leader" />
        <result column="PHONE" property="phone" />
        <result column="EMAIL" property="email" />
        <result column="ORDER_NUM" property="sort" />
        <result column="STATUS" property="status" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="REMARK" property="remark" />
        <result column="PARENT_NAME" property="parentName" />
        <result column="USER_COUNT" property="userCount" />
        <result column="LEVEL" property="level" />
        <result column="DEPT_PATH" property="deptPath" />
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        DEPT_ID, DEPT_NAME, DEPT_CODE, PARENT_ID, LEADER, PHONE, EMAIL,
        ORDER_NUM, STATUS, CREATE_TIME, UPDATE_TIME, REMARK
    </sql>

    <!-- 查询部门列表 -->
    <select id="selectDepartmentList" resultMap="BaseResultMap">
        SELECT
            d.DEPT_ID, d.DEPT_NAME, d.DEPT_CODE, d.PARENT_ID, d.LEADER,
            d.PHONE, d.EMAIL, d.ORDER_NUM, d.STATUS, d.CREATE_TIME, d.UPDATE_TIME, d.REMARK,
            p.DEPT_NAME as PARENT_NAME
            <if test="query.includeUserCount">
                , (SELECT COUNT(*) FROM SYS_USER u WHERE u.DEPARTMENT_ID = d.DEPT_ID) as USER_COUNT
            </if>
        FROM SYS_DEPARTMENT d
        LEFT JOIN SYS_DEPARTMENT p ON d.PARENT_ID = p.DEPT_ID
        <where>
            <if test="query.deptName != null and query.deptName != ''">
                AND d.DEPT_NAME LIKE CONCAT('%', #{query.deptName}, '%')
            </if>
            <if test="query.deptCode != null and query.deptCode != ''">
                AND d.DEPT_CODE LIKE CONCAT('%', #{query.deptCode}, '%')
            </if>
            <if test="query.leader != null and query.leader != ''">
                AND d.LEADER LIKE CONCAT('%', #{query.leader}, '%')
            </if>
            <if test="query.status != null and query.status != ''">
                AND d.STATUS = #{query.status}
            </if>
            <if test="query.parentId != null and query.parentId != ''">
                AND d.PARENT_ID = #{query.parentId}
            </if>
            <if test="query.excludeDeptId != null and query.excludeDeptId != ''">
                AND d.DEPT_ID != #{query.excludeDeptId}
            </if>
            <if test="query.onlyEnabled">
                AND d.STATUS = '1'
            </if>
            <if test="query.startTime != null and query.startTime != ''">
                AND d.CREATE_TIME >= #{query.startTime}
            </if>
            <if test="query.endTime != null and query.endTime != ''">
                AND d.CREATE_TIME &lt;= #{query.endTime}
            </if>
        </where>
        <if test="query.orderBy != null and query.orderBy != ''">
            ORDER BY ${query.orderBy}
        </if>
    </select>

    <!-- 根据部门ID查询部门详情 -->
    <select id="selectDepartmentById" resultMap="BaseResultMap">
        SELECT
            d.DEPT_ID, d.DEPT_NAME, d.DEPT_CODE, d.PARENT_ID, d.LEADER,
            d.PHONE, d.EMAIL, d.ORDER_NUM, d.STATUS, d.CREATE_TIME, d.UPDATE_TIME, d.REMARK,
            p.DEPT_NAME as PARENT_NAME,
            (SELECT COUNT(*) FROM SYS_USER u WHERE u.DEPARTMENT_ID = d.DEPT_ID) as USER_COUNT
        FROM SYS_DEPARTMENT d
        LEFT JOIN SYS_DEPARTMENT p ON d.PARENT_ID = p.DEPT_ID
        WHERE d.DEPT_ID = #{deptId}
    </select>

    <!-- 查询子部门列表 -->
    <select id="selectChildrenByParentId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM SYS_DEPARTMENT
        WHERE PARENT_ID = #{parentId}
        AND STATUS = '1'
        ORDER BY ORDER_NUM ASC, CREATE_TIME ASC
    </select>

    <!-- 查询部门下的用户数量 -->
    <select id="selectUserCountByDeptId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM SYS_USER
        WHERE DEPARTMENT_ID = #{deptId}
    </select>

    <!-- 检查部门编码是否存在 -->
    <select id="checkDeptCodeExists" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM SYS_DEPARTMENT
        WHERE DEPT_CODE = #{deptCode}
        <if test="excludeDeptId != null and excludeDeptId != ''">
            AND DEPT_ID != #{excludeDeptId}
        </if>
    </select>

    <!-- 检查部门名称是否存在（同一父部门下） -->
    <select id="checkDeptNameExists" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM SYS_DEPARTMENT
        WHERE DEPT_NAME = #{deptName}
        <if test="parentId != null and parentId != ''">
            AND PARENT_ID = #{parentId}
        </if>
        <if test="parentId == null or parentId == ''">
            AND (PARENT_ID IS NULL OR PARENT_ID = '')
        </if>
        <if test="excludeDeptId != null and excludeDeptId != ''">
            AND DEPT_ID != #{excludeDeptId}
        </if>
    </select>

    <!-- 查询所有祖先部门 -->
    <select id="selectAncestorDepartments" resultMap="BaseResultMap">
        WITH RECURSIVE dept_tree AS (
            SELECT <include refid="Base_Column_List" />, 0 as level
            FROM SYS_DEPARTMENT
            WHERE DEPT_ID = #{deptId}
            
            UNION ALL
            
            SELECT d.<include refid="Base_Column_List" />, dt.level + 1
            FROM SYS_DEPARTMENT d
            INNER JOIN dept_tree dt ON d.DEPT_ID = dt.PARENT_ID
        )
        SELECT * FROM dept_tree WHERE level > 0
        ORDER BY level DESC
    </select>

    <!-- 查询所有后代部门 -->
    <select id="selectDescendantDepartments" resultMap="BaseResultMap">
        WITH RECURSIVE dept_tree AS (
            SELECT <include refid="Base_Column_List" />, 0 as level
            FROM SYS_DEPARTMENT
            WHERE DEPT_ID = #{deptId}
            
            UNION ALL
            
            SELECT d.<include refid="Base_Column_List" />, dt.level + 1
            FROM SYS_DEPARTMENT d
            INNER JOIN dept_tree dt ON d.PARENT_ID = dt.DEPT_ID
        )
        SELECT * FROM dept_tree WHERE level > 0
        ORDER BY level ASC, SORT ASC
    </select>

    <!-- 更新部门状态 -->
    <update id="updateDepartmentStatus">
        UPDATE SYS_DEPARTMENT
        SET STATUS = #{status}, UPDATE_TIME = SYSDATE
        WHERE DEPT_ID = #{deptId}
    </update>

    <!-- 批量更新子部门的父ID -->
    <update id="updateChildrenParentId">
        UPDATE SYS_DEPARTMENT
        SET PARENT_ID = #{newParentId}, UPDATE_TIME = SYSDATE
        WHERE PARENT_ID = #{oldParentId}
    </update>

    <!-- 查询部门树形结构（用于下拉选择） -->
    <select id="selectDepartmentTree" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM SYS_DEPARTMENT
        WHERE STATUS = '1'
        <if test="excludeDeptId != null and excludeDeptId != ''">
            AND DEPT_ID != #{excludeDeptId}
        </if>
        ORDER BY SORT ASC, CREATE_TIME ASC
    </select>

    <!-- 查询根部门列表 -->
    <select id="selectRootDepartments" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM SYS_DEPARTMENT
        WHERE (PARENT_ID IS NULL OR PARENT_ID = '' OR PARENT_ID = '0')
        AND STATUS = '1'
        ORDER BY SORT ASC, CREATE_TIME ASC
    </select>

    <!-- 查询部门路径 -->
    <select id="selectDepartmentPath" resultType="java.lang.String">
        WITH RECURSIVE dept_path AS (
            SELECT DEPT_ID, DEPT_NAME, PARENT_ID, DEPT_NAME as path
            FROM SYS_DEPARTMENT
            WHERE DEPT_ID = #{deptId}
            
            UNION ALL
            
            SELECT d.DEPT_ID, d.DEPT_NAME, d.PARENT_ID, CONCAT(d.DEPT_NAME, ' > ', dp.path)
            FROM SYS_DEPARTMENT d
            INNER JOIN dept_path dp ON d.DEPT_ID = dp.PARENT_ID
        )
        SELECT path FROM dept_path WHERE PARENT_ID IS NULL OR PARENT_ID = '' OR PARENT_ID = '0'
    </select>

    <!-- 查询部门层级 -->
    <select id="selectDepartmentLevel" resultType="java.lang.Integer">
        WITH RECURSIVE dept_level AS (
            SELECT DEPT_ID, PARENT_ID, 0 as level
            FROM SYS_DEPARTMENT
            WHERE DEPT_ID = #{deptId}
            
            UNION ALL
            
            SELECT d.DEPT_ID, d.PARENT_ID, dl.level + 1
            FROM SYS_DEPARTMENT d
            INNER JOIN dept_level dl ON d.DEPT_ID = dl.PARENT_ID
        )
        SELECT MAX(level) FROM dept_level
    </select>

    <!-- 查询同级部门的最大排序号 -->
    <select id="selectMaxSortByParentId" resultType="java.lang.Integer">
        SELECT COALESCE(MAX(ORDER_NUM), 0)
        FROM SYS_DEPARTMENT
        <where>
            <if test="parentId != null and parentId != ''">
                PARENT_ID = #{parentId}
            </if>
            <if test="parentId == null or parentId == ''">
                (PARENT_ID IS NULL OR PARENT_ID = '' OR PARENT_ID = '0')
            </if>
        </where>
    </select>

    <!-- 批量删除部门 -->
    <delete id="batchDeleteDepartments">
        DELETE FROM SYS_DEPARTMENT
        WHERE DEPT_ID IN
        <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
            #{deptId}
        </foreach>
    </delete>

    <!-- 查询启用的部门列表 -->
    <select id="selectEnabledDepartments" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM SYS_DEPARTMENT
        WHERE STATUS = '1'
        ORDER BY ORDER_NUM ASC, CREATE_TIME ASC
    </select>

    <!-- 查询部门统计信息 -->
    <select id="selectDepartmentStatistics" resultMap="BaseResultMap">
        SELECT 
            d.DEPT_ID, d.DEPT_NAME, d.DEPT_CODE, d.PARENT_ID, d.LEADER,
            (SELECT COUNT(*) FROM SYS_USER u WHERE u.DEPARTMENT_ID = d.DEPT_ID) as USER_COUNT
        FROM SYS_DEPARTMENT d
        WHERE d.STATUS = '1'
        ORDER BY d.ORDER_NUM ASC, d.CREATE_TIME ASC
    </select>

</mapper>
