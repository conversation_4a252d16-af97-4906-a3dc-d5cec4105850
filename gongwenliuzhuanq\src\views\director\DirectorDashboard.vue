<template>
  <div class="director-dashboard">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon pending-icon">
              <i class="el-icon-s-flag"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ dashboardData.pendingApprovalCount || 0 }}</div>
              <div class="stats-label">待审签</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon today-icon">
              <i class="el-icon-date"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ dashboardData.todayApprovedCount || 0 }}</div>
              <div class="stats-label">今日审签</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon strategic-icon">
              <i class="el-icon-trophy"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ dashboardData.strategicDecisionCount || 0 }}</div>
              <div class="stats-label">战略决策</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon rate-icon">
              <i class="el-icon-pie-chart"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ ((dashboardData.approvalRate || 0) * 100).toFixed(1) }}%</div>
              <div class="stats-label">通过率</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="chart-card">
          <div slot="header" class="card-header">
            <span>各部门公文统计</span>
          </div>
          <div id="deptDocChart" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="chart-card">
          <div slot="header" class="card-header">
            <span>本周审签趋势</span>
          </div>
          <div id="weeklyApprovalChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快捷操作和决策支持 -->
    <el-row :gutter="20" class="actions-row">
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="action-card">
          <div slot="header" class="card-header">
            <span>快捷操作</span>
          </div>
          <div class="action-buttons">
            <el-button type="primary" @click="goToPendingApproval">
              <i class="el-icon-s-flag"></i> 待审签公文
            </el-button>
            <el-button type="success" @click="goToApprovalHistory">
              <i class="el-icon-document"></i> 审签历史
            </el-button>
            <el-button type="warning" @click="goToCompanyOverview">
              <i class="el-icon-office-building"></i> 公司概览
            </el-button>
            <el-button type="info" @click="goToAllDocuments">
              <i class="el-icon-folder"></i> 所有公文
            </el-button>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="decision-card">
          <div slot="header" class="card-header">
            <span>决策支持</span>
          </div>
          <div class="decision-content" v-if="dashboardData.decisionSupport">
            <div class="decision-item">
              <div class="decision-label">重要决策</div>
              <div class="decision-value">
                {{ dashboardData.decisionSupport.importantDecisionCount || 0 }} 项
              </div>
            </div>
            <div class="decision-item">
              <div class="decision-label">待跟踪执行</div>
              <div class="decision-value">
                {{ dashboardData.decisionSupport.trackingCount || 0 }} 项
              </div>
            </div>
            <div class="decision-item">
              <div class="decision-label">预算影响</div>
              <div class="decision-value">
                ¥{{ (dashboardData.decisionSupport.totalBudgetImpact || 0).toLocaleString() }}
              </div>
            </div>
            <div class="decision-item">
              <div class="decision-label">高风险决策</div>
              <div class="decision-value">
                {{ dashboardData.decisionSupport.highRiskCount || 0 }} 项
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 部门绩效排名 -->
    <el-row :gutter="20" class="performance-row">
      <el-col :span="24">
        <el-card class="performance-card">
          <div slot="header" class="card-header">
            <span>部门绩效排名</span>
            <el-button type="text" @click="goToPerformanceDetail">查看详情</el-button>
          </div>
          <div class="performance-table">
            <el-table :data="dashboardData.deptPerformanceRanking" style="width: 100%">
              <el-table-column prop="ranking" label="排名" width="80" align="center">
                <template slot-scope="scope">
                  <el-tag :type="getRankingType(scope.row.ranking)" size="small">
                    {{ scope.row.ranking }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="deptName" label="部门" width="120" />
              <el-table-column prop="documentCount" label="公文数量" width="100" align="center" />
              <el-table-column prop="avgFlowTime" label="平均流转时间" width="120" align="center">
                <template slot-scope="scope">
                  {{ scope.row.avgFlowTime }}小时
                </template>
              </el-table-column>
              <el-table-column prop="passRate" label="通过率" width="100" align="center">
                <template slot-scope="scope">
                  {{ (scope.row.passRate * 100).toFixed(1) }}%
                </template>
              </el-table-column>
              <el-table-column prop="efficiencyScore" label="效率评分" width="100" align="center">
                <template slot-scope="scope">
                  <el-rate
                    :value="scope.row.efficiencyScore / 20"
                    disabled
                    show-score
                    text-color="#ff9900"
                  />
                </template>
              </el-table-column>
              <el-table-column label="趋势" width="100" align="center">
                <template slot-scope="scope">
                  <i class="el-icon-top trend-up" v-if="scope.row.ranking <= 2"></i>
                  <i class="el-icon-bottom trend-down" v-else-if="scope.row.ranking >= 4"></i>
                  <i class="el-icon-minus trend-stable" v-else></i>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 待审签和最近审签 -->
    <el-row :gutter="20" class="approval-row">
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="approval-card">
          <div slot="header" class="card-header">
            <span>待审签公文</span>
            <el-button type="text" @click="goToPendingApproval">查看更多</el-button>
          </div>
          <div class="approval-list">
            <div
              v-for="doc in dashboardData.pendingApprovals"
              :key="doc.docId"
              class="approval-item"
              @click="approveDocument(doc)"
            >
              <div class="approval-header">
                <div class="approval-title">{{ doc.docTitle }}</div>
                <el-tag :type="getLevelType(doc.docLevel)" size="mini">
                  {{ doc.docLevel }}
                </el-tag>
              </div>
              <div class="approval-meta">
                <span class="approval-dept">{{ doc.createDeptName }}</span>
                <span class="approval-type">{{ getDocTypeLabel(doc.docType) }}</span>
                <span class="approval-time">{{ doc.createTime }}</span>
              </div>
              <div class="approval-decision">
                <el-tag :type="getDecisionType(doc.estimatedDecisionLevel)" size="mini">
                  {{ getDecisionLabel(doc.estimatedDecisionLevel) }}
                </el-tag>
                <span class="waiting-time">等待 {{ doc.waitingHours }} 小时</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="approval-card">
          <div slot="header" class="card-header">
            <span>最近审签</span>
            <el-button type="text" @click="goToApprovalHistory">查看更多</el-button>
          </div>
          <div class="approval-list">
            <div
              v-for="approval in dashboardData.recentApprovals"
              :key="approval.docId"
              class="approval-item"
              @click="viewDocument(approval)"
            >
              <div class="approval-header">
                <div class="approval-title">{{ approval.docTitle }}</div>
                <el-tag :type="approval.isApproved ? 'success' : 'danger'" size="mini">
                  {{ approval.isApproved ? '通过' : '退回' }}
                </el-tag>
              </div>
              <div class="approval-meta">
                <span class="approval-dept">{{ approval.createDeptName }}</span>
                <span class="approval-type">{{ getDocTypeLabel(approval.docType) }}</span>
                <span class="approval-time">{{ approval.approvalTime }}</span>
              </div>
              <div class="approval-decision">
                <el-tag :type="getDecisionType(approval.decisionLevel)" size="mini">
                  {{ getDecisionLabel(approval.decisionLevel) }}
                </el-tag>
              </div>
              <div class="approval-opinion" v-if="approval.approvalOpinion">
                {{ approval.approvalOpinion }}
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getDirectorDashboard } from '@/api/director/dashboard'

export default {
  name: 'DirectorDashboard',
  data() {
    return {
      dashboardData: {
        pendingApprovalCount: 0,
        todayApprovedCount: 0,
        strategicDecisionCount: 0,
        approvalRate: 0,
        deptDocumentStats: {},
        weeklyApprovalTrend: [],
        pendingApprovals: [],
        recentApprovals: [],
        deptPerformanceRanking: [],
        decisionSupport: null
      },
      deptDocChart: null,
      weeklyApprovalChart: null
    }
  },
  mounted() {
    this.initData()
    this.initCharts()
    // 定时刷新数据
    this.timer = setInterval(() => {
      this.refreshData()
    }, 30000) // 每30秒刷新一次
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
    if (this.deptDocChart) {
      this.deptDocChart.dispose()
    }
    if (this.weeklyApprovalChart) {
      this.weeklyApprovalChart.dispose()
    }
  },
  methods: {
    // 初始化数据
    async initData() {
      await this.getDashboardData()
    },

    // 获取仪表板数据
    async getDashboardData() {
      try {
        const response = await getDirectorDashboard()
        this.dashboardData = response.data
        this.updateCharts()
      } catch (error) {
        this.$message.error('获取仪表板数据失败')
      }
    },

    // 刷新数据
    refreshData() {
      this.getDashboardData()
    },

    // 初始化图表
    initCharts() {
      this.initDeptDocChart()
      this.initWeeklyApprovalChart()
    },

    // 初始化部门公文图表
    initDeptDocChart() {
      const chartDom = document.getElementById('deptDocChart')
      this.deptDocChart = echarts.init(chartDom)
    },

    // 初始化周审签图表
    initWeeklyApprovalChart() {
      const chartDom = document.getElementById('weeklyApprovalChart')
      this.weeklyApprovalChart = echarts.init(chartDom)
    },

    // 更新图表
    updateCharts() {
      this.updateDeptDocChart()
      this.updateWeeklyApprovalChart()
    },

    // 更新部门公文图表
    updateDeptDocChart() {
      if (!this.deptDocChart) return
      
      const data = Object.entries(this.dashboardData.deptDocumentStats || {}).map(([key, value]) => ({
        name: this.getDeptLabel(key),
        value: value
      }))
      
      const option = {
        tooltip: {
          trigger: 'item'
        },
        series: [
          {
            name: '部门公文',
            type: 'pie',
            radius: ['40%', '70%'],
            data: data,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      this.deptDocChart.setOption(option)
    },

    // 更新周审签图表
    updateWeeklyApprovalChart() {
      if (!this.weeklyApprovalChart) return
      
      const weeklyData = this.dashboardData.weeklyApprovalTrend || []
      const dates = weeklyData.map(item => item.date)
      const approvalCount = weeklyData.map(item => item.approvalCount)
      const approvedCount = weeklyData.map(item => item.approvedCount)
      const strategicCount = weeklyData.map(item => item.strategicCount)
      
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['审签总数', '通过数量', '战略决策']
        },
        xAxis: {
          type: 'category',
          data: dates
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '审签总数',
            type: 'line',
            data: approvalCount,
            itemStyle: { color: '#409EFF' }
          },
          {
            name: '通过数量',
            type: 'line',
            data: approvedCount,
            itemStyle: { color: '#67C23A' }
          },
          {
            name: '战略决策',
            type: 'bar',
            data: strategicCount,
            itemStyle: { color: '#E6A23C' }
          }
        ]
      }
      this.weeklyApprovalChart.setOption(option)
    },

    // 获取部门标签
    getDeptLabel(deptId) {
      const deptMap = {
        'DEPT002': '办公室',
        'DEPT003': '生产部',
        'DEPT004': '销售部',
        'DEPT005': '财务部'
      }
      return deptMap[deptId] || deptId
    },

    // 获取公文类型标签
    getDocTypeLabel(type) {
      const typeMap = {
        'NOTICE': '通知',
        'REPORT': '报告',
        'REQUEST': '请示',
        'DECISION': '决定',
        'OPINION': '意见',
        'LETTER': '函',
        'MINUTES': '会议纪要'
      }
      return typeMap[type] || type
    },

    // 获取级别类型
    getLevelType(level) {
      const typeMap = {
        '普通': 'info',
        '紧急': 'warning',
        '特急': 'danger'
      }
      return typeMap[level] || 'info'
    },

    // 获取决策类型
    getDecisionType(level) {
      const typeMap = {
        'STRATEGIC': 'danger',
        'OPERATIONAL': 'warning',
        'ROUTINE': 'info'
      }
      return typeMap[level] || 'info'
    },

    // 获取决策标签
    getDecisionLabel(level) {
      const labelMap = {
        'STRATEGIC': '战略级',
        'OPERATIONAL': '运营级',
        'ROUTINE': '常规级'
      }
      return labelMap[level] || '常规级'
    },

    // 获取排名类型
    getRankingType(ranking) {
      if (ranking === 1) return 'success'
      if (ranking <= 3) return 'warning'
      return 'info'
    },

    // 导航到待审签公文
    goToPendingApproval() {
      this.$router.push('/director/pending-approval')
    },

    // 导航到审签历史
    goToApprovalHistory() {
      this.$router.push('/director/approval-history')
    },

    // 导航到公司概览
    goToCompanyOverview() {
      this.$router.push('/director/company-overview')
    },

    // 导航到所有公文
    goToAllDocuments() {
      this.$router.push('/director/all-documents')
    },

    // 导航到绩效详情
    goToPerformanceDetail() {
      this.$router.push('/director/performance-detail')
    },

    // 审签公文
    approveDocument(doc) {
      this.$router.push(`/director/approve/${doc.docId}`)
    },

    // 查看公文
    viewDocument(doc) {
      this.$router.push(`/director/document/${doc.docId}`)
    }
  }
}
</script>

<style scoped>
.director-dashboard {
  padding: 20px;
}

.stats-row,
.charts-row,
.actions-row,
.performance-row,
.approval-row {
  margin-bottom: 20px;
}

.stats-card {
  height: 120px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: white;
}

.pending-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.today-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.strategic-icon {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.rate-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.chart-card {
  height: 400px;
}

.chart-container {
  height: 320px;
}

.action-card,
.decision-card {
  height: 300px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.action-buttons .el-button {
  flex: 1;
  min-width: 120px;
}

.decision-content {
  padding: 10px 0;
}

.decision-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.decision-label {
  font-size: 14px;
  color: #606266;
}

.decision-value {
  font-weight: bold;
  color: #303133;
}

.performance-table {
  max-height: 300px;
  overflow-y: auto;
}

.trend-up {
  color: #67C23A;
}

.trend-down {
  color: #F56C6C;
}

.trend-stable {
  color: #909399;
}

.approval-card {
  height: 400px;
}

.approval-list {
  max-height: 320px;
  overflow-y: auto;
}

.approval-item {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s;
}

.approval-item:hover {
  background-color: #f8f9fa;
}

.approval-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.approval-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  margin-right: 10px;
}

.approval-meta {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 5px;
  font-size: 12px;
  color: #909399;
}

.approval-decision {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 5px;
}

.waiting-time {
  font-size: 12px;
  color: #E6A23C;
}

.approval-opinion {
  font-size: 12px;
  color: #606266;
  margin-top: 5px;
  background-color: #f5f7fa;
  padding: 5px;
  border-radius: 3px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .director-dashboard {
    padding: 10px;
  }
  
  .stats-card {
    margin-bottom: 10px;
  }
  
  .chart-card {
    height: 350px;
    margin-bottom: 10px;
  }
  
  .chart-container {
    height: 270px;
  }
}

@media (max-width: 800px) {
  .stats-content {
    flex-direction: column;
    text-align: center;
  }
  
  .stats-icon {
    margin-right: 0;
    margin-bottom: 10px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-buttons .el-button {
    width: 100%;
  }
  
  .decision-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .decision-value {
    margin-top: 5px;
  }
}
</style>
