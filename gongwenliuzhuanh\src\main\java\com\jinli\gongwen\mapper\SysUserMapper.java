package com.jinli.gongwen.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinli.gongwen.entity.SysUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户信息表 Mapper 接口
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
@Mapper
public interface SysUserMapper extends BaseMapper<SysUser> {

    /**
     * 根据用户名查询用户信息（包含角色和部门信息）
     * 
     * @param username 用户名
     * @return 用户信息
     */
    SysUser selectUserByUsername(@Param("username") String username);

    /**
     * 根据用户ID查询用户详细信息（包含角色和部门信息）
     * 
     * @param userId 用户ID
     * @return 用户信息
     */
    SysUser selectUserDetailById(@Param("userId") String userId);

    /**
     * 分页查询用户列表（包含部门和角色信息）
     * 
     * @param page 分页参数
     * @param username 用户名（模糊查询）
     * @param realName 真实姓名（模糊查询）
     * @param departmentId 部门ID
     * @param status 状态
     * @return 用户列表
     */
    IPage<SysUser> selectUserPage(Page<SysUser> page, 
                                  @Param("username") String username,
                                  @Param("realName") String realName,
                                  @Param("departmentId") String departmentId,
                                  @Param("status") String status);

    /**
     * 根据部门ID查询用户列表
     * 
     * @param departmentId 部门ID
     * @return 用户列表
     */
    List<SysUser> selectUsersByDepartmentId(@Param("departmentId") String departmentId);

    /**
     * 根据角色键查询用户列表
     * 
     * @param roleKey 角色键
     * @return 用户列表
     */
    List<SysUser> selectUsersByRoleKey(@Param("roleKey") String roleKey);

    /**
     * 检查用户名是否存在
     * 
     * @param username 用户名
     * @param userId 用户ID（排除自己）
     * @return 数量
     */
    int checkUsernameUnique(@Param("username") String username, @Param("userId") String userId);

    /**
     * 更新用户登录信息
     * 
     * @param userId 用户ID
     * @param loginIp 登录IP
     * @return 影响行数
     */
    int updateUserLoginInfo(@Param("userId") String userId, @Param("loginIp") String loginIp);

    /**
     * 重置用户密码
     * 
     * @param userId 用户ID
     * @param password 新密码
     * @return 影响行数
     */
    int resetUserPassword(@Param("userId") String userId, @Param("password") String password);

    /**
     * 根据用户ID列表查询用户信息
     * 
     * @param userIds 用户ID列表
     * @return 用户列表
     */
    List<SysUser> selectUsersByIds(@Param("userIds") List<String> userIds);

    /**
     * 统计部门下的用户数量
     * 
     * @param departmentId 部门ID
     * @return 用户数量
     */
    int countUsersByDepartmentId(@Param("departmentId") String departmentId);

    /**
     * 查询所有正常状态的用户
     * 
     * @return 用户列表
     */
    List<SysUser> selectAllActiveUsers();
}
