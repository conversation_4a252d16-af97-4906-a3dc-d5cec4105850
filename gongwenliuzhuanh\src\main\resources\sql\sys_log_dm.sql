-- 达梦数据库 - 系统日志表创建脚本

-- 创建系统日志表
CREATE TABLE sys_log (
    log_id VARCHAR(64) NOT NULL,
    username VARCHAR(50),
    real_name VARCHAR(50),
    module VARCHAR(50),
    operation_type VARCHAR(50),
    description VARCHAR(500),
    request_url VARCHAR(500),
    request_method VARCHAR(10),
    request_params CLOB,
    response_result CLOB,
    ip VARCHAR(50),
    user_agent VARCHAR(1000),
    status VARCHAR(20),
    error_msg CLOB,
    response_time BIGINT,
    create_time DATETIME DEFAULT SYSDATE,
    PRIMARY KEY (log_id)
);

-- 创建索引
CREATE INDEX idx_sys_log_username ON sys_log (username);
CREATE INDEX idx_sys_log_module ON sys_log (module);
CREATE INDEX idx_sys_log_operation_type ON sys_log (operation_type);
CREATE INDEX idx_sys_log_status ON sys_log (status);
CREATE INDEX idx_sys_log_create_time ON sys_log (create_time);
CREATE INDEX idx_sys_log_ip ON sys_log (ip);

-- 添加表注释（达梦数据库语法）
COMMENT ON TABLE sys_log IS '系统日志表';
COMMENT ON COLUMN sys_log.log_id IS '日志ID';
COMMENT ON COLUMN sys_log.username IS '操作用户名';
COMMENT ON COLUMN sys_log.real_name IS '用户真实姓名';
COMMENT ON COLUMN sys_log.module IS '操作模块';
COMMENT ON COLUMN sys_log.operation_type IS '操作类型';
COMMENT ON COLUMN sys_log.description IS '操作描述';
COMMENT ON COLUMN sys_log.request_url IS '请求URL';
COMMENT ON COLUMN sys_log.request_method IS '请求方法';
COMMENT ON COLUMN sys_log.request_params IS '请求参数';
COMMENT ON COLUMN sys_log.response_result IS '响应结果';
COMMENT ON COLUMN sys_log.ip IS '操作IP地址';
COMMENT ON COLUMN sys_log.user_agent IS '用户代理';
COMMENT ON COLUMN sys_log.status IS '操作状态（SUCCESS-成功，FAIL-失败）';
COMMENT ON COLUMN sys_log.error_msg IS '错误信息';
COMMENT ON COLUMN sys_log.response_time IS '响应时间（毫秒）';
COMMENT ON COLUMN sys_log.create_time IS '创建时间';

-- 提交事务
COMMIT;
