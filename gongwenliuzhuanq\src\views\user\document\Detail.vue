<template>
  <div class="document-detail">
    <el-card class="content-card" v-loading="loading">
      <template #header>
        <div class="card-header">
          <span>{{ document.docTitle }}</span>
          <div class="header-actions">
            <el-button @click="goBack">
              <el-icon><ArrowLeft /></el-icon>
              返回
            </el-button>
            <el-button @click="downloadDocument" v-if="document.docId">
              <el-icon><Download /></el-icon>
              下载
            </el-button>
            <el-button @click="printDocument" v-if="document.docId">
              <el-icon><Printer /></el-icon>
              打印
            </el-button>
          </div>
        </div>
      </template>

      <div class="document-content" v-if="document.docId">
        <!-- 公文基本信息 -->
        <el-descriptions :column="3" border class="document-info">
          <el-descriptions-item label="公文编号">{{ document.docNumber }}</el-descriptions-item>
          <el-descriptions-item label="公文类型">{{ document.docType }}</el-descriptions-item>
          <el-descriptions-item label="紧急程度">
            <el-tag :type="getLevelType(document.docLevel)" size="small">
              {{ document.docLevel }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="发起人">{{ document.createUserName }}</el-descriptions-item>
          <el-descriptions-item label="发起部门">{{ document.createDeptName }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ document.createTime }}</el-descriptions-item>
          <el-descriptions-item label="当前状态">
            <el-tag :type="getStatusType(document.docStatus)" size="small">
              {{ getStatusText(document.docStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="当前处理人">{{ document.currentHandlerName || '无' }}</el-descriptions-item>
          <el-descriptions-item label="办理期限">{{ document.deadline || '无' }}</el-descriptions-item>
        </el-descriptions>

        <!-- 公文正文 -->
        <div class="document-body">
          <h3>公文正文</h3>
          <div class="content-area">
            <pre>{{ document.docContent }}</pre>
          </div>
        </div>

        <!-- 附件列表 -->
        <div class="attachments-section" v-if="attachments.length">
          <h3>附件列表</h3>
          <el-table :data="attachments" border>
            <el-table-column prop="fileName" label="文件名" min-width="200" />
            <el-table-column prop="fileSize" label="文件大小" width="120">
              <template #default="{ row }">
                {{ formatFileSize(row.fileSize) }}
              </template>
            </el-table-column>
            <el-table-column prop="uploadUserName" label="上传人" width="120" />
            <el-table-column prop="uploadTime" label="上传时间" width="150" />
            <el-table-column label="操作" width="100">
              <template #default="{ row }">
                <el-button type="text" size="small" @click="downloadAttachment(row)">
                  下载
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 流转记录 -->
        <div class="flow-history-section">
          <h3>流转记录</h3>
          <el-timeline>
            <el-timeline-item
              v-for="record in flowHistory"
              :key="record.recordId"
              :timestamp="record.processTime"
              :type="getTimelineType(record.action)"
            >
              <div class="timeline-content">
                <div class="timeline-header">
                  <h4>{{ getActionText(record.action) }}</h4>
                  <el-tag size="small" :type="getStepTagType(record.flowStep)">
                    {{ getStepText(record.flowStep) }}
                  </el-tag>
                </div>
                <div class="timeline-body">
                  <p><strong>处理人：</strong>{{ record.fromUserName || '系统' }}</p>
                  <p v-if="record.fromDeptName"><strong>处理部门：</strong>{{ record.fromDeptName }}</p>
                  <p v-if="record.toUserName"><strong>流转至：</strong>{{ record.toUserName }}</p>
                  <p v-if="record.opinion" class="opinion-text">
                    <strong>处理意见：</strong>{{ record.opinion }}
                  </p>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>

        <!-- 签收记录 -->
        <div class="receive-records-section" v-if="receiveRecords.length">
          <h3>签收记录</h3>
          <el-table :data="receiveRecords" border>
            <el-table-column prop="receiveUserName" label="签收人" width="120" />
            <el-table-column prop="receiveDeptName" label="签收部门" width="150" />
            <el-table-column prop="receiveTime" label="签收时间" width="150" />
            <el-table-column prop="readTime" label="阅读时间" width="150">
              <template #default="{ row }">
                {{ row.readTime || '未阅读' }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="row.status === '1' ? 'success' : 'warning'" size="small">
                  {{ row.status === '1' ? '已读' : '未读' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="remark" label="备注" min-width="150" />
          </el-table>
        </div>
      </div>

      <div v-else class="empty-state">
        <el-empty description="公文不存在或已被删除" />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Download, Printer } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const document = ref({})
const attachments = ref([])
const flowHistory = ref([])
const receiveRecords = ref([])

// 方法
const loadDocumentDetail = async () => {
  loading.value = true
  try {
    const docId = route.params.id
    
    // 这里调用API获取公文详情
    // const response = await getDocumentDetail(docId)
    // document.value = response.data.document
    // attachments.value = response.data.attachments || []
    // flowHistory.value = response.data.flowHistory || []
    // receiveRecords.value = response.data.receiveRecords || []
    
    // 模拟数据
    document.value = {
      docId: docId,
      docTitle: '关于加强安全生产管理的通知',
      docNumber: 'JINLI-2025-001',
      docType: '通知',
      docLevel: '紧急',
      docStatus: 'COMPLETED',
      createUserName: '张三',
      createDeptName: '生产部',
      createTime: '2025-01-30 10:00',
      currentHandlerName: null,
      deadline: '2025-02-05 18:00',
      docContent: `各部门：

为进一步加强我集团安全生产管理工作，确保员工生命安全和企业财产安全，现就有关事项通知如下：

一、提高安全意识
各部门要高度重视安全生产工作，定期组织安全培训，提高员工安全意识。

二、完善安全制度
建立健全安全生产责任制，明确各级人员安全职责。

三、加强安全检查
定期开展安全隐患排查，及时整改发现的问题。

请各部门认真贯彻执行。

特此通知。

河北金力集团
2025年1月30日`
    }
    
    flowHistory.value = [
      {
        recordId: '1',
        flowStep: 'DRAFT',
        action: 'SUBMIT',
        fromUserName: '张三',
        fromDeptName: '生产部',
        opinion: '创建安全生产管理通知',
        processTime: '2025-01-30 10:00'
      },
      {
        recordId: '2',
        flowStep: 'OFFICE_EDIT',
        action: 'EDIT',
        fromUserName: '办公室',
        fromDeptName: '办公室',
        opinion: '格式规范，内容完善',
        processTime: '2025-01-30 11:00'
      },
      {
        recordId: '3',
        flowStep: 'VICE_REVIEW',
        action: 'APPROVE',
        fromUserName: '李副厂长',
        fromDeptName: '生产管理部',
        opinion: '同意发布，请各部门认真执行',
        processTime: '2025-01-30 15:30'
      },
      {
        recordId: '4',
        flowStep: 'DIRECTOR_APPROVE',
        action: 'APPROVE',
        fromUserName: '张厂长',
        fromDeptName: '厂长办公室',
        opinion: '批准执行',
        processTime: '2025-01-30 16:00'
      }
    ]
    
    receiveRecords.value = [
      {
        receiveUserName: '王主任',
        receiveDeptName: '销售部',
        receiveTime: '2025-01-30 17:00',
        readTime: '2025-01-30 17:30',
        status: '1',
        remark: '已阅读并传达'
      },
      {
        receiveUserName: '陈主任',
        receiveDeptName: '财务部',
        receiveTime: '2025-01-30 17:00',
        readTime: '2025-01-30 18:00',
        status: '1',
        remark: '已阅读'
      }
    ]
    
  } catch (error) {
    ElMessage.error('获取公文详情失败')
    console.error('Error loading document:', error)
  } finally {
    loading.value = false
  }
}

const goBack = () => {
  router.go(-1)
}

const downloadDocument = async () => {
  try {
    // 这里调用API下载公文
    // await downloadDocumentFile(document.value.docId)
    
    ElMessage.success('下载开始')
  } catch (error) {
    ElMessage.error('下载失败')
  }
}

const printDocument = () => {
  window.print()
}

const downloadAttachment = async (attachment) => {
  try {
    // 这里调用API下载附件
    // await downloadAttachmentFile(attachment.attachmentId)
    
    ElMessage.success('下载开始')
  } catch (error) {
    ElMessage.error('下载失败')
  }
}

const formatFileSize = (size) => {
  if (!size) return '0 B'
  const units = ['B', 'KB', 'MB', 'GB']
  let index = 0
  while (size >= 1024 && index < units.length - 1) {
    size /= 1024
    index++
  }
  return `${size.toFixed(1)} ${units[index]}`
}

const getLevelType = (level) => {
  const types = {
    '特急': 'danger',
    '紧急': 'warning',
    '普通': 'info'
  }
  return types[level] || 'info'
}

const getStatusType = (status) => {
  const types = {
    'DRAFT': 'info',
    'PROCESSING': 'warning',
    'APPROVED': 'success',
    'REJECTED': 'danger',
    'COMPLETED': 'success'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    'DRAFT': '草稿',
    'PROCESSING': '流转中',
    'APPROVED': '已批准',
    'REJECTED': '已退回',
    'COMPLETED': '已完成'
  }
  return texts[status] || status
}

const getStepText = (step) => {
  const texts = {
    'DRAFT': '草稿',
    'OFFICE_EDIT': '办公室修改',
    'VICE_REVIEW': '副厂长审核',
    'DIRECTOR_APPROVE': '厂长审签',
    'OFFICE_SEND': '办公室发送',
    'DEPT_RECEIVE': '部门签收'
  }
  return texts[step] || step
}

const getActionText = (action) => {
  const texts = {
    'SUBMIT': '提交',
    'APPROVE': '同意',
    'REJECT': '退回',
    'EDIT': '修改',
    'SEND': '发送',
    'RECEIVE': '签收',
    'TRANSFER': '转发'
  }
  return texts[action] || action
}

const getTimelineType = (action) => {
  const types = {
    'SUBMIT': 'primary',
    'APPROVE': 'success',
    'REJECT': 'danger',
    'EDIT': 'warning',
    'SEND': 'info',
    'RECEIVE': 'success',
    'TRANSFER': 'warning'
  }
  return types[action] || 'primary'
}

const getStepTagType = (step) => {
  const types = {
    'DRAFT': 'info',
    'OFFICE_EDIT': 'warning',
    'VICE_REVIEW': 'primary',
    'DIRECTOR_APPROVE': 'success',
    'OFFICE_SEND': 'info',
    'DEPT_RECEIVE': 'success'
  }
  return types[step] || 'info'
}

// 生命周期
onMounted(() => {
  loadDocumentDetail()
})
</script>

<style scoped>
.document-detail {
  padding: 20px;
}

.content-card {
  background: white;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.document-content {
  max-width: 1000px;
  margin: 0 auto;
}

.document-info {
  margin-bottom: 30px;
}

.document-body {
  margin-bottom: 30px;
}

.document-body h3 {
  margin-bottom: 15px;
  color: #303133;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.content-area {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 20px;
  min-height: 300px;
}

.content-area pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Microsoft YaHei', sans-serif;
  font-size: 14px;
  line-height: 1.6;
  margin: 0;
  color: #333;
}

.attachments-section,
.flow-history-section,
.receive-records-section {
  margin-bottom: 30px;
}

.attachments-section h3,
.flow-history-section h3,
.receive-records-section h3 {
  margin-bottom: 15px;
  color: #303133;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.timeline-content {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 10px;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.timeline-header h4 {
  margin: 0;
  color: #303133;
}

.timeline-body p {
  margin: 5px 0;
  color: #606266;
}

.opinion-text {
  background-color: #f0f9ff;
  padding: 10px;
  border-radius: 4px;
  border-left: 4px solid #409eff;
  margin-top: 10px;
}

.empty-state {
  text-align: center;
  padding: 50px 0;
}

@media print {
  .header-actions {
    display: none;
  }
  
  .document-detail {
    padding: 0;
  }
  
  .content-card {
    box-shadow: none;
    border: none;
  }
}
</style>
