package com.jinli.gongwen.service.document;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinli.gongwen.dto.document.DocumentApprovalDTO;

import java.util.List;

/**
 * 审批服务接口
 */
public interface ApprovalService {

    /**
     * 审批公文
     */
    void approveDocument(DocumentApprovalDTO approvalDTO);

    /**
     * 批量审批公文
     */
    void batchApproveDocuments(List<DocumentApprovalDTO> approvalDTOList);

    /**
     * 获取审批统计
     */
    Object getApprovalStatistics(String userId);

    /**
     * 获取审批历史
     */
    IPage<Object> getApprovalHistory(Page<Object> page, String userId);

    /**
     * 退回公文
     */
    void returnDocument(String docId, String reason, String userId);
}
