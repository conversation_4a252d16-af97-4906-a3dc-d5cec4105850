package com.jinli.gongwen.dto.system;

import com.jinli.gongwen.common.BaseQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 角色查询数据传输对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("角色查询数据传输对象")
public class RoleQueryDTO extends BaseQueryDTO {

    @ApiModelProperty("角色名称")
    private String roleName;

    @ApiModelProperty("角色编码")
    private String roleCode;

    @ApiModelProperty("是否启用")
    private Boolean isEnabled;
}
