package com.jinli.gongwen.dto.document;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 简化公文数据传输对象
 * 用于普通员工协助创建公文
 */
@Data
@ApiModel("简化公文数据传输对象")
public class SimpleDocumentDTO {

    @ApiModelProperty("公文ID")
    private String docId;

    @ApiModelProperty(value = "公文标题", required = true)
    @NotBlank(message = "公文标题不能为空")
    @Size(max = 200, message = "公文标题长度不能超过200个字符")
    private String docTitle;

    @ApiModelProperty(value = "公文类型", required = true)
    @NotBlank(message = "公文类型不能为空")
    private String docType;

    @ApiModelProperty("公文级别")
    private String docLevel = "普通";

    @ApiModelProperty(value = "公文内容", required = true)
    @NotBlank(message = "公文内容不能为空")
    private String docContent;

    @ApiModelProperty("创建用户ID")
    private String createUserId;

    @ApiModelProperty("创建部门ID")
    private String createDeptId;

    @ApiModelProperty("协助人员ID")
    private String assistantUserId;

    @ApiModelProperty("协助说明")
    @Size(max = 500, message = "协助说明长度不能超过500个字符")
    private String assistanceNote;

    @ApiModelProperty("模板ID")
    private String templateId;

    @ApiModelProperty("备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    @ApiModelProperty("是否需要部门经理审核")
    private Boolean needManagerReview = true;
}
