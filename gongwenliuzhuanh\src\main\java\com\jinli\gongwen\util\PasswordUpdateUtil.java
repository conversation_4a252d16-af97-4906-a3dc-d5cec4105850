package com.jinli.gongwen.util;

import cn.hutool.crypto.digest.BCrypt;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;

/**
 * 密码更新工具
 */
public class PasswordUpdateUtil {
    
    public static void main(String[] args) {
        updateAllPasswords();
    }
    
    public static void updateAllPasswords() {
        String url = "jdbc:dm://localhost:5236?schema=gongwen";
        String username = "SYSDBA";
        String password = "051001Hzy";
        String driverClass = "dm.jdbc.driver.DmDriver";
        
        try {
            Class.forName(driverClass);
            System.out.println("达梦JDBC驱动加载成功");
        } catch (ClassNotFoundException e) {
            System.err.println("达梦JDBC驱动加载失败: " + e.getMessage());
            return;
        }
        
        // 生成正确的123456密码哈希
        String correctPasswordHash = BCrypt.hashpw("123456", BCrypt.gensalt());
        System.out.println("生成的密码哈希: " + correctPasswordHash);
        
        try (Connection connection = DriverManager.getConnection(url, username, password)) {
            System.out.println("✓ 数据库连接成功!");
            
            // 更新所有用户密码
            String updateSql = "UPDATE SYS_USER SET PASSWORD = ?";
            try (PreparedStatement pstmt = connection.prepareStatement(updateSql)) {
                pstmt.setString(1, correctPasswordHash);
                int updatedRows = pstmt.executeUpdate();
                System.out.println("✓ 成功更新 " + updatedRows + " 个用户的密码");
            }
            
            // 验证更新结果
            System.out.println("\n=== 验证密码更新结果 ===");
            boolean testResult = BCrypt.checkpw("123456", correctPasswordHash);
            System.out.println("密码验证测试: " + (testResult ? "✓ 成功" : "✗ 失败"));
            
            System.out.println("\n✅ 所有用户密码已更新为: 123456");
            
        } catch (SQLException e) {
            System.err.println("✗ 数据库操作失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
