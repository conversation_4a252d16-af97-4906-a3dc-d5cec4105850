<template>
  <div class="pending-documents">
    <div class="page-header">
      <h1 class="page-title">待处理公文</h1>
      <div class="page-actions">
        <el-button type="primary" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="公文标题">
          <el-input
            v-model="searchForm.title"
            placeholder="请输入公文标题"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        
        <el-form-item label="公文类型">
          <el-select
            v-model="searchForm.docType"
            placeholder="请选择公文类型"
            clearable
            style="width: 150px"
          >
            <el-option label="通知" value="NOTICE" />
            <el-option label="请示" value="REQUEST" />
            <el-option label="报告" value="REPORT" />
            <el-option label="意见" value="OPINION" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="紧急程度">
          <el-select
            v-model="searchForm.urgency"
            placeholder="请选择紧急程度"
            clearable
            style="width: 120px"
          >
            <el-option label="紧急" value="urgent" />
            <el-option label="普通" value="normal" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="docId" label="公文编号" width="120" />
        
        <el-table-column prop="docTitle" label="公文标题" min-width="200" show-overflow-tooltip />
        
        <el-table-column prop="docType" label="公文类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getDocTypeColor(row.docType)" size="small">
              {{ getDocTypeName(row.docType) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="docLevel" label="紧急程度" width="100">
          <template #default="{ row }">
            <el-tag :type="row.docLevel === '紧急' ? 'danger' : 'info'" size="small">
              {{ row.docLevel }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="createUserName" label="发起人" width="100" />
        
        <el-table-column prop="createTime" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatTime(row.createTime) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="deadline" label="截止时间" width="160">
          <template #default="{ row }">
            <span :class="{ 'text-danger': isOverdue(row.deadline) }">
              {{ formatTime(row.deadline) }}
            </span>
          </template>
        </el-table-column>
        
        <el-table-column prop="currentStep" label="当前步骤" width="100" />
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleProcess(row)">
              处理
            </el-button>
            <el-button type="info" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button type="warning" size="small" @click="handleTransfer(row)">
              转办
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 批量操作 -->
    <div v-if="selectedRows.length > 0" class="batch-actions">
      <el-card shadow="never">
        <div class="batch-info">
          已选择 {{ selectedRows.length }} 项
          <el-button type="primary" size="small" @click="handleBatchProcess">
            批量处理
          </el-button>
          <el-button type="warning" size="small" @click="handleBatchTransfer">
            批量转办
          </el-button>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getPendingDocuments } from '@/api/document'
import { useUserStore } from '@/stores/user'
import dayjs from 'dayjs'
import { Refresh, Search } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const selectedRows = ref([])
const userStore = useUserStore()

// 计算属性
const userInfo = computed(() => userStore.userInfo)

// 搜索表单
const searchForm = reactive({
  title: '',
  docType: '',
  urgency: ''
})

// 分页数据
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 方法
const formatTime = (timestamp) => {
  return timestamp ? dayjs(timestamp).format('YYYY-MM-DD HH:mm') : '-'
}

const isOverdue = (deadline) => {
  return deadline && dayjs(deadline).isBefore(dayjs())
}

const getDocTypeName = (type) => {
  const typeMap = {
    'NOTICE': '通知',
    'REQUEST': '请示',
    'REPORT': '报告',
    'OPINION': '意见'
  }
  return typeMap[type] || type
}

const getDocTypeColor = (type) => {
  const colorMap = {
    'NOTICE': 'primary',
    'REQUEST': 'warning',
    'REPORT': 'success',
    'OPINION': 'info'
  }
  return colorMap[type] || 'info'
}

const loadData = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: pagination.current,
      pageSize: pagination.size,
      userId: userInfo.value?.userId || 'USER001', // 添加userId参数
      ...searchForm
    }

    const response = await getPendingDocuments(params)
    if (response.code === 200) {
      tableData.value = response.data.records || []
      pagination.total = response.data.total || 0
    }
  } catch (error) {
    console.error('加载待处理公文失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  loadData()
}

const handleSearch = () => {
  pagination.current = 1
  loadData()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    title: '',
    docType: '',
    urgency: ''
  })
  pagination.current = 1
  loadData()
}

const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  loadData()
}

const handleCurrentChange = (current) => {
  pagination.current = current
  loadData()
}

const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

const handleProcess = (row) => {
  ElMessage.info(`处理公文: ${row.docTitle}`)
}

const handleView = (row) => {
  ElMessage.info(`查看公文: ${row.docTitle}`)
}

const handleTransfer = (row) => {
  ElMessage.info(`转办公文: ${row.docTitle}`)
}

const handleBatchProcess = () => {
  ElMessageBox.confirm(
    `确定要批量处理选中的 ${selectedRows.value.length} 个公文吗？`,
    '批量处理',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('批量处理成功')
    selectedRows.value = []
    loadData()
  }).catch(() => {
    ElMessage.info('已取消批量处理')
  })
}

const handleBatchTransfer = () => {
  ElMessage.info(`批量转办 ${selectedRows.value.length} 个公文`)
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.pending-documents {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .page-title {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }
  }

  .search-card {
    margin-bottom: 20px;

    .search-form {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }

  .table-card {
    .pagination-container {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }
  }

  .batch-actions {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;

    .batch-info {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 8px 16px;
      background: #fff;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }
  }

  .text-danger {
    color: #f56c6c;
  }
}
</style>
