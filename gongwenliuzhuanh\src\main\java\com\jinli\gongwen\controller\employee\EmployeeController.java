package com.jinli.gongwen.controller.employee;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinli.gongwen.common.Result;
import com.jinli.gongwen.dto.document.DocumentQueryDTO;
import com.jinli.gongwen.dto.document.DocumentReceiveDTO;
import com.jinli.gongwen.dto.document.SimpleDocumentDTO;
import com.jinli.gongwen.entity.document.DocDocument;
import com.jinli.gongwen.service.document.DocumentService;
import com.jinli.gongwen.service.document.ReceiveService;
import com.jinli.gongwen.vo.document.DocumentVO;
import com.jinli.gongwen.vo.employee.EmployeeDashboardVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 普通员工控制器
 * 负责基础的公文协助和签收功能
 */
@Api(tags = "普通员工功能")
@RestController
@RequestMapping("/api/employee")
@PreAuthorize("hasRole('EMPLOYEE')")
public class EmployeeController {

    @Autowired
    private DocumentService documentService;

    @Autowired
    private ReceiveService receiveService;

    /**
     * 员工个人工作台
     */
    @ApiOperation("员工个人工作台")
    @GetMapping("/dashboard")
    public Result<EmployeeDashboardVO> getDashboard() {
        EmployeeDashboardVO dashboard = documentService.getEmployeeDashboard(getCurrentUserId(), getCurrentDeptId());
        return Result.success(dashboard);
    }

    /**
     * 获取待签收公文列表
     */
    @ApiOperation("获取待签收公文列表")
    @GetMapping("/pendingReceive")
    public Result<IPage<DocumentVO>> getPendingReceiveDocuments(DocumentQueryDTO queryDTO) {
        Page<DocDocument> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        QueryWrapper<DocDocument> queryWrapper = new QueryWrapper<>();
        
        // 查询发送给当前部门且未签收的公文
        queryWrapper.eq("doc_status", "OFFICE_SEND");
        queryWrapper.eq("target_dept_id", getCurrentDeptId());
        queryWrapper.eq("receive_status", "PENDING");
        
        // 构建查询条件
        if (queryDTO.getDocTitle() != null && !queryDTO.getDocTitle().trim().isEmpty()) {
            queryWrapper.like("doc_title", queryDTO.getDocTitle().trim());
        }
        if (queryDTO.getDocType() != null && !queryDTO.getDocType().trim().isEmpty()) {
            queryWrapper.eq("doc_type", queryDTO.getDocType());
        }
        
        queryWrapper.orderByDesc("create_time");
        
        IPage<DocumentVO> result = documentService.getDocumentList(page, queryWrapper);
        return Result.success(result);
    }

    /**
     * 获取已签收公文列表
     */
    @ApiOperation("获取已签收公文列表")
    @GetMapping("/receivedDocuments")
    public Result<IPage<DocumentVO>> getReceivedDocuments(DocumentQueryDTO queryDTO) {
        Page<DocDocument> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        QueryWrapper<DocDocument> queryWrapper = new QueryWrapper<>();
        
        // 查询当前用户已签收的公文
        queryWrapper.exists("SELECT 1 FROM doc_receive_record drr WHERE drr.doc_id = doc_document.doc_id " +
                           "AND drr.receive_user_id = '" + getCurrentUserId() + "' " +
                           "AND drr.receive_status = 'RECEIVED'");
        
        // 构建查询条件
        if (queryDTO.getDocTitle() != null && !queryDTO.getDocTitle().trim().isEmpty()) {
            queryWrapper.like("doc_title", queryDTO.getDocTitle().trim());
        }
        if (queryDTO.getDocType() != null && !queryDTO.getDocType().trim().isEmpty()) {
            queryWrapper.eq("doc_type", queryDTO.getDocType());
        }
        
        queryWrapper.orderByDesc("update_time");
        
        IPage<DocumentVO> result = documentService.getDocumentList(page, queryWrapper);
        return Result.success(result);
    }

    /**
     * 协助创建简单公文草稿
     */
    @ApiOperation("协助创建简单公文草稿")
    @PostMapping("/assistDraft")
    public Result<String> assistCreateDraft(@Valid @RequestBody SimpleDocumentDTO simpleDocumentDTO) {
        simpleDocumentDTO.setCreateUserId(getCurrentUserId());
        simpleDocumentDTO.setCreateDeptId(getCurrentDeptId());
        simpleDocumentDTO.setAssistantUserId(getCurrentUserId());
        String docId = documentService.createSimpleDraft(simpleDocumentDTO);
        return Result.success(docId);
    }

    /**
     * 签收公文
     */
    @ApiOperation("签收公文")
    @PostMapping("/receive/{docId}")
    public Result<Void> receiveDocument(@PathVariable String docId, @Valid @RequestBody DocumentReceiveDTO receiveDTO) {
        receiveDTO.setDocId(docId);
        receiveDTO.setReceiveUserId(getCurrentUserId());
        receiveDTO.setReceiveDeptId(getCurrentDeptId());
        receiveService.receiveDocument(receiveDTO);
        return Result.success();
    }

    /**
     * 获取公文详情（只读）
     */
    @ApiOperation("获取公文详情")
    @GetMapping("/document/{docId}")
    public Result<DocumentVO> getDocument(@PathVariable String docId) {
        DocumentVO documentVO = documentService.getDocumentById(docId);
        // 员工只能查看基础信息，隐藏敏感信息
        documentVO = filterSensitiveInfo(documentVO);
        return Result.success(documentVO);
    }

    /**
     * 获取个人待办事项
     */
    @ApiOperation("获取个人待办事项")
    @GetMapping("/todoList")
    public Result<List<Object>> getTodoList() {
        List<Object> todoList = documentService.getEmployeeTodoList(getCurrentUserId());
        return Result.success(todoList);
    }

    /**
     * 获取公文通知
     */
    @ApiOperation("获取公文通知")
    @GetMapping("/notifications")
    public Result<IPage<Object>> getNotifications(DocumentQueryDTO queryDTO) {
        Page<Object> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        IPage<Object> result = documentService.getEmployeeNotifications(page, getCurrentUserId());
        return Result.success(result);
    }

    /**
     * 标记通知为已读
     */
    @ApiOperation("标记通知为已读")
    @PutMapping("/notification/{notificationId}/read")
    public Result<Void> markNotificationAsRead(@PathVariable String notificationId) {
        documentService.markNotificationAsRead(notificationId, getCurrentUserId());
        return Result.success();
    }

    /**
     * 获取简化的公文模板
     */
    @ApiOperation("获取简化的公文模板")
    @GetMapping("/simpleTemplates")
    public Result<List<Object>> getSimpleTemplates() {
        List<Object> templates = documentService.getSimpleDocumentTemplates();
        return Result.success(templates);
    }

    /**
     * 获取个人工作统计
     */
    @ApiOperation("获取个人工作统计")
    @GetMapping("/workStatistics")
    public Result<Object> getWorkStatistics() {
        Object statistics = documentService.getEmployeeWorkStatistics(getCurrentUserId());
        return Result.success(statistics);
    }

    /**
     * 提交协助意见
     */
    @ApiOperation("提交协助意见")
    @PostMapping("/submitAssistance/{docId}")
    public Result<Void> submitAssistance(@PathVariable String docId, @RequestParam String assistance) {
        documentService.submitEmployeeAssistance(docId, getCurrentUserId(), assistance);
        return Result.success();
    }

    /**
     * 过滤敏感信息（员工权限限制）
     */
    private DocumentVO filterSensitiveInfo(DocumentVO documentVO) {
        // 员工只能看到基础信息，隐藏审核意见、流转记录等敏感信息
        if (documentVO != null) {
            // 可以根据需要隐藏某些字段
            // documentVO.setReviewOpinion(null);
            // documentVO.setApprovalOpinion(null);
        }
        return documentVO;
    }

    /**
     * 获取当前用户ID（从安全上下文中获取）
     */
    private String getCurrentUserId() {
        // 从Spring Security上下文中获取当前用户ID
        // 这里简化处理，实际应该从SecurityContextHolder中获取
        return "USER010"; // 示例：生产部员工
    }

    /**
     * 获取当前部门ID（从安全上下文中获取）
     */
    private String getCurrentDeptId() {
        // 从Spring Security上下文中获取当前用户的部门ID
        // 这里简化处理，实际应该从SecurityContextHolder中获取
        return "DEPT003"; // 示例：生产部
    }
}
