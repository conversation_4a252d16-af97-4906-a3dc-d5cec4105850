package com.jinli.gongwen.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinli.gongwen.common.Constants;
import com.jinli.gongwen.common.ResultCode;
import com.jinli.gongwen.entity.SysUser;
import com.jinli.gongwen.entity.SysUserRole;
import com.jinli.gongwen.mapper.SysUserMapper;
import com.jinli.gongwen.mapper.SysUserRoleMapper;
import com.jinli.gongwen.service.SysUserService;
import com.jinli.gongwen.util.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户信息表 服务实现类
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService {

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private SysUserRoleMapper userRoleMapper;

    @Autowired
    private JwtUtil jwtUtil;

    @Override
    public String login(String username, String password, String loginIp) {
        // 查询用户信息
        SysUser user = userMapper.selectUserByUsername(username);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 检查用户状态
        if (!Constants.USER_STATUS_NORMAL.equals(user.getStatus())) {
            throw new RuntimeException("用户已被禁用");
        }

        // 验证密码 - 直接比较明文密码
        if (!password.equals(user.getPassword())) {
            throw new RuntimeException("用户名或密码错误");
        }

        // 更新登录信息
        userMapper.updateUserLoginInfo(user.getUserId(), loginIp);

        // 生成JWT Token
        Map<String, Object> claims = new HashMap<>();
        claims.put(Constants.JWT_CLAIMS_USER_ID, user.getUserId());
        claims.put(Constants.JWT_CLAIMS_USERNAME, user.getUsername());
        claims.put(Constants.JWT_CLAIMS_ROLE_KEY, user.getRoleKey());

        return jwtUtil.generateToken(claims);
    }

    @Override
    public SysUser getUserByUsername(String username) {
        return userMapper.selectUserByUsername(username);
    }

    @Override
    public SysUser getUserDetailById(String userId) {
        return userMapper.selectUserDetailById(userId);
    }

    @Override
    public IPage<SysUser> getUserPage(Integer pageNum, Integer pageSize, String username, 
                                      String realName, String departmentId, String status) {
        Page<SysUser> page = new Page<>(pageNum, pageSize);
        return userMapper.selectUserPage(page, username, realName, departmentId, status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createUser(SysUser user, String roleId) {
        // 检查用户名是否唯一
        if (!checkUsernameUnique(user.getUsername(), null)) {
            throw new RuntimeException("用户名已存在");
        }

        // 设置默认值
        user.setStatus(Constants.USER_STATUS_NORMAL);
        user.setCreateTime(new Date());
        user.setUpdateTime(new Date());
        
        // 设置默认密码（明文存储）
        if (StrUtil.isBlank(user.getPassword())) {
            user.setPassword(Constants.DEFAULT_PASSWORD);
        }
        // 密码直接存储，不进行加密

        // 保存用户
        boolean result = save(user);
        
        // 分配角色
        if (result && StrUtil.isNotBlank(roleId)) {
            SysUserRole userRole = new SysUserRole();
            userRole.setUserId(user.getUserId());
            userRole.setRoleId(roleId);
            userRoleMapper.insert(userRole);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUser(SysUser user, String roleId) {
        // 检查用户名是否唯一
        if (!checkUsernameUnique(user.getUsername(), user.getUserId())) {
            throw new RuntimeException("用户名已存在");
        }

        user.setUpdateTime(new Date());
        boolean result = updateById(user);

        // 更新角色
        if (result && StrUtil.isNotBlank(roleId)) {
            // 删除原有角色
            QueryWrapper<SysUserRole> wrapper = new QueryWrapper<>();
            wrapper.eq("USER_ID", user.getUserId());
            userRoleMapper.delete(wrapper);

            // 分配新角色
            SysUserRole userRole = new SysUserRole();
            userRole.setUserId(user.getUserId());
            userRole.setRoleId(roleId);
            userRoleMapper.insert(userRole);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteUser(String userId) {
        // 删除用户角色关联
        QueryWrapper<SysUserRole> wrapper = new QueryWrapper<>();
        wrapper.eq("USER_ID", userId);
        userRoleMapper.delete(wrapper);

        // 删除用户
        return removeById(userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteUsers(List<String> userIds) {
        // 删除用户角色关联
        QueryWrapper<SysUserRole> wrapper = new QueryWrapper<>();
        wrapper.in("USER_ID", userIds);
        userRoleMapper.delete(wrapper);

        // 删除用户
        return removeByIds(userIds);
    }

    @Override
    public boolean resetPassword(String userId, String newPassword) {
        // 直接存储明文密码
        return userMapper.resetUserPassword(userId, newPassword) > 0;
    }

    @Override
    public boolean changePassword(String userId, String oldPassword, String newPassword) {
        SysUser user = getById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 验证旧密码 - 直接比较明文
        if (!oldPassword.equals(user.getPassword())) {
            throw new RuntimeException("原密码错误");
        }

        // 更新密码 - 直接存储明文
        return userMapper.resetUserPassword(userId, newPassword) > 0;
    }

    @Override
    public boolean changeUserStatus(String userId, String status) {
        SysUser user = new SysUser();
        user.setUserId(userId);
        user.setStatus(status);
        user.setUpdateTime(new Date());
        return updateById(user);
    }

    @Override
    public boolean checkUsernameUnique(String username, String userId) {
        return userMapper.checkUsernameUnique(username, userId) == 0;
    }

    @Override
    public List<SysUser> getUsersByDepartmentId(String departmentId) {
        return userMapper.selectUsersByDepartmentId(departmentId);
    }

    @Override
    public List<SysUser> getUsersByRoleKey(String roleKey) {
        return userMapper.selectUsersByRoleKey(roleKey);
    }

    @Override
    public List<SysUser> getAllActiveUsers() {
        return userMapper.selectAllActiveUsers();
    }

    @Override
    public boolean updateProfile(SysUser user) {
        user.setUpdateTime(new Date());
        return updateById(user);
    }

    @Override
    public Object getUserStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        // 总用户数
        long totalUsers = count();
        statistics.put("totalUsers", totalUsers);
        
        // 正常用户数
        QueryWrapper<SysUser> normalWrapper = new QueryWrapper<>();
        normalWrapper.eq("STATUS", Constants.USER_STATUS_NORMAL);
        long normalUsers = count(normalWrapper);
        statistics.put("normalUsers", normalUsers);
        
        // 禁用用户数
        QueryWrapper<SysUser> disabledWrapper = new QueryWrapper<>();
        disabledWrapper.eq("STATUS", Constants.USER_STATUS_DISABLED);
        long disabledUsers = count(disabledWrapper);
        statistics.put("disabledUsers", disabledUsers);
        
        return statistics;
    }
}
