<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinli.gongwen.mapper.SysUserMapper">

    <!-- 用户信息结果映射 -->
    <resultMap id="UserResultMap" type="com.jinli.gongwen.entity.SysUser">
        <id column="USER_ID" property="userId"/>
        <result column="USERNAME" property="username"/>
        <result column="PASSWORD" property="password"/>
        <result column="REAL_NAME" property="realName"/>
        <result column="EMAIL" property="email"/>
        <result column="PHONE" property="phone"/>
        <result column="DEPARTMENT_ID" property="departmentId"/>
        <result column="STATUS" property="status"/>
        <result column="LOGIN_IP" property="loginIp"/>
        <result column="LOGIN_DATE" property="loginDate"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="UPDATE_TIME" property="updateTime"/>
        <result column="REMARK" property="remark"/>
        <result column="DEPT_NAME" property="departmentName"/>
        <result column="ROLE_NAME" property="roleName"/>
        <result column="ROLE_KEY" property="roleKey"/>
    </resultMap>

    <!-- 根据用户名查询用户信息（包含角色和部门信息） -->
    <select id="selectUserByUsername" resultMap="UserResultMap">
        SELECT u.*, d.DEPT_NAME, r.ROLE_NAME, r.ROLE_KEY
        FROM SYS_USER u
        LEFT JOIN SYS_DEPARTMENT d ON u.DEPARTMENT_ID = d.DEPT_ID
        LEFT JOIN SYS_USER_ROLE ur ON u.USER_ID = ur.USER_ID
        LEFT JOIN SYS_ROLE r ON ur.ROLE_ID = r.ROLE_ID
        WHERE u.USERNAME = #{username}
    </select>

    <!-- 根据用户ID查询用户详细信息（包含角色和部门信息） -->
    <select id="selectUserDetailById" resultMap="UserResultMap">
        SELECT u.*, d.DEPT_NAME, r.ROLE_NAME, r.ROLE_KEY
        FROM SYS_USER u
        LEFT JOIN SYS_DEPARTMENT d ON u.DEPARTMENT_ID = d.DEPT_ID
        LEFT JOIN SYS_USER_ROLE ur ON u.USER_ID = ur.USER_ID
        LEFT JOIN SYS_ROLE r ON ur.ROLE_ID = r.ROLE_ID
        WHERE u.USER_ID = #{userId}
    </select>

    <!-- 分页查询用户列表（包含部门和角色信息） -->
    <select id="selectUserPage" resultMap="UserResultMap">
        SELECT u.*, d.DEPT_NAME, r.ROLE_NAME, r.ROLE_KEY
        FROM SYS_USER u
        LEFT JOIN SYS_DEPARTMENT d ON u.DEPARTMENT_ID = d.DEPT_ID
        LEFT JOIN SYS_USER_ROLE ur ON u.USER_ID = ur.USER_ID
        LEFT JOIN SYS_ROLE r ON ur.ROLE_ID = r.ROLE_ID
        <where>
            <if test="username != null and username != ''">
                AND u.USERNAME LIKE CONCAT('%', #{username}, '%')
            </if>
            <if test="realName != null and realName != ''">
                AND u.REAL_NAME LIKE CONCAT('%', #{realName}, '%')
            </if>
            <if test="departmentId != null and departmentId != ''">
                AND u.DEPARTMENT_ID = #{departmentId}
            </if>
            <if test="status != null and status != ''">
                AND u.STATUS = #{status}
            </if>
        </where>
        ORDER BY u.CREATE_TIME DESC
    </select>

    <!-- 根据部门ID查询用户列表 -->
    <select id="selectUsersByDepartmentId" resultMap="UserResultMap">
        SELECT u.*, d.DEPT_NAME, r.ROLE_NAME, r.ROLE_KEY
        FROM SYS_USER u
        LEFT JOIN SYS_DEPARTMENT d ON u.DEPARTMENT_ID = d.DEPT_ID
        LEFT JOIN SYS_USER_ROLE ur ON u.USER_ID = ur.USER_ID
        LEFT JOIN SYS_ROLE r ON ur.ROLE_ID = r.ROLE_ID
        WHERE u.DEPARTMENT_ID = #{departmentId}
        AND u.STATUS = '1'
        ORDER BY u.REAL_NAME
    </select>

    <!-- 根据角色键查询用户列表 -->
    <select id="selectUsersByRoleKey" resultMap="UserResultMap">
        SELECT u.*, d.DEPT_NAME, r.ROLE_NAME, r.ROLE_KEY
        FROM SYS_USER u
        LEFT JOIN SYS_DEPARTMENT d ON u.DEPARTMENT_ID = d.DEPT_ID
        INNER JOIN SYS_USER_ROLE ur ON u.USER_ID = ur.USER_ID
        INNER JOIN SYS_ROLE r ON ur.ROLE_ID = r.ROLE_ID
        WHERE r.ROLE_KEY = #{roleKey}
        AND u.STATUS = '1'
        ORDER BY u.REAL_NAME
    </select>

    <!-- 检查用户名是否存在 -->
    <select id="checkUsernameUnique" resultType="int">
        SELECT COUNT(1)
        FROM SYS_USER
        WHERE USERNAME = #{username}
        <if test="userId != null and userId != ''">
            AND USER_ID != #{userId}
        </if>
    </select>

    <!-- 更新用户登录信息 -->
    <update id="updateUserLoginInfo">
        UPDATE SYS_USER
        SET LOGIN_IP = #{loginIp}, LOGIN_DATE = SYSDATE
        WHERE USER_ID = #{userId}
    </update>

    <!-- 重置用户密码 -->
    <update id="resetUserPassword">
        UPDATE SYS_USER
        SET PASSWORD = #{password}, UPDATE_TIME = SYSDATE
        WHERE USER_ID = #{userId}
    </update>

    <!-- 根据用户ID列表查询用户信息 -->
    <select id="selectUsersByIds" resultMap="UserResultMap">
        SELECT u.*, d.DEPT_NAME, r.ROLE_NAME, r.ROLE_KEY
        FROM SYS_USER u
        LEFT JOIN SYS_DEPARTMENT d ON u.DEPARTMENT_ID = d.DEPT_ID
        LEFT JOIN SYS_USER_ROLE ur ON u.USER_ID = ur.USER_ID
        LEFT JOIN SYS_ROLE r ON ur.ROLE_ID = r.ROLE_ID
        WHERE u.USER_ID IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>

    <!-- 统计部门下的用户数量 -->
    <select id="countUsersByDepartmentId" resultType="int">
        SELECT COUNT(1)
        FROM SYS_USER
        WHERE DEPARTMENT_ID = #{departmentId}
        AND STATUS = '1'
    </select>

    <!-- 查询所有正常状态的用户 -->
    <select id="selectAllActiveUsers" resultMap="UserResultMap">
        SELECT u.*, d.DEPT_NAME, r.ROLE_NAME, r.ROLE_KEY
        FROM SYS_USER u
        LEFT JOIN SYS_DEPARTMENT d ON u.DEPARTMENT_ID = d.DEPT_ID
        LEFT JOIN SYS_USER_ROLE ur ON u.USER_ID = ur.USER_ID
        LEFT JOIN SYS_ROLE r ON ur.ROLE_ID = r.ROLE_ID
        WHERE u.STATUS = '1'
        ORDER BY d.ORDER_NUM, u.REAL_NAME
    </select>

</mapper>
