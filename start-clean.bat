@echo off
echo === 清理并启动应用 ===

echo 1. 停止所有Java进程...
taskkill /f /im java.exe 2>nul
timeout /t 3 /nobreak >nul

echo 2. 清理编译缓存...
cd /d "D:\Program Files (x86)\Javaxiangmu\gongwenliuzhuan\gongwenliuzhuanh"
if exist target rmdir /s /q target

echo 3. 重新编译...
mvn clean compile -DskipTests

echo 4. 启动应用...
start "Gongwen Backend" mvn spring-boot:run

echo 5. 等待应用启动...
echo 应用正在启动中，请等待约30秒...
timeout /t 10 /nobreak >nul

echo === 启动完成 ===
echo 请等待应用完全启动后测试前端
pause
