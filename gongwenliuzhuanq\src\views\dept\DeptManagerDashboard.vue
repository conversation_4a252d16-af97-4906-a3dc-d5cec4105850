<template>
  <div class="dept-manager-dashboard">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon pending-icon">
              <i class="el-icon-message"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ dashboardData.pendingReceiveCount || 0 }}</div>
              <div class="stats-label">待签收</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon draft-icon">
              <i class="el-icon-edit-outline"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ dashboardData.draftCount || 0 }}</div>
              <div class="stats-label">草稿箱</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon processing-icon">
              <i class="el-icon-loading"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ dashboardData.processingCount || 0 }}</div>
              <div class="stats-label">流转中</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon completed-icon">
              <i class="el-icon-circle-check"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ dashboardData.completedCount || 0 }}</div>
              <div class="stats-label">已完成</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="chart-card">
          <div slot="header" class="card-header">
            <span>公文类型分布</span>
          </div>
          <div id="docTypeChart" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="chart-card">
          <div slot="header" class="card-header">
            <span>本周工作统计</span>
          </div>
          <div id="weeklyWorkChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快捷操作和效率统计 -->
    <el-row :gutter="20" class="actions-row">
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="action-card">
          <div slot="header" class="card-header">
            <span>快捷操作</span>
          </div>
          <div class="action-buttons">
            <el-button type="primary" @click="goToDocumentDraft">
              <i class="el-icon-edit"></i> 公文拟制
            </el-button>
            <el-button type="success" @click="goToPendingReceive">
              <i class="el-icon-message"></i> 待签收
            </el-button>
            <el-button type="warning" @click="goToDraftList">
              <i class="el-icon-document"></i> 草稿箱
            </el-button>
            <el-button type="info" @click="goToDocumentList">
              <i class="el-icon-folder"></i> 公文浏览
            </el-button>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="efficiency-card">
          <div slot="header" class="card-header">
            <span>部门工作效率</span>
          </div>
          <div class="efficiency-content" v-if="dashboardData.efficiency">
            <div class="efficiency-item">
              <div class="efficiency-label">平均处理时间</div>
              <div class="efficiency-value">
                {{ dashboardData.efficiency.avgProcessTime || 0 }} 小时
              </div>
            </div>
            <div class="efficiency-item">
              <div class="efficiency-label">及时完成率</div>
              <div class="efficiency-value">
                {{ (dashboardData.efficiency.timelyCompletionRate || 0) * 100 }}%
              </div>
            </div>
            <div class="efficiency-item">
              <div class="efficiency-label">本月排名</div>
              <div class="efficiency-value">
                第 {{ dashboardData.efficiency.monthlyRanking || 0 }} 名
              </div>
            </div>
            <div class="efficiency-item">
              <div class="efficiency-label">效率趋势</div>
              <div class="efficiency-value">
                <el-tag :type="getTrendType(dashboardData.efficiency.efficiencyTrend)">
                  {{ getTrendLabel(dashboardData.efficiency.efficiencyTrend) }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最近公文 -->
    <el-row :gutter="20" class="recent-row">
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="recent-card">
          <div slot="header" class="card-header">
            <span>最近创建</span>
            <el-button type="text" @click="goToCreatedList">查看更多</el-button>
          </div>
          <div class="recent-list">
            <div
              v-for="doc in dashboardData.recentCreated"
              :key="doc.docId"
              class="recent-item"
              @click="viewDocument(doc)"
            >
              <div class="recent-title">{{ doc.docTitle }}</div>
              <div class="recent-meta">
                <el-tag size="mini">{{ getDocTypeLabel(doc.docType) }}</el-tag>
                <span class="recent-time">{{ doc.createTime }}</span>
              </div>
              <div class="recent-status">
                <el-tag :type="getStatusType(doc.currentStatus)" size="mini">
                  {{ getStatusLabel(doc.currentStatus) }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="recent-card">
          <div slot="header" class="card-header">
            <span>最近签收</span>
            <el-button type="text" @click="goToReceivedList">查看更多</el-button>
          </div>
          <div class="recent-list">
            <div
              v-for="doc in dashboardData.recentReceived"
              :key="doc.docId"
              class="recent-item"
              @click="viewDocument(doc)"
            >
              <div class="recent-title">{{ doc.docTitle }}</div>
              <div class="recent-meta">
                <el-tag size="mini">{{ getDocTypeLabel(doc.docType) }}</el-tag>
                <span class="recent-time">{{ doc.processTime }}</span>
              </div>
              <div class="recent-status">
                <el-tag :type="getStatusType(doc.currentStatus)" size="mini">
                  {{ getStatusLabel(doc.currentStatus) }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getDeptManagerDashboard } from '@/api/dept/dashboard'

export default {
  name: 'DeptManagerDashboard',
  data() {
    return {
      dashboardData: {
        pendingReceiveCount: 0,
        draftCount: 0,
        processingCount: 0,
        completedCount: 0,
        docTypeStats: {},
        weeklyWork: [],
        recentCreated: [],
        recentReceived: [],
        efficiency: null
      },
      docTypeChart: null,
      weeklyWorkChart: null
    }
  },
  mounted() {
    this.initData()
    this.initCharts()
    // 定时刷新数据
    this.timer = setInterval(() => {
      this.refreshData()
    }, 60000) // 每分钟刷新一次
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
    if (this.docTypeChart) {
      this.docTypeChart.dispose()
    }
    if (this.weeklyWorkChart) {
      this.weeklyWorkChart.dispose()
    }
  },
  methods: {
    // 初始化数据
    async initData() {
      await this.getDashboardData()
    },

    // 获取仪表板数据
    async getDashboardData() {
      try {
        const response = await getDeptManagerDashboard()
        this.dashboardData = response.data
        this.updateCharts()
      } catch (error) {
        this.$message.error('获取仪表板数据失败')
      }
    },

    // 刷新数据
    refreshData() {
      this.getDashboardData()
    },

    // 初始化图表
    initCharts() {
      this.initDocTypeChart()
      this.initWeeklyWorkChart()
    },

    // 初始化公文类型图表
    initDocTypeChart() {
      const chartDom = document.getElementById('docTypeChart')
      this.docTypeChart = echarts.init(chartDom)
    },

    // 初始化周工作图表
    initWeeklyWorkChart() {
      const chartDom = document.getElementById('weeklyWorkChart')
      this.weeklyWorkChart = echarts.init(chartDom)
    },

    // 更新图表
    updateCharts() {
      this.updateDocTypeChart()
      this.updateWeeklyWorkChart()
    },

    // 更新公文类型图表
    updateDocTypeChart() {
      if (!this.docTypeChart) return
      
      const data = Object.entries(this.dashboardData.docTypeStats || {}).map(([key, value]) => ({
        name: this.getDocTypeLabel(key),
        value: value
      }))
      
      const option = {
        tooltip: {
          trigger: 'item'
        },
        series: [
          {
            name: '公文类型',
            type: 'pie',
            radius: '60%',
            data: data,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      this.docTypeChart.setOption(option)
    },

    // 更新周工作图表
    updateWeeklyWorkChart() {
      if (!this.weeklyWorkChart) return
      
      const weeklyData = this.dashboardData.weeklyWork || []
      const dates = weeklyData.map(item => item.date)
      const created = weeklyData.map(item => item.created)
      const received = weeklyData.map(item => item.received)
      const processed = weeklyData.map(item => item.processed)
      
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['创建', '签收', '处理']
        },
        xAxis: {
          type: 'category',
          data: dates
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '创建',
            type: 'bar',
            data: created,
            itemStyle: { color: '#409EFF' }
          },
          {
            name: '签收',
            type: 'bar',
            data: received,
            itemStyle: { color: '#67C23A' }
          },
          {
            name: '处理',
            type: 'bar',
            data: processed,
            itemStyle: { color: '#E6A23C' }
          }
        ]
      }
      this.weeklyWorkChart.setOption(option)
    },

    // 获取公文类型标签
    getDocTypeLabel(type) {
      const typeMap = {
        'NOTICE': '通知',
        'REPORT': '报告',
        'REQUEST': '请示',
        'DECISION': '决定',
        'OPINION': '意见',
        'LETTER': '函',
        'MINUTES': '会议纪要'
      }
      return typeMap[type] || type
    },

    // 获取状态标签
    getStatusLabel(status) {
      const statusMap = {
        'DRAFT': '草稿',
        'OFFICE_EDIT': '办公室修改',
        'VICE_REVIEW': '副厂长审核',
        'DIRECTOR_APPROVE': '厂长审签',
        'OFFICE_SEND': '办公室发送',
        'DEPT_RECEIVE': '部门签收',
        'COMPLETED': '已完成',
        'REJECTED': '已退回'
      }
      return statusMap[status] || status
    },

    // 获取状态类型
    getStatusType(status) {
      const typeMap = {
        'DRAFT': 'info',
        'OFFICE_EDIT': 'warning',
        'VICE_REVIEW': 'primary',
        'DIRECTOR_APPROVE': 'primary',
        'OFFICE_SEND': 'warning',
        'DEPT_RECEIVE': 'success',
        'COMPLETED': 'success',
        'REJECTED': 'danger'
      }
      return typeMap[status] || 'info'
    },

    // 获取趋势类型
    getTrendType(trend) {
      const typeMap = {
        'UP': 'success',
        'DOWN': 'danger',
        'STABLE': 'info'
      }
      return typeMap[trend] || 'info'
    },

    // 获取趋势标签
    getTrendLabel(trend) {
      const labelMap = {
        'UP': '上升',
        'DOWN': '下降',
        'STABLE': '稳定'
      }
      return labelMap[trend] || '稳定'
    },

    // 导航到公文拟制
    goToDocumentDraft() {
      this.$router.push('/dept/draft')
    },

    // 导航到待签收
    goToPendingReceive() {
      this.$router.push('/dept/pending-receive')
    },

    // 导航到草稿箱
    goToDraftList() {
      this.$router.push('/dept/drafts')
    },

    // 导航到公文浏览
    goToDocumentList() {
      this.$router.push('/dept/documents')
    },

    // 导航到已创建列表
    goToCreatedList() {
      this.$router.push('/dept/created')
    },

    // 导航到已签收列表
    goToReceivedList() {
      this.$router.push('/dept/received')
    },

    // 查看公文
    viewDocument(doc) {
      this.$router.push(`/dept/document/${doc.docId}`)
    }
  }
}
</script>

<style scoped>
.dept-manager-dashboard {
  padding: 20px;
}

.stats-row,
.charts-row,
.actions-row,
.recent-row {
  margin-bottom: 20px;
}

.stats-card {
  height: 120px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: white;
}

.pending-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.draft-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.processing-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.completed-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.chart-card {
  height: 400px;
}

.chart-container {
  height: 320px;
}

.action-card,
.efficiency-card {
  height: 300px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.action-buttons .el-button {
  flex: 1;
  min-width: 120px;
}

.efficiency-content {
  padding: 10px 0;
}

.efficiency-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.efficiency-label {
  font-size: 14px;
  color: #606266;
}

.efficiency-value {
  font-weight: bold;
  color: #303133;
}

.recent-card {
  height: 400px;
}

.recent-list {
  max-height: 320px;
  overflow-y: auto;
}

.recent-item {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s;
}

.recent-item:hover {
  background-color: #f8f9fa;
}

.recent-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.recent-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 5px;
}

.recent-time {
  font-size: 12px;
  color: #909399;
}

.recent-status {
  text-align: right;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .dept-manager-dashboard {
    padding: 10px;
  }
  
  .stats-card {
    margin-bottom: 10px;
  }
  
  .chart-card {
    height: 350px;
    margin-bottom: 10px;
  }
  
  .chart-container {
    height: 270px;
  }
}

@media (max-width: 800px) {
  .stats-content {
    flex-direction: column;
    text-align: center;
  }
  
  .stats-icon {
    margin-right: 0;
    margin-bottom: 10px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-buttons .el-button {
    width: 100%;
  }
  
  .efficiency-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .efficiency-value {
    margin-top: 5px;
  }
}
</style>
