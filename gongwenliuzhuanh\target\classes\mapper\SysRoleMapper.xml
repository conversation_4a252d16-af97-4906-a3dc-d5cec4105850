<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinli.gongwen.mapper.SysRoleMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.jinli.gongwen.entity.SysRole">
        <id column="ROLE_ID" property="roleId" />
        <result column="ROLE_NAME" property="roleName" />
        <result column="ROLE_KEY" property="roleKey" />
        <result column="ROLE_SORT" property="roleSort" />
        <result column="STATUS" property="status" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="REMARK" property="remark" />
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        ROLE_ID, ROLE_NAME, ROLE_KEY, ROLE_SORT, STATUS, CREATE_TIME, UPDATE_TIME, REMARK
    </sql>

    <!-- 分页查询角色列表 -->
    <select id="selectRolePage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM SYS_ROLE
        <where>
            <if test="roleName != null and roleName != ''">
                AND ROLE_NAME LIKE CONCAT('%', #{roleName}, '%')
            </if>
            <if test="roleKey != null and roleKey != ''">
                AND ROLE_KEY LIKE CONCAT('%', #{roleKey}, '%')
            </if>
            <if test="status != null and status != ''">
                AND STATUS = #{status}
            </if>
        </where>
        ORDER BY ROLE_SORT ASC, CREATE_TIME DESC
    </select>

    <!-- 根据角色ID查询角色详细信息 -->
    <select id="selectRoleDetailById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM SYS_ROLE
        WHERE ROLE_ID = #{roleId}
    </select>

    <!-- 检查角色名称是否唯一 -->
    <select id="checkRoleNameUnique" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM SYS_ROLE
        WHERE ROLE_NAME = #{roleName}
        <if test="roleId != null and roleId != ''">
            AND ROLE_ID != #{roleId}
        </if>
    </select>

    <!-- 检查角色标识是否唯一 -->
    <select id="checkRoleKeyUnique" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM SYS_ROLE
        WHERE ROLE_KEY = #{roleKey}
        <if test="roleId != null and roleId != ''">
            AND ROLE_ID != #{roleId}
        </if>
    </select>

    <!-- 查询所有启用的角色 -->
    <select id="selectAllEnabledRoles" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM SYS_ROLE
        WHERE STATUS = '1'
        ORDER BY ROLE_SORT ASC, CREATE_TIME DESC
    </select>

    <!-- 根据用户ID查询角色列表 -->
    <select id="selectRolesByUserId" resultMap="BaseResultMap">
        SELECT r.<include refid="Base_Column_List" />
        FROM SYS_ROLE r
        INNER JOIN SYS_USER_ROLE ur ON r.ROLE_ID = ur.ROLE_ID
        WHERE ur.USER_ID = #{userId}
        AND r.STATUS = '1'
        ORDER BY r.ROLE_SORT ASC
    </select>

    <!-- 统计角色下的用户数量 -->
    <select id="countUsersByRoleId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM SYS_USER_ROLE ur
        INNER JOIN SYS_USER u ON ur.USER_ID = u.USER_ID
        WHERE ur.ROLE_ID = #{roleId}
        AND u.STATUS = '1'
    </select>

</mapper>
