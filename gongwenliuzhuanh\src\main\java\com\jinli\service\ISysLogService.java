package com.jinli.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jinli.entity.SysLog;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 系统日志服务接口
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
public interface ISysLogService extends IService<SysLog> {

    /**
     * 分页查询系统日志
     *
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param username 用户名
     * @param module 操作模块
     * @param operationType 操作类型
     * @param status 操作状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分页结果
     */
    Map<String, Object> selectLogPage(Integer pageNum, Integer pageSize, String username, 
                                     String module, String operationType, String status, 
                                     LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 记录系统日志
     *
     * @param username 用户名
     * @param realName 真实姓名
     * @param module 操作模块
     * @param operationType 操作类型
     * @param description 操作描述
     * @param requestUrl 请求URL
     * @param requestMethod 请求方法
     * @param requestParams 请求参数
     * @param responseResult 响应结果
     * @param ip IP地址
     * @param userAgent 用户代理
     * @param status 操作状态
     * @param errorMsg 错误信息
     * @param responseTime 响应时间
     */
    void saveLog(String username, String realName, String module, String operationType, 
                String description, String requestUrl, String requestMethod, 
                String requestParams, String responseResult, String ip, String userAgent, 
                String status, String errorMsg, Long responseTime);

    /**
     * 清空所有日志
     *
     * @return 是否成功
     */
    boolean clearAllLogs();

    /**
     * 导出日志
     *
     * @param username 用户名
     * @param module 操作模块
     * @param operationType 操作类型
     * @param status 操作状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return Excel文件字节数组
     */
    byte[] exportLogs(String username, String module, String operationType, String status, 
                     LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取日志统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getLogStatistics();

    /**
     * 获取操作模块列表
     *
     * @return 模块列表
     */
    List<Map<String, String>> getLogModules();

    /**
     * 获取操作类型列表
     *
     * @return 操作类型列表
     */
    List<Map<String, String>> getLogOperationTypes();

    /**
     * 记录登录日志
     *
     * @param username 用户名
     * @param realName 真实姓名
     * @param ip IP地址
     * @param userAgent 用户代理
     * @param status 登录状态
     * @param errorMsg 错误信息
     */
    void saveLoginLog(String username, String realName, String ip, String userAgent, 
                     String status, String errorMsg);

    /**
     * 记录操作日志
     *
     * @param username 用户名
     * @param realName 真实姓名
     * @param module 操作模块
     * @param operationType 操作类型
     * @param description 操作描述
     * @param ip IP地址
     * @param status 操作状态
     */
    void saveOperationLog(String username, String realName, String module, String operationType, 
                         String description, String ip, String status);
}
