-- 系统日志表（达梦数据库版本）
-- 检查表是否存在，如果存在则删除（达梦不支持IF NOT EXISTS）
-- DROP TABLE IF EXISTS sys_log;

CREATE TABLE sys_log (
    log_id VARCHAR(64) NOT NULL,
    username VARCHAR(50),
    real_name VARCHAR(50),
    module VARCHAR(50),
    operation_type VARCHAR(50),
    description VARCHAR(500),
    request_url VARCHAR(500),
    request_method VARCHAR(10),
    request_params CLOB,
    response_result CLOB,
    ip VARCHAR(50),
    user_agent VARCHAR(1000),
    status VARCHAR(20),
    error_msg CLOB,
    response_time BIGINT,
    create_time DATETIME DEFAULT SYSDATE,
    PRIMARY KEY (log_id)
);

-- 创建索引以提高查询性能
CREATE INDEX idx_sys_log_username ON sys_log (username);
CREATE INDEX idx_sys_log_module ON sys_log (module);
CREATE INDEX idx_sys_log_operation_type ON sys_log (operation_type);
CREATE INDEX idx_sys_log_status ON sys_log (status);
CREATE INDEX idx_sys_log_create_time ON sys_log (create_time);
CREATE INDEX idx_sys_log_ip ON sys_log (ip);
CREATE INDEX idx_sys_log_composite ON sys_log (username, module, operation_type, status, create_time);

-- 插入测试数据（达梦数据库版本）
INSERT INTO sys_log (log_id, username, real_name, module, operation_type, description, request_url, request_method, request_params, ip, user_agent, status, create_time) VALUES
('LOG001', 'admin', '系统管理员', 'USER', 'LOGIN', '用户登录系统', '/auth/login', 'POST', '{"username":"admin"}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'SUCCESS', SYSDATE - 1/24);

INSERT INTO sys_log (log_id, username, real_name, module, operation_type, description, request_url, request_method, request_params, ip, user_agent, status, create_time) VALUES
('LOG002', 'admin', '系统管理员', 'DOCUMENT', 'SELECT', '查询公文列表', '/system/document/list', 'GET', '{"pageNum":1,"pageSize":10}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'SUCCESS', SYSDATE - 2/24);

INSERT INTO sys_log (log_id, username, real_name, module, operation_type, description, request_url, request_method, request_params, ip, user_agent, status, create_time) VALUES
('LOG003', 'zhangsan', '张三', 'DOCUMENT', 'CREATE', '创建新公文', '/system/document', 'POST', '{"title":"测试公文","content":"测试内容"}', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'SUCCESS', SYSDATE - 3/24);

INSERT INTO sys_log (log_id, username, real_name, module, operation_type, description, request_url, request_method, request_params, ip, user_agent, status, create_time) VALUES
('LOG004', 'lisi', '李四', 'USER', 'UPDATE', '修改用户信息', '/system/user', 'PUT', '{"userId":"123","realName":"李四"}', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'FAIL', SYSDATE - 4/24);

INSERT INTO sys_log (log_id, username, real_name, module, operation_type, description, request_url, request_method, request_params, ip, user_agent, status, create_time) VALUES
('LOG005', 'wangwu', '王五', 'DEPT', 'CREATE', '新增部门', '/system/department', 'POST', '{"deptName":"测试部门","deptCode":"TEST_DEPT"}', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'SUCCESS', SYSDATE - 5/24);

INSERT INTO sys_log (log_id, username, real_name, module, operation_type, description, request_url, request_method, request_params, ip, user_agent, status, create_time) VALUES
('LOG006', 'admin', '系统管理员', 'ROLE', 'UPDATE', '修改角色权限', '/system/role', 'PUT', '{"roleId":"456","permissions":["user:list","user:add"]}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'SUCCESS', SYSDATE - 6/24);

INSERT INTO sys_log (log_id, username, real_name, module, operation_type, description, request_url, request_method, request_params, ip, user_agent, status, create_time) VALUES
('LOG007', 'zhangsan', '张三', 'DOCUMENT', 'DELETE', '删除公文', '/system/document/789', 'DELETE', '{}', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'SUCCESS', SYSDATE - 7/24);

INSERT INTO sys_log (log_id, username, real_name, module, operation_type, description, request_url, request_method, request_params, ip, user_agent, status, create_time) VALUES
('LOG008', 'lisi', '李四', 'SYSTEM', 'UPDATE', '修改系统配置', '/system/config', 'PUT', '{"configKey":"system.title","configValue":"公文流转系统"}', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'SUCCESS', SYSDATE - 8/24);

INSERT INTO sys_log (log_id, username, real_name, module, operation_type, description, request_url, request_method, request_params, ip, user_agent, status, create_time) VALUES
('LOG009', 'wangwu', '王五', 'USER', 'LOGIN', '用户登录系统', '/auth/login', 'POST', '{"username":"wangwu"}', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'SUCCESS', SYSDATE - 9/24);

INSERT INTO sys_log (log_id, username, real_name, module, operation_type, description, request_url, request_method, request_params, ip, user_agent, status, create_time) VALUES
('LOG010', 'admin', '系统管理员', 'DOCUMENT', 'SELECT', '导出公文列表', '/system/document/export', 'GET', '{"startTime":"2025-01-01","endTime":"2025-01-31"}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'SUCCESS', SYSDATE - 10/24);

-- 提交事务
COMMIT;
