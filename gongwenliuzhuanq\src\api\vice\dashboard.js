import request from '@/utils/request'

// 获取副厂长仪表板数据
export function getViceDirectorDashboard() {
  return request({
    url: '/api/vice/dashboard',
    method: 'get'
  })
}

// 获取待审核公文列表
export function getPendingReviewDocuments(params) {
  return request({
    url: '/api/vice/pendingReview',
    method: 'get',
    params
  })
}

// 获取已审核公文列表
export function getReviewedDocuments(params) {
  return request({
    url: '/api/vice/reviewed',
    method: 'get',
    params
  })
}

// 获取分管部门统计
export function getDeptStatistics() {
  return request({
    url: '/api/vice/deptStatistics',
    method: 'get'
  })
}
