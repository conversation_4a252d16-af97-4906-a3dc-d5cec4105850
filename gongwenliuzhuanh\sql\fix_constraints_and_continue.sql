-- =====================================================
-- 修复外键约束问题并继续执行组织架构重构
-- 数据库：达梦数据库
-- 说明：解决删除部门时的外键约束问题
-- =====================================================

-- 1. 首先查看当前用户表中的部门引用情况
SELECT USER_ID, USERNAME, REAL_NAME, DEPARTMENT_ID FROM SYS_USER;

-- 2. 更新所有用户的部门为根部门，避免外键约束
UPDATE SYS_USER SET DEPARTMENT_ID = 'DEPT001' WHERE DEPARTMENT_ID != 'DEPT001';

-- 3. 现在可以安全删除旧部门（保留公司根部门）
DELETE FROM SYS_DEPARTMENT WHERE DEPT_ID != 'DEPT001';

-- 4. 继续执行组织架构重构 - 创建新的角色体系
-- =====================================================

-- 厂长角色
INSERT INTO SYS_ROLE (ROLE_ID, ROLE_NAME, ROLE_KEY, DESCRIPTION, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('ROLE002', '厂长', 'FACTORY_DIRECTOR', '公司最高管理者，负责全面管理', 1, SYSDATE, SYSDATE, '厂长角色，拥有最高决策权');

-- 生产副厂长角色
INSERT INTO SYS_ROLE (ROLE_ID, ROLE_NAME, ROLE_KEY, DESCRIPTION, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('ROLE003', '生产副厂长', 'VICE_DIRECTOR_PROD', '负责生产管理和协调', 1, SYSDATE, SYSDATE, '生产副厂长，负责生产相关事务');

-- 技术副厂长角色
INSERT INTO SYS_ROLE (ROLE_ID, ROLE_NAME, ROLE_KEY, DESCRIPTION, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('ROLE004', '技术副厂长', 'VICE_DIRECTOR_TECH', '负责技术管理和创新', 1, SYSDATE, SYSDATE, '技术副厂长，负责技术相关事务');

-- 安全副厂长角色
INSERT INTO SYS_ROLE (ROLE_ID, ROLE_NAME, ROLE_KEY, DESCRIPTION, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('ROLE005', '安全副厂长', 'VICE_DIRECTOR_SAFETY', '负责安全管理和监督', 1, SYSDATE, SYSDATE, '安全副厂长，负责安全相关事务');

-- 销售主任角色
INSERT INTO SYS_ROLE (ROLE_ID, ROLE_NAME, ROLE_KEY, DESCRIPTION, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('ROLE006', '销售主任', 'SALES_DIRECTOR', '负责销售管理和市场开拓', 1, SYSDATE, SYSDATE, '销售主任，负责销售相关事务');

-- 财务主任角色
INSERT INTO SYS_ROLE (ROLE_ID, ROLE_NAME, ROLE_KEY, DESCRIPTION, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('ROLE007', '财务主任', 'FINANCE_DIRECTOR', '负责财务管理和资金监督', 1, SYSDATE, SYSDATE, '财务主任，负责财务相关事务');

-- 办公室主任角色
INSERT INTO SYS_ROLE (ROLE_ID, ROLE_NAME, ROLE_KEY, DESCRIPTION, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('ROLE008', '办公室主任', 'OFFICE_DIRECTOR', '负责行政管理和综合协调', 1, SYSDATE, SYSDATE, '办公室主任，负责行政相关事务');

-- 分厂厂长角色
INSERT INTO SYS_ROLE (ROLE_ID, ROLE_NAME, ROLE_KEY, DESCRIPTION, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('ROLE009', '分厂厂长', 'FACTORY_MANAGER', '负责分厂日常管理', 1, SYSDATE, SYSDATE, '分厂厂长，负责分厂管理');

-- 5. 创建新的部门结构
-- =====================================================

-- 更新公司根部门信息
UPDATE SYS_DEPARTMENT SET 
    DEPT_NAME = '金利科技有限公司',
    DEPT_CODE = 'JINLI_TECH',
    LEADER = '厂长',
    PHONE = '010-12345678',
    EMAIL = '<EMAIL>',
    UPDATE_TIME = SYSDATE
WHERE DEPT_ID = 'DEPT001';

-- 厂长办公室
INSERT INTO SYS_DEPARTMENT (DEPT_ID, DEPT_NAME, DEPT_CODE, PARENT_ID, LEADER, PHONE, EMAIL, SORT, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('DEPT010', '厂长办公室', 'DIRECTOR_OFFICE', 'DEPT001', '厂长', '010-12345679', '<EMAIL>', 1, 1, SYSDATE, SYSDATE, '厂长办公室');

-- 生产管理部
INSERT INTO SYS_DEPARTMENT (DEPT_ID, DEPT_NAME, DEPT_CODE, PARENT_ID, LEADER, PHONE, EMAIL, SORT, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('DEPT011', '生产管理部', 'PRODUCTION_DEPT', 'DEPT001', '生产副厂长', '010-12345680', '<EMAIL>', 2, 1, SYSDATE, SYSDATE, '生产管理部门');

-- 技术管理部
INSERT INTO SYS_DEPARTMENT (DEPT_ID, DEPT_NAME, DEPT_CODE, PARENT_ID, LEADER, PHONE, EMAIL, SORT, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('DEPT012', '技术管理部', 'TECH_DEPT', 'DEPT001', '技术副厂长', '010-12345681', '<EMAIL>', 3, 1, SYSDATE, SYSDATE, '技术管理部门');

-- 安全管理部
INSERT INTO SYS_DEPARTMENT (DEPT_ID, DEPT_NAME, DEPT_CODE, PARENT_ID, LEADER, PHONE, EMAIL, SORT, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('DEPT013', '安全管理部', 'SAFETY_DEPT', 'DEPT001', '安全副厂长', '010-12345682', '<EMAIL>', 4, 1, SYSDATE, SYSDATE, '安全管理部门');

-- 销售部
INSERT INTO SYS_DEPARTMENT (DEPT_ID, DEPT_NAME, DEPT_CODE, PARENT_ID, LEADER, PHONE, EMAIL, SORT, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('DEPT014', '销售部', 'SALES_DEPT', 'DEPT001', '销售主任', '010-12345683', '<EMAIL>', 5, 1, SYSDATE, SYSDATE, '销售部门');

-- 财务部
INSERT INTO SYS_DEPARTMENT (DEPT_ID, DEPT_NAME, DEPT_CODE, PARENT_ID, LEADER, PHONE, EMAIL, SORT, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('DEPT015', '财务部', 'FINANCE_DEPT', 'DEPT001', '财务主任', '010-12345684', '<EMAIL>', 6, 1, SYSDATE, SYSDATE, '财务部门');

-- 办公室
INSERT INTO SYS_DEPARTMENT (DEPT_ID, DEPT_NAME, DEPT_CODE, PARENT_ID, LEADER, PHONE, EMAIL, SORT, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('DEPT016', '办公室', 'OFFICE_DEPT', 'DEPT001', '办公室主任', '010-12345685', '<EMAIL>', 7, 1, SYSDATE, SYSDATE, '办公室部门');

-- 一分厂
INSERT INTO SYS_DEPARTMENT (DEPT_ID, DEPT_NAME, DEPT_CODE, PARENT_ID, LEADER, PHONE, EMAIL, SORT, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('DEPT017', '一分厂', 'FACTORY_ONE', 'DEPT011', '一分厂厂长', '010-12345686', '<EMAIL>', 1, 1, SYSDATE, SYSDATE, '第一分厂');

-- 二分厂
INSERT INTO SYS_DEPARTMENT (DEPT_ID, DEPT_NAME, DEPT_CODE, PARENT_ID, LEADER, PHONE, EMAIL, SORT, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('DEPT018', '二分厂', 'FACTORY_TWO', 'DEPT011', '二分厂厂长', '010-12345687', '<EMAIL>', 2, 1, SYSDATE, SYSDATE, '第二分厂');

-- 三分厂
INSERT INTO SYS_DEPARTMENT (DEPT_ID, DEPT_NAME, DEPT_CODE, PARENT_ID, LEADER, PHONE, EMAIL, SORT, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('DEPT019', '三分厂', 'FACTORY_THREE', 'DEPT011', '三分厂厂长', '010-12345688', '<EMAIL>', 3, 1, SYSDATE, SYSDATE, '第三分厂');

-- 6. 创建新用户
-- =====================================================

-- 厂长
INSERT INTO SYS_USER (USER_ID, USERNAME, PASSWORD, REAL_NAME, EMAIL, PHONE, DEPARTMENT_ID, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('USER010', 'factory_director', '123456', '张厂长', '<EMAIL>', '13800000010', 'DEPT010', 1, SYSDATE, SYSDATE, '厂长账号');

-- 生产副厂长
INSERT INTO SYS_USER (USER_ID, USERNAME, PASSWORD, REAL_NAME, EMAIL, PHONE, DEPARTMENT_ID, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('USER011', 'vice_director_prod', '123456', '李副厂长', '<EMAIL>', '13800000011', 'DEPT011', 1, SYSDATE, SYSDATE, '生产副厂长账号');

-- 技术副厂长
INSERT INTO SYS_USER (USER_ID, USERNAME, PASSWORD, REAL_NAME, EMAIL, PHONE, DEPARTMENT_ID, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('USER012', 'vice_director_tech', '123456', '王副厂长', '<EMAIL>', '13800000012', 'DEPT012', 1, SYSDATE, SYSDATE, '技术副厂长账号');

-- 安全副厂长
INSERT INTO SYS_USER (USER_ID, USERNAME, PASSWORD, REAL_NAME, EMAIL, PHONE, DEPARTMENT_ID, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('USER013', 'vice_director_safety', '123456', '赵副厂长', '<EMAIL>', '13800000013', 'DEPT013', 1, SYSDATE, SYSDATE, '安全副厂长账号');

-- 销售主任
INSERT INTO SYS_USER (USER_ID, USERNAME, PASSWORD, REAL_NAME, EMAIL, PHONE, DEPARTMENT_ID, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('USER014', 'sales_director', '123456', '刘主任', '<EMAIL>', '13800000014', 'DEPT014', 1, SYSDATE, SYSDATE, '销售主任账号');

-- 财务主任
INSERT INTO SYS_USER (USER_ID, USERNAME, PASSWORD, REAL_NAME, EMAIL, PHONE, DEPARTMENT_ID, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('USER015', 'finance_director', '123456', '陈主任', '<EMAIL>', '13800000015', 'DEPT015', 1, SYSDATE, SYSDATE, '财务主任账号');

-- 办公室主任
INSERT INTO SYS_USER (USER_ID, USERNAME, PASSWORD, REAL_NAME, EMAIL, PHONE, DEPARTMENT_ID, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('USER016', 'office_director', '123456', '孙主任', '<EMAIL>', '13800000016', 'DEPT016', 1, SYSDATE, SYSDATE, '办公室主任账号');

-- 一分厂厂长
INSERT INTO SYS_USER (USER_ID, USERNAME, PASSWORD, REAL_NAME, EMAIL, PHONE, DEPARTMENT_ID, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('USER017', 'factory1_manager', '123456', '周厂长', '<EMAIL>', '13800000017', 'DEPT017', 1, SYSDATE, SYSDATE, '一分厂厂长账号');

-- 二分厂厂长
INSERT INTO SYS_USER (USER_ID, USERNAME, PASSWORD, REAL_NAME, EMAIL, PHONE, DEPARTMENT_ID, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('USER018', 'factory2_manager', '123456', '吴厂长', '<EMAIL>', '13800000018', 'DEPT018', 1, SYSDATE, SYSDATE, '二分厂厂长账号');

-- 三分厂厂长
INSERT INTO SYS_USER (USER_ID, USERNAME, PASSWORD, REAL_NAME, EMAIL, PHONE, DEPARTMENT_ID, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('USER019', 'factory3_manager', '123456', '郑厂长', '<EMAIL>', '13800000019', 'DEPT019', 1, SYSDATE, SYSDATE, '三分厂厂长账号');

-- 7. 分配用户角色
-- =====================================================

-- 厂长角色分配
INSERT INTO SYS_USER_ROLE (USER_ID, ROLE_ID) VALUES ('USER010', 'ROLE002');

-- 生产副厂长角色分配
INSERT INTO SYS_USER_ROLE (USER_ID, ROLE_ID) VALUES ('USER011', 'ROLE003');

-- 技术副厂长角色分配
INSERT INTO SYS_USER_ROLE (USER_ID, ROLE_ID) VALUES ('USER012', 'ROLE004');

-- 安全副厂长角色分配
INSERT INTO SYS_USER_ROLE (USER_ID, ROLE_ID) VALUES ('USER013', 'ROLE005');

-- 销售主任角色分配
INSERT INTO SYS_USER_ROLE (USER_ID, ROLE_ID) VALUES ('USER014', 'ROLE006');

-- 财务主任角色分配
INSERT INTO SYS_USER_ROLE (USER_ID, ROLE_ID) VALUES ('USER015', 'ROLE007');

-- 办公室主任角色分配
INSERT INTO SYS_USER_ROLE (USER_ID, ROLE_ID) VALUES ('USER016', 'ROLE008');

-- 分厂厂长角色分配
INSERT INTO SYS_USER_ROLE (USER_ID, ROLE_ID) VALUES ('USER017', 'ROLE009');
INSERT INTO SYS_USER_ROLE (USER_ID, ROLE_ID) VALUES ('USER018', 'ROLE009');
INSERT INTO SYS_USER_ROLE (USER_ID, ROLE_ID) VALUES ('USER019', 'ROLE009');

-- 8. 提交事务并验证
-- =====================================================
COMMIT;

-- 验证创建结果
SELECT '=== 角色信息 ===' AS 信息类型;
SELECT ROLE_NAME AS 角色名称, ROLE_KEY AS 角色标识, DESCRIPTION AS 描述 FROM SYS_ROLE ORDER BY ROLE_ID;

SELECT '=== 部门信息 ===' AS 信息类型;
SELECT DEPT_NAME AS 部门名称, DEPT_CODE AS 部门编码, LEADER AS 负责人 FROM SYS_DEPARTMENT ORDER BY SORT;

SELECT '=== 用户信息 ===' AS 信息类型;
SELECT u.REAL_NAME AS 姓名, u.USERNAME AS 用户名, d.DEPT_NAME AS 部门, r.ROLE_NAME AS 角色 
FROM SYS_USER u 
LEFT JOIN SYS_DEPARTMENT d ON u.DEPARTMENT_ID = d.DEPT_ID
LEFT JOIN SYS_USER_ROLE ur ON u.USER_ID = ur.USER_ID
LEFT JOIN SYS_ROLE r ON ur.ROLE_ID = r.ROLE_ID
ORDER BY u.USER_ID;
