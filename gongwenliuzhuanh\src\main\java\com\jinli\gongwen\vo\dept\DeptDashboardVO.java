package com.jinli.gongwen.vo.dept;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 部门经理仪表板视图对象
 */
@Data
@ApiModel("部门经理仪表板视图对象")
public class DeptDashboardVO {

    @ApiModelProperty("待签收公文数量")
    private Integer pendingReceiveCount;

    @ApiModelProperty("本部门公文总数")
    private Integer deptDocumentCount;

    @ApiModelProperty("本月创建公文数量")
    private Integer monthCreatedCount;

    @ApiModelProperty("本月签收公文数量")
    private Integer monthReceivedCount;

    @ApiModelProperty("草稿数量")
    private Integer draftCount;

    @ApiModelProperty("流转中公文数量")
    private Integer processingCount;

    @ApiModelProperty("已完成公文数量")
    private Integer completedCount;

    @ApiModelProperty("公文类型统计")
    private Map<String, Integer> docTypeStats;

    @ApiModelProperty("公文状态统计")
    private Map<String, Integer> docStatusStats;

    @ApiModelProperty("最近创建的公文列表")
    private List<RecentDocumentVO> recentCreated;

    @ApiModelProperty("最近签收的公文列表")
    private List<RecentDocumentVO> recentReceived;

    @ApiModelProperty("本周工作统计")
    private List<DailyWorkVO> weeklyWork;

    @ApiModelProperty("部门工作效率")
    private DeptEfficiencyVO efficiency;

    /**
     * 最近公文视图对象
     */
    @Data
    @ApiModel("最近公文视图对象")
    public static class RecentDocumentVO {
        @ApiModelProperty("公文ID")
        private String docId;

        @ApiModelProperty("公文标题")
        private String docTitle;

        @ApiModelProperty("公文类型")
        private String docType;

        @ApiModelProperty("当前状态")
        private String currentStatus;

        @ApiModelProperty("创建时间")
        private String createTime;

        @ApiModelProperty("处理时间")
        private String processTime;
    }

    /**
     * 每日工作统计视图对象
     */
    @Data
    @ApiModel("每日工作统计视图对象")
    public static class DailyWorkVO {
        @ApiModelProperty("日期")
        private String date;

        @ApiModelProperty("创建数量")
        private Integer created;

        @ApiModelProperty("签收数量")
        private Integer received;

        @ApiModelProperty("处理数量")
        private Integer processed;
    }

    /**
     * 部门效率视图对象
     */
    @Data
    @ApiModel("部门效率视图对象")
    public static class DeptEfficiencyVO {
        @ApiModelProperty("平均处理时间（小时）")
        private Double avgProcessTime;

        @ApiModelProperty("及时完成率")
        private Double timelyCompletionRate;

        @ApiModelProperty("本月处理效率排名")
        private Integer monthlyRanking;

        @ApiModelProperty("效率趋势")
        private String efficiencyTrend; // UP, DOWN, STABLE
    }
}
