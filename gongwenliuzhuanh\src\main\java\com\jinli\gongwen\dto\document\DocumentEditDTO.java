package com.jinli.gongwen.dto.document;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 公文编辑数据传输对象
 */
@Data
@ApiModel("公文编辑数据传输对象")
public class DocumentEditDTO {

    @ApiModelProperty("公文ID")
    private String docId;

    @ApiModelProperty(value = "公文标题", required = true)
    @NotBlank(message = "公文标题不能为空")
    @Size(max = 200, message = "公文标题长度不能超过200个字符")
    private String docTitle;

    @ApiModelProperty("公文编号")
    @Size(max = 50, message = "公文编号长度不能超过50个字符")
    private String docNumber;

    @ApiModelProperty("公文类型")
    private String docType;

    @ApiModelProperty("公文级别")
    private String docLevel;

    @ApiModelProperty(value = "公文内容", required = true)
    @NotBlank(message = "公文内容不能为空")
    private String docContent;

    @ApiModelProperty("截止时间")
    private String deadline;

    @ApiModelProperty("编辑意见")
    @Size(max = 500, message = "编辑意见长度不能超过500个字符")
    private String editOpinion;

    @ApiModelProperty("格式调整说明")
    @Size(max = 500, message = "格式调整说明长度不能超过500个字符")
    private String formatAdjustment;

    @ApiModelProperty("备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;
}
