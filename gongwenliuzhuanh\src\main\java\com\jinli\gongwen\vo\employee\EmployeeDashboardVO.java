package com.jinli.gongwen.vo.employee;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 员工个人工作台视图对象
 */
@Data
@ApiModel("员工个人工作台视图对象")
public class EmployeeDashboardVO {

    @ApiModelProperty("待签收公文数量")
    private Integer pendingReceiveCount;

    @ApiModelProperty("已签收公文数量")
    private Integer receivedCount;

    @ApiModelProperty("协助创建公文数量")
    private Integer assistedCount;

    @ApiModelProperty("未读通知数量")
    private Integer unreadNotificationCount;

    @ApiModelProperty("本月工作统计")
    private MonthlyWorkVO monthlyWork;

    @ApiModelProperty("待办事项列表")
    private List<TodoItemVO> todoList;

    @ApiModelProperty("最近通知列表")
    private List<NotificationVO> recentNotifications;

    @ApiModelProperty("最近签收的公文")
    private List<RecentDocumentVO> recentReceived;

    @ApiModelProperty("个人工作效率")
    private WorkEfficiencyVO workEfficiency;

    /**
     * 月度工作统计视图对象
     */
    @Data
    @ApiModel("月度工作统计视图对象")
    public static class MonthlyWorkVO {
        @ApiModelProperty("签收数量")
        private Integer receiveCount;

        @ApiModelProperty("协助数量")
        private Integer assistCount;

        @ApiModelProperty("完成率")
        private Double completionRate;

        @ApiModelProperty("及时率")
        private Double timelyRate;
    }

    /**
     * 待办事项视图对象
     */
    @Data
    @ApiModel("待办事项视图对象")
    public static class TodoItemVO {
        @ApiModelProperty("事项ID")
        private String itemId;

        @ApiModelProperty("事项类型")
        private String itemType; // RECEIVE, ASSIST, NOTIFICATION

        @ApiModelProperty("事项标题")
        private String itemTitle;

        @ApiModelProperty("事项描述")
        private String itemDescription;

        @ApiModelProperty("优先级")
        private String priority; // HIGH, NORMAL, LOW

        @ApiModelProperty("截止时间")
        private String deadline;

        @ApiModelProperty("创建时间")
        private String createTime;
    }

    /**
     * 通知视图对象
     */
    @Data
    @ApiModel("通知视图对象")
    public static class NotificationVO {
        @ApiModelProperty("通知ID")
        private String notificationId;

        @ApiModelProperty("通知类型")
        private String notificationType;

        @ApiModelProperty("通知标题")
        private String notificationTitle;

        @ApiModelProperty("通知内容")
        private String notificationContent;

        @ApiModelProperty("是否已读")
        private Boolean isRead;

        @ApiModelProperty("发送时间")
        private String sendTime;

        @ApiModelProperty("相关公文ID")
        private String relatedDocId;
    }

    /**
     * 最近公文视图对象
     */
    @Data
    @ApiModel("最近公文视图对象")
    public static class RecentDocumentVO {
        @ApiModelProperty("公文ID")
        private String docId;

        @ApiModelProperty("公文标题")
        private String docTitle;

        @ApiModelProperty("公文类型")
        private String docType;

        @ApiModelProperty("签收时间")
        private String receiveTime;

        @ApiModelProperty("发送部门")
        private String sendDeptName;
    }

    /**
     * 工作效率视图对象
     */
    @Data
    @ApiModel("工作效率视图对象")
    public static class WorkEfficiencyVO {
        @ApiModelProperty("平均处理时间（小时）")
        private Double avgProcessTime;

        @ApiModelProperty("本月排名")
        private Integer monthlyRanking;

        @ApiModelProperty("效率评分")
        private Double efficiencyScore;

        @ApiModelProperty("改进建议")
        private List<String> improvements;
    }
}
