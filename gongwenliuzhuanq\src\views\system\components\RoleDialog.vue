<template>
  <el-dialog
    :title="isEdit ? '编辑角色' : '新增角色'"
    v-model="dialogVisible"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="角色名称" prop="roleName">
        <el-input
          v-model="formData.roleName"
          placeholder="请输入角色名称"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="角色标识" prop="roleKey">
        <el-input
          v-model="formData.roleKey"
          placeholder="请输入角色标识"
          maxlength="50"
          show-word-limit
          :disabled="isEdit"
        />
        <div class="form-tip">角色标识用于系统内部识别，创建后不可修改</div>
      </el-form-item>
      
      <el-form-item label="显示顺序" prop="roleSort">
        <el-input-number
          v-model="formData.roleSort"
          :min="0"
          :max="999"
          controls-position="right"
          style="width: 200px"
        />
      </el-form-item>
      
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio label="1">正常</el-radio>
          <el-radio label="0">停用</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="角色描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入角色描述"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { createRole, updateRole } from '@/api/system/role'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  roleData: {
    type: Object,
    default: () => ({})
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const loading = ref(false)
const formRef = ref()
const dialogVisible = ref(false)

const formData = reactive({
  roleName: '',
  roleKey: '',
  roleSort: 0,
  status: '1',
  description: ''
})

// 表单验证规则
const rules = {
  roleName: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { min: 2, max: 50, message: '角色名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  roleKey: [
    { required: true, message: '请输入角色标识', trigger: 'blur' },
    { min: 2, max: 50, message: '角色标识长度在 2 到 50 个字符', trigger: 'blur' },
    { pattern: /^[A-Z_]+$/, message: '角色标识只能包含大写字母和下划线', trigger: 'blur' }
  ],
  roleSort: [
    { required: true, message: '请输入显示顺序', trigger: 'blur' },
    { type: 'number', min: 0, max: 999, message: '显示顺序必须在 0 到 999 之间', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 监听visible变化
watch(() => props.visible, (val) => {
  dialogVisible.value = val
  if (val) {
    initForm()
  }
})

watch(dialogVisible, (val) => {
  emit('update:visible', val)
})

// 初始化表单
const initForm = () => {
  nextTick(() => {
    formRef.value?.clearValidate()
  })
  
  if (props.isEdit && props.roleData.roleId) {
    // 编辑模式，复制数据
    Object.assign(formData, {
      roleId: props.roleData.roleId,
      roleName: props.roleData.roleName || '',
      roleKey: props.roleData.roleKey || '',
      roleSort: props.roleData.roleSort || 0,
      status: props.roleData.status || '1',
      description: props.roleData.description || ''
    })
  } else {
    // 新增模式，重置表单
    Object.assign(formData, {
      roleName: '',
      roleKey: '',
      roleSort: 0,
      status: '1',
      description: ''
    })
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    loading.value = true
    
    if (props.isEdit) {
      await updateRole(formData.roleId, formData)
      ElMessage.success('角色更新成功')
    } else {
      await createRole(formData)
      ElMessage.success('角色创建成功')
    }
    
    emit('success')
    handleClose()
  } catch (error) {
    if (error !== false) { // 不是表单验证错误
      ElMessage.error(props.isEdit ? '角色更新失败' : '角色创建失败')
    }
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  formRef.value?.resetFields()
}
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.dialog-footer {
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto !important;
  }
}
</style>
