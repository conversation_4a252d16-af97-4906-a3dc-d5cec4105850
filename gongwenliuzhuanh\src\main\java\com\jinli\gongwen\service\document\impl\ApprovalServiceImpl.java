package com.jinli.gongwen.service.document.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinli.gongwen.dto.document.DocumentApprovalDTO;
import com.jinli.gongwen.service.document.ApprovalService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 审批服务实现类
 */
@Service
public class ApprovalServiceImpl implements ApprovalService {

    @Override
    public void approveDocument(DocumentApprovalDTO approvalDTO) {
        // TODO: 实现具体逻辑
    }

    @Override
    public void batchApproveDocuments(List<DocumentApprovalDTO> approvalDTOList) {
        // TODO: 实现具体逻辑
    }

    @Override
    public Object getApprovalStatistics(String userId) {
        // TODO: 实现具体逻辑
        return new Object();
    }

    @Override
    public IPage<Object> getApprovalHistory(Page<Object> page, String userId) {
        // TODO: 实现具体逻辑
        return new Page<>();
    }

    @Override
    public void returnDocument(String docId, String reason, String userId) {
        // TODO: 实现具体逻辑
    }
}
