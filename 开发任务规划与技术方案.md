# 河北金力集团公文流转系统开发任务规划与技术方案

## 📋 项目概述

基于需求文档和组织架构重构，为金利科技有限公司设计完整的公文流转系统。系统包含1个系统管理员、1个厂长、3个副厂长、3个主任、3个分厂厂长，共11个核心管理岗位。

## 🏢 **组织架构重构（2025年1月更新）**

### **新组织架构**
```
金利科技有限公司
├── 厂长 (FACTORY_DIRECTOR) - 张厂长
├── 生产副厂长 (VICE_DIRECTOR_PROD) - 李副厂长
├── 技术副厂长 (VICE_DIRECTOR_TECH) - 王副厂长
├── 安全副厂长 (VICE_DIRECTOR_SAFETY) - 赵副厂长
├── 销售主任 (SALES_DIRECTOR) - 刘主任
├── 财务主任 (FINANCE_DIRECTOR) - 陈主任
├── 办公室主任 (OFFICE_DIRECTOR) - 孙主任
├── 一分厂厂长 (FACTORY_MANAGER) - 周厂长
├── 二分厂厂长 (FACTORY_MANAGER) - 吴厂长
├── 三分厂厂长 (FACTORY_MANAGER) - 郑厂长
└── 系统管理员 (ADMIN) - 系统管理员
```

### **权限层级设计**
1. **厂长**：最高决策权，全部公文审批权限
2. **副厂长**：专业领域审批权限，部门协调权限
3. **主任级**：部门管理权限，专业公文处理权限
4. **分厂厂长**：分厂管理权限，生产公文处理权限
5. **系统管理员**：系统维护权限，无业务权限

## 🎯 核心约束条件

1. **严格遵循公文流转流程**：部门拟稿→办公室修改→副厂长审核→厂长审签→办公室发送→部门签收
2. **基于重构组织架构**：11个管理岗位，9个部门，9个角色
3. **技术栈要求**：Vue.js前端 + Spring Boot后端 + 达梦8数据库
4. **界面适配**：支持800*600和1024*768分辨率
5. **公文格式**：参考需求文档中的公文样例进行设计

## 👥 **各职位详细职责分析**

### **1. 厂长 (FACTORY_DIRECTOR)**
**职责范围**：
- 公司最高决策者，负责公司整体战略规划和重大决策
- 审批所有重要公文和决策文件（预算、人事、重大合同等）
- 监督各部门工作执行情况和绩效考核
- 对外代表公司形象，处理重要商务活动

**公文流转权限**：
- 最终审批权：所有公文的最终决策权
- 查看权限：可查看全公司所有公文
- 退回权限：可将公文退回任何环节重新处理
- 紧急处理：可跨越流程直接处理紧急公文

### **2. 生产副厂长 (VICE_DIRECTOR_PROD)**
**职责范围**：
- 负责生产计划制定和执行，协调各分厂生产任务
- 审批生产相关公文（生产计划、设备采购、工艺改进等）
- 监督生产安全和质量管理
- 协调生产与其他部门的配合

**公文流转权限**：
- 专业审批权：生产相关公文的审批权
- 分厂协调：可直接向分厂下达生产指令
- 部门查看：可查看生产相关部门的公文
- 上报权限：向厂长上报重要生产事项

### **3. 技术副厂长 (VICE_DIRECTOR_TECH)**
**职责范围**：
- 负责技术创新和研发管理，制定技术发展规划
- 审批技术改造方案和技术标准
- 指导技术培训和人才培养
- 监督技术标准执行和质量控制

**公文流转权限**：
- 技术审批权：技术相关公文的审批权
- 标准制定：可制定和修改技术标准文件
- 培训管理：可审批技术培训计划和方案
- 创新推广：可推广技术创新成果

### **4. 安全副厂长 (VICE_DIRECTOR_SAFETY)**
**职责范围**：
- 负责安全生产监督管理和安全制度制定
- 审批安全相关制度和安全措施
- 组织安全检查、安全培训和应急演练
- 处理安全事故和安全隐患

**公文流转权限**：
- 安全审批权：安全相关公文的审批权
- 紧急处置：安全事故的紧急处置权
- 检查权限：可对所有部门进行安全检查
- 培训组织：可组织全公司安全培训

### **5. 销售主任 (SALES_DIRECTOR)**
**职责范围**：
- 负责市场开拓和客户维护，制定销售策略
- 审批销售合同、价格政策和客户信用
- 管理销售团队和销售渠道
- 收集市场信息和竞争对手分析

**公文流转权限**：
- 销售审批权：销售相关公文的审批权
- 合同管理：可审批一定额度内的销售合同
- 客户管理：可处理客户相关事务
- 市场报告：定期上报市场情况

### **6. 财务主任 (FINANCE_DIRECTOR)**
**职责范围**：
- 负责财务管理和资金监督，制定财务制度
- 审批财务支出、预算和财务报表
- 监督成本控制和资金使用效率
- 处理税务、审计和财务风险管理

**公文流转权限**：
- 财务审批权：财务相关公文的审批权
- 预算控制：可审批部门预算和支出申请
- 报表编制：编制和上报财务报表
- 风险监控：监控财务风险和资金安全

### **7. 办公室主任 (OFFICE_DIRECTOR)**
**职责范围**：
- 负责行政管理和综合协调，处理日常行政事务
- 组织会议、活动和公文流转管理
- 管理档案、文件和信息发布
- 协调各部门关系和对外联络

**公文流转权限**：
- 流转管理权：负责公文的流转和分发
- 格式审核：审核公文格式和规范性
- 档案管理：管理公文档案和历史记录
- 信息发布：负责公司信息的对外发布

### **8. 分厂厂长 (FACTORY_MANAGER)**
**职责范围**：
- 负责分厂日常生产管理和员工管理
- 执行生产计划和任务，确保生产目标完成
- 管理分厂设备、物料和质量
- 上报生产情况和问题处理

**公文流转权限**：
- 分厂管理权：分厂内部公文的处理权
- 生产报告：定期上报生产情况和问题
- 申请权限：可申请设备、物料和人员
- 执行权限：执行上级下达的生产指令

### **9. 系统管理员 (ADMIN)**
**职责范围**：
- 负责系统维护和技术支持
- 管理用户账号、权限和系统配置
- 监控系统运行状态和数据安全
- 处理系统故障和技术问题

**系统权限**：
- 用户管理：创建、修改、删除用户账号
- 权限配置：分配和调整用户权限
- 系统维护：系统备份、恢复和升级
- 日志监控：查看系统日志和操作记录

## 🏗️ 开发任务优先级与依赖关系

### 第一优先级：基础架构（并行开发）

#### 1. 系统管理员功能模块 ⭐⭐⭐⭐⭐
**开发周期**：5-7天
**依赖关系**：无依赖，可独立开发
**关键原因**：为其他角色提供用户和权限管理基础

**核心功能**：
- 用户管理（11个管理岗位的增删改查）
- 角色管理（9个角色的权限配置）
- 流程管理（6个流转步骤的配置）

#### 2. 办公室主任功能模块 ⭐⭐⭐⭐⭐
**开发周期**：7-10天
**依赖关系**：依赖系统管理员模块的用户权限体系
**关键原因**：公文流转的核心枢纽，其他角色都需要与之交互

**核心功能**：
- 公文编辑器（富文本，格式规范化）
- 流转控制（步骤管理，状态跟踪）
- 发送管理（批量发送，记录管理）

### 第二优先级：业务核心（顺序开发）

#### 3. 部门经理功能模块 ⭐⭐⭐⭐
**开发周期**：5-7天  
**依赖关系**：依赖办公室主任的流转控制功能  
**关键原因**：公文流转的起点，为后续审核环节提供数据

**核心功能**：
- 公文拟制（创建草稿，提交流转）
- 签收管理（接收公文，确认签收）
- 公文浏览（部门相关公文查看）

#### 4. 副厂长功能模块 ⭐⭐⭐⭐
**开发周期**：6-8天  
**依赖关系**：依赖部门经理的公文拟制和办公室的流转控制  
**关键原因**：审核环节的关键节点

**核心功能**：
- 公文审核（按分管领域自动分配）
- 审核意见（同意/退回操作）
- 公文查询（已发公文浏览）

#### 5. 厂长功能模块 ⭐⭐⭐⭐
**开发周期**：5-6天  
**依赖关系**：依赖副厂长的审核功能  
**关键原因**：最终审批环节

**核心功能**：
- 公文审签（最终审批权限）
- 意见填写（审签意见记录）
- 历史查询（所有公文查看权限）

### 第三优先级：辅助功能

#### 6. 普通员工功能模块 ⭐⭐⭐
**开发周期**：3-4天  
**依赖关系**：依赖部门经理功能模块  
**关键原因**：协助功能，优先级相对较低

**核心功能**：
- 简化拟制（协助部门经理）
- 公文签收（基础签收功能）
- 公文浏览（已签收公文查看）

## 🛠️ 技术实现架构

### 前端技术栈
```
Vue.js 2.6+ + Element UI 2.15+
├── 路由管理 (Vue Router)
├── 状态管理 (Vuex)
├── HTTP客户端 (Axios)
├── 富文本编辑器 (Quill Editor)
└── 图表组件 (ECharts)
```

### 后端技术栈
```
Spring Boot 2.7+ + MyBatis Plus 3.5+
├── 安全认证 (Spring Security + JWT)
├── 数据访问 (MyBatis Plus)
├── 文件处理 (Spring Boot File Upload)
├── API文档 (Swagger 3.0)
└── 日志管理 (Logback)
```

### 数据库设计
```
达梦8数据库
├── 用户权限表 (SYS_USER, SYS_ROLE, SYS_DEPARTMENT)
├── 公文管理表 (DOC_DOCUMENT, DOC_ATTACHMENT)
├── 流转记录表 (DOC_FLOW_RECORD, DOC_RECEIVE_RECORD)
└── 系统配置表 (SYS_CONFIG, SYS_FLOW_CONFIG)
```

## 📱 界面设计规范

### 响应式布局
- **800*600分辨率**：紧凑布局，侧边栏可折叠
- **1024*768分辨率**：标准布局，完整功能展示
- **更高分辨率**：宽屏适配，内容居中显示

### 色彩方案
- **主色调**：#409EFF（蓝色，专业稳重）
- **辅助色**：#67C23A（绿色，成功状态）
- **警告色**：#E6A23C（橙色，警告提示）
- **错误色**：#F56C6C（红色，错误状态）

### 组件规范
- **按钮**：统一使用Element UI按钮组件
- **表单**：统一验证规则和错误提示
- **表格**：支持分页、排序、筛选
- **弹窗**：统一的确认和取消操作

## 🔐 权限控制方案

### 角色权限矩阵
| 功能模块 | 系统管理员 | 厂长 | 副厂长 | 办公室主任 | 部门经理 | 普通员工 |
|---------|-----------|------|--------|-----------|----------|----------|
| 用户管理 | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |
| 角色管理 | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |
| 流程管理 | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |
| 公文拟制 | ❌ | ❌ | ❌ | ❌ | ✅ | ✅ |
| 公文修改 | ❌ | ❌ | ❌ | ✅ | ❌ | ❌ |
| 公文审核 | ❌ | ❌ | ✅ | ❌ | ❌ | ❌ |
| 公文审签 | ❌ | ✅ | ❌ | ❌ | ❌ | ❌ |
| 公文发送 | ❌ | ❌ | ❌ | ✅ | ❌ | ❌ |
| 公文签收 | ❌ | ❌ | ❌ | ❌ | ✅ | ✅ |
| 公文查询 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |

### 数据权限控制
- **系统管理员**：全部数据访问权限
- **厂长**：所有公文的查看和审签权限
- **副厂长**：分管领域公文的审核权限
- **办公室主任**：所有公文的修改和流转权限
- **部门经理**：本部门公文的拟制和签收权限
- **普通员工**：本部门公文的协助和签收权限

## 📋 开发里程碑

### 第一阶段（2周）：基础架构
- [ ] 系统管理员功能模块完成
- [ ] 办公室主任功能模块完成
- [ ] 基础权限体系建立
- [ ] 公文流转核心逻辑实现

### 第二阶段（2周）：业务核心
- [ ] 部门经理功能模块完成
- [ ] 副厂长功能模块完成
- [ ] 厂长功能模块完成
- [ ] 完整流转流程测试通过

### 第三阶段（1周）：功能完善
- [ ] 普通员工功能模块完成
- [ ] 界面优化和响应式适配
- [ ] 性能优化和安全加固
- [ ] 全功能集成测试

## 🧪 测试策略

### 单元测试
- 后端Service层业务逻辑测试
- 前端组件功能测试
- 数据库操作测试

### 集成测试
- 公文流转完整流程测试
- 用户权限控制测试
- 文件上传下载测试

### 用户验收测试
- 12个测试账号的功能验证
- 不同分辨率下的界面测试
- 公文格式规范性测试

## 📦 部署方案

### 开发环境
- 前端：npm run serve (端口8081)
- 后端：Spring Boot (端口8080)
- 数据库：达梦8 (端口5236)

### 生产环境
- 前端：Nginx静态文件服务
- 后端：Tomcat应用服务器
- 数据库：达梦8集群部署

这个开发任务规划确保了严格按照需求文档要求，基于已精简的用户配置，实现完整的公文流转系统功能。
