<template>
  <div class="layout-container">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside :width="isCollapse ? '64px' : '200px'" class="sidebar-container">
        <div class="logo">
          <img src="/logo.svg" alt="Logo" v-if="!isCollapse">
          <span v-if="!isCollapse">金力集团</span>
        </div>
        
        <el-menu
          :default-active="activeMenu"
          :collapse="isCollapse"
          :unique-opened="true"
          class="sidebar-menu"
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
          router
        >
          <template v-for="route in menuRoutes" :key="route.path">
            <el-sub-menu v-if="route.children && route.children.length > 0" :index="route.path">
              <template #title>
                <el-icon><component :is="route.meta.icon" /></el-icon>
                <span>{{ route.meta.title }}</span>
              </template>
              <el-menu-item
                v-for="child in route.children"
                :key="child.path"
                :index="child.path"
              >
                <el-icon><component :is="child.meta.icon" /></el-icon>
                <span>{{ child.meta.title }}</span>
              </el-menu-item>
            </el-sub-menu>
            
            <el-menu-item v-else :index="route.path">
              <el-icon><component :is="route.meta.icon" /></el-icon>
              <span>{{ route.meta.title }}</span>
            </el-menu-item>
          </template>
        </el-menu>
      </el-aside>

      <!-- 主内容区 -->
      <el-container>
        <!-- 顶部导航栏 -->
        <el-header class="navbar">
          <div class="navbar-left">
            <el-button
              type="primary"
              link
              @click="toggleSidebar"
              class="hamburger"
            >
              <el-icon><Expand v-if="isCollapse" /><Fold v-else /></el-icon>
            </el-button>
            
            <el-breadcrumb separator="/">
              <el-breadcrumb-item
                v-for="item in breadcrumbList"
                :key="item.path"
                :to="item.path"
              >
                {{ item.meta.title }}
              </el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          
          <div class="navbar-right">
            <!-- 消息通知 -->
            <el-badge :value="unreadCount" class="navbar-item">
              <el-button type="primary" link @click="handleNotification">
                <el-icon><Bell /></el-icon>
              </el-button>
            </el-badge>
            
            <!-- 用户菜单 -->
            <el-dropdown class="navbar-item" @command="handleCommand">
              <span class="user-info">
                <el-avatar :size="32" :src="userInfo?.avatar">
                  {{ userInfo?.realName?.charAt(0) }}
                </el-avatar>
                <span class="user-name">{{ userInfo?.realName }}</span>
                <el-icon><ArrowDown /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">
                    <el-icon><User /></el-icon>个人中心
                  </el-dropdown-item>
                  <el-dropdown-item command="changePassword">
                    <el-icon><Key /></el-icon>修改密码
                  </el-dropdown-item>
                  <el-dropdown-item divided command="logout">
                    <el-icon><SwitchButton /></el-icon>退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>

        <!-- 主内容 -->
        <el-main class="main-content">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
    
    <!-- 修改密码对话框 -->
    <el-dialog
      v-model="passwordDialogVisible"
      title="修改密码"
      width="400px"
      :before-close="handlePasswordDialogClose"
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="80px"
      >
        <el-form-item label="原密码" prop="oldPassword">
          <el-input
            v-model="passwordForm.oldPassword"
            type="password"
            show-password
            placeholder="请输入原密码"
          />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            show-password
            placeholder="请输入新密码"
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            show-password
            placeholder="请再次输入新密码"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handlePasswordDialogClose">取消</el-button>
          <el-button type="primary" @click="handlePasswordSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 消息通知抽屉 -->
    <el-drawer
      v-model="notificationDrawerVisible"
      title="消息通知"
      direction="rtl"
      size="400px"
    >
      <div class="notification-content">
        <!-- 通知列表 -->
        <div class="notification-list">
          <div class="notification-item" v-for="(item, index) in notifications" :key="index">
            <div class="notification-header">
              <span class="notification-title">{{ item.title }}</span>
              <span class="notification-time">{{ formatNotificationTime(item.time) }}</span>
            </div>
            <div class="notification-content-text">{{ item.content }}</div>
            <div class="notification-actions">
              <el-button size="small" type="primary" @click="markAsRead(item)">
                标记已读
              </el-button>
              <el-button size="small" @click="viewNotification(item)">
                查看详情
              </el-button>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <el-empty v-if="notifications.length === 0" description="暂无消息通知" />

        <!-- 操作按钮 -->
        <div class="notification-footer">
          <el-button type="primary" @click="markAllAsRead">全部标记已读</el-button>
          <el-button @click="clearAllNotifications">清空通知</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { changePassword } from '@/api/auth'
import { ElMessage, ElMessageBox } from 'element-plus'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const isCollapse = ref(false)
const unreadCount = ref(5)
const passwordDialogVisible = ref(false)
const passwordFormRef = ref()
const notificationDrawerVisible = ref(false)

// 通知数据
const notifications = ref([
  {
    id: 1,
    title: '新公文待处理',
    content: '您有一份关于"安全生产管理"的公文待处理，请及时查看。',
    time: Date.now() - 30 * 60 * 1000, // 30分钟前
    read: false,
    type: 'document'
  },
  {
    id: 2,
    title: '公文审核通过',
    content: '您提交的"采购申请"公文已审核通过。',
    time: Date.now() - 2 * 60 * 60 * 1000, // 2小时前
    read: false,
    type: 'approval'
  },
  {
    id: 3,
    title: '系统维护通知',
    content: '系统将于今晚22:00-24:00进行维护，请提前保存工作。',
    time: Date.now() - 4 * 60 * 60 * 1000, // 4小时前
    read: false,
    type: 'system'
  },
  {
    id: 4,
    title: '会议提醒',
    content: '明天上午9:00有部门例会，请准时参加。',
    time: Date.now() - 6 * 60 * 60 * 1000, // 6小时前
    read: true,
    type: 'meeting'
  },
  {
    id: 5,
    title: '公文被退回',
    content: '您提交的"工作报告"公文被退回，请查看意见并重新提交。',
    time: Date.now() - 8 * 60 * 60 * 1000, // 8小时前
    read: false,
    type: 'document'
  }
])

// 修改密码表单
const passwordForm = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 表单验证规则
const passwordRules = {
  oldPassword: [
    { required: true, message: '请输入原密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.value.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 计算属性
const userInfo = computed(() => userStore.userInfo)
const activeMenu = computed(() => route.path)

// 菜单路由
const menuRoutes = computed(() => {
  const routes = router.getRoutes()
  const layoutRoute = routes.find(r => r.name === 'Layout')
  if (!layoutRoute || !layoutRoute.children) return []
  
  return layoutRoute.children.filter(route => {
    // 过滤掉不需要在菜单中显示的路由
    if (route.meta?.hidden) return false
    
    // 权限检查
    if (route.meta?.roles && route.meta.roles.length > 0) {
      return userStore.hasRole(route.meta.roles[0])
    }
    
    return route.meta?.title
  })
})

// 面包屑导航
const breadcrumbList = computed(() => {
  const matched = route.matched.filter(item => item.meta && item.meta.title)
  return matched
})

// 方法
const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value
}

const handleNotification = () => {
  notificationDrawerVisible.value = true
}

// 通知相关方法
const formatNotificationTime = (timestamp) => {
  const now = Date.now()
  const diff = now - timestamp
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else {
    return `${days}天前`
  }
}

const markAsRead = (notification) => {
  notification.read = true
  updateUnreadCount()
  ElMessage.success('已标记为已读')
}

const viewNotification = (notification) => {
  notification.read = true
  updateUnreadCount()
  ElMessage.info(`查看通知: ${notification.title}`)
  // 这里可以添加跳转到具体页面的逻辑
}

const markAllAsRead = () => {
  notifications.value.forEach(item => {
    item.read = true
  })
  updateUnreadCount()
  ElMessage.success('已全部标记为已读')
}

const clearAllNotifications = () => {
  notifications.value = []
  updateUnreadCount()
  ElMessage.success('已清空所有通知')
}

const updateUnreadCount = () => {
  unreadCount.value = notifications.value.filter(item => !item.read).length
}

// 初始化
onMounted(() => {
  updateUnreadCount()
})

const handleCommand = (command) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'changePassword':
      passwordDialogVisible.value = true
      break
    case 'logout':
      handleLogout()
      break
  }
}

const handleLogout = () => {
  ElMessageBox.confirm('确定要退出登录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    userStore.logout()
    router.push('/login')
    ElMessage.success('退出登录成功')
  })
}

const handlePasswordDialogClose = () => {
  passwordDialogVisible.value = false
  passwordForm.value = {
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
  }
  passwordFormRef.value?.resetFields()
}

const handlePasswordSubmit = () => {
  passwordFormRef.value?.validate(async (valid) => {
    if (valid) {
      try {
        await changePassword({
          userId: userInfo.value.userId,
          oldPassword: passwordForm.value.oldPassword,
          newPassword: passwordForm.value.newPassword
        })
        ElMessage.success('密码修改成功')
        handlePasswordDialogClose()
      } catch (error) {
        ElMessage.error(error.message || '密码修改失败')
      }
    }
  })
}
</script>

<style lang="scss" scoped>
.layout-container {
  height: 100vh;
  
  .el-container {
    height: 100%;
  }
}

.sidebar-container {
  background-color: #304156;
  transition: width 0.28s;
  
  .logo {
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #2b2f3a;
    color: #fff;
    font-size: 16px;
    font-weight: bold;
    
    img {
      width: 32px;
      height: 32px;
      margin-right: 8px;
    }
  }
  
  .sidebar-menu {
    border: none;
    height: calc(100vh - 50px);
    width: 100% !important;
  }
}

.navbar {
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  
  .navbar-left {
    display: flex;
    align-items: center;
    
    .hamburger {
      margin-right: 20px;
      font-size: 18px;
    }
  }
  
  .navbar-right {
    display: flex;
    align-items: center;
    
    .navbar-item {
      margin-left: 20px;
    }
    
    .user-info {
      display: flex;
      align-items: center;
      cursor: pointer;
      
      .user-name {
        margin: 0 8px;
        color: #606266;
      }
    }
  }
}

.main-content {
  background-color: #f0f2f5;
  padding: 20px;
  overflow-y: auto;
}

// 通知抽屉样式
.notification-content {
  .notification-list {
    .notification-item {
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;
      margin-bottom: 12px;
      border-radius: 8px;
      background: #fafafa;

      &:last-child {
        border-bottom: none;
        margin-bottom: 0;
      }

      .notification-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .notification-title {
          font-weight: 600;
          color: #303133;
          font-size: 14px;
        }

        .notification-time {
          color: #909399;
          font-size: 12px;
        }
      }

      .notification-content-text {
        color: #606266;
        font-size: 13px;
        line-height: 1.5;
        margin-bottom: 12px;
      }

      .notification-actions {
        display: flex;
        gap: 8px;
      }
    }
  }

  .notification-footer {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    gap: 12px;
    justify-content: center;
  }
}
</style>
