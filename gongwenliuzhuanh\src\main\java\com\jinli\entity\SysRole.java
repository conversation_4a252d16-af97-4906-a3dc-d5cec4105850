package com.jinli.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 角色实体类
 * 
 * <AUTHOR>
 * @since 2025-01-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYS_ROLE")
public class SysRole implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 角色ID
     */
    @TableId(value = "ROLE_ID", type = IdType.ASSIGN_ID)
    private String roleId;

    /**
     * 角色名称
     */
    @TableField("ROLE_NAME")
    private String roleName;

    /**
     * 角色标识
     */
    @TableField("ROLE_KEY")
    private String roleKey;

    /**
     * 角色描述
     */
    @TableField("ROLE_DESC")
    private String roleDesc;

    /**
     * 角色状态（1正常 0停用）
     */
    @TableField("STATUS")
    private String status;

    /**
     * 显示顺序
     */
    @TableField("SORT_ORDER")
    private Integer sortOrder;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("UPDATE_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 创建者
     */
    @TableField("CREATE_BY")
    private String createBy;

    /**
     * 更新者
     */
    @TableField("UPDATE_BY")
    private String updateBy;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    // 非数据库字段
    /**
     * 权限ID列表
     */
    @TableField(exist = false)
    private List<String> permissionIds;

    /**
     * 权限列表
     */
    @TableField(exist = false)
    private List<String> permissions;

    /**
     * 用户数量
     */
    @TableField(exist = false)
    private Integer userCount;

    // 手动添加getter/setter方法（以防Lombok不工作）
    public String getRoleId() { return roleId; }
    public void setRoleId(String roleId) { this.roleId = roleId; }
    
    public String getRoleName() { return roleName; }
    public void setRoleName(String roleName) { this.roleName = roleName; }
    
    public String getRoleKey() { return roleKey; }
    public void setRoleKey(String roleKey) { this.roleKey = roleKey; }
    
    public String getRoleDesc() { return roleDesc; }
    public void setRoleDesc(String roleDesc) { this.roleDesc = roleDesc; }
    
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    
    public Integer getSortOrder() { return sortOrder; }
    public void setSortOrder(Integer sortOrder) { this.sortOrder = sortOrder; }
    
    public LocalDateTime getCreateTime() { return createTime; }
    public void setCreateTime(LocalDateTime createTime) { this.createTime = createTime; }
    
    public LocalDateTime getUpdateTime() { return updateTime; }
    public void setUpdateTime(LocalDateTime updateTime) { this.updateTime = updateTime; }
    
    public String getCreateBy() { return createBy; }
    public void setCreateBy(String createBy) { this.createBy = createBy; }
    
    public String getUpdateBy() { return updateBy; }
    public void setUpdateBy(String updateBy) { this.updateBy = updateBy; }
    
    public String getRemark() { return remark; }
    public void setRemark(String remark) { this.remark = remark; }
    
    public List<String> getPermissionIds() { return permissionIds; }
    public void setPermissionIds(List<String> permissionIds) { this.permissionIds = permissionIds; }
    
    public List<String> getPermissions() { return permissions; }
    public void setPermissions(List<String> permissions) { this.permissions = permissions; }
    
    public Integer getUserCount() { return userCount; }
    public void setUserCount(Integer userCount) { this.userCount = userCount; }

    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        return "1".equals(status) ? "正常" : "停用";
    }

    /**
     * 检查角色状态是否正常
     */
    public Boolean isNormal() {
        return "1".equals(status);
    }

    /**
     * 检查角色状态是否停用
     */
    public Boolean isDisabled() {
        return "0".equals(status);
    }

    /**
     * 获取格式化的创建时间
     */
    public String getCreateTimeStr() {
        return createTime != null ? createTime.toString() : "";
    }

    /**
     * 获取格式化的更新时间
     */
    public String getUpdateTimeStr() {
        return updateTime != null ? updateTime.toString() : "";
    }

    /**
     * 检查是否为系统管理员角色
     */
    public Boolean isAdmin() {
        return "ADMIN".equals(roleKey);
    }

    /**
     * 检查是否为内置角色
     */
    public Boolean isBuiltIn() {
        return "ADMIN".equals(roleKey) || "USER".equals(roleKey);
    }
}
