<template>
  <div class="document-draft">
    <el-card class="content-card">
      <template #header>
        <div class="card-header">
          <span>公文拟制</span>
          <div class="header-actions">
            <el-button @click="saveDraft" :loading="saving">
              <el-icon><Document /></el-icon>
              保存草稿
            </el-button>
            <el-button type="primary" @click="submitDocument" :loading="submitting">
              <el-icon><Promotion /></el-icon>
              提交审批
            </el-button>
          </div>
        </div>
      </template>

      <el-form :model="documentForm" :rules="formRules" ref="documentFormRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="公文标题" prop="docTitle">
              <el-input 
                v-model="documentForm.docTitle" 
                placeholder="请输入公文标题"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="公文类型" prop="docType">
              <el-select v-model="documentForm.docType" placeholder="请选择公文类型" style="width: 100%">
                <el-option label="通知" value="NOTICE" />
                <el-option label="通报" value="BULLETIN" />
                <el-option label="报告" value="REPORT" />
                <el-option label="请示" value="REQUEST" />
                <el-option label="批复" value="REPLY" />
                <el-option label="意见" value="OPINION" />
                <el-option label="函" value="LETTER" />
                <el-option label="会议纪要" value="MINUTES" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="紧急程度" prop="docLevel">
              <el-select v-model="documentForm.docLevel" placeholder="请选择紧急程度" style="width: 100%">
                <el-option label="普通" value="普通" />
                <el-option label="紧急" value="紧急" />
                <el-option label="特急" value="特急" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="办理期限" prop="deadline">
              <el-date-picker
                v-model="documentForm.deadline"
                type="datetime"
                placeholder="请选择办理期限"
                style="width: 100%"
                :disabled-date="disabledDate"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="公文内容" prop="docContent">
          <div class="editor-container">
            <div class="editor-toolbar">
              <el-button-group>
                <el-button size="small" @click="insertTemplate('notice')">通知模板</el-button>
                <el-button size="small" @click="insertTemplate('report')">报告模板</el-button>
                <el-button size="small" @click="insertTemplate('request')">请示模板</el-button>
              </el-button-group>
            </div>
            <el-input
              v-model="documentForm.docContent"
              type="textarea"
              :rows="15"
              placeholder="请输入公文内容..."
              class="content-editor"
            />
          </div>
        </el-form-item>

        <el-form-item label="附件上传">
          <el-upload
            class="upload-demo"
            drag
            action="#"
            multiple
            :before-upload="beforeUpload"
            :file-list="fileList"
            :on-remove="handleRemove"
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持 doc/docx/pdf/jpg/png 文件，且不超过 10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>

        <el-form-item label="备注">
          <el-input
            v-model="documentForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息（可选）"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 草稿列表 -->
    <el-card class="draft-list-card">
      <template #header>
        <span>我的草稿</span>
      </template>
      
      <el-table :data="draftList" style="width: 100%" v-loading="loadingDrafts">
        <el-table-column prop="docTitle" label="标题" min-width="200" show-overflow-tooltip />
        <el-table-column prop="docType" label="类型" width="100" />
        <el-table-column prop="docLevel" label="紧急程度" width="100">
          <template #default="{ row }">
            <el-tag :type="getLevelType(row.docLevel)" size="small">
              {{ row.docLevel }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间" width="150" />
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="editDraft(row)">
              编辑
            </el-button>
            <el-button type="text" size="small" @click="submitDraft(row)">
              提交
            </el-button>
            <el-button type="text" size="small" @click="deleteDraft(row)" class="danger-text">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document, Promotion, UploadFilled } from '@element-plus/icons-vue'
import {
  getDraftDocuments,
  createDocumentDraft,
  updateDocumentDraft,
  submitDocument,
  submitDraft,
  deleteDraft
} from '@/api/user/workspace'

const router = useRouter()

// 响应式数据
const saving = ref(false)
const submitting = ref(false)
const loadingDrafts = ref(false)
const documentFormRef = ref()

const documentForm = reactive({
  docTitle: '',
  docType: '',
  docLevel: '普通',
  docContent: '',
  deadline: null,
  remark: ''
})

const formRules = {
  docTitle: [
    { required: true, message: '请输入公文标题', trigger: 'blur' },
    { min: 5, max: 200, message: '标题长度在 5 到 200 个字符', trigger: 'blur' }
  ],
  docType: [
    { required: true, message: '请选择公文类型', trigger: 'change' }
  ],
  docLevel: [
    { required: true, message: '请选择紧急程度', trigger: 'change' }
  ],
  docContent: [
    { required: true, message: '请输入公文内容', trigger: 'blur' },
    { min: 10, message: '公文内容至少10个字符', trigger: 'blur' }
  ]
}

const fileList = ref([])
const draftList = ref([])

// 方法
const disabledDate = (time) => {
  return time.getTime() < Date.now() - 8.64e7
}

const beforeUpload = (file) => {
  const allowedTypes = ['application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 
                       'application/pdf', 'image/jpeg', 'image/png']
  const isAllowedType = allowedTypes.includes(file.type)
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isAllowedType) {
    ElMessage.error('只能上传 doc/docx/pdf/jpg/png 格式的文件!')
  }
  if (!isLt10M) {
    ElMessage.error('上传文件大小不能超过 10MB!')
  }
  return isAllowedType && isLt10M
}

const handleRemove = (file, fileList) => {
  console.log('移除文件:', file, fileList)
}

const insertTemplate = (type) => {
  const templates = {
    notice: `各部门：

为了[具体事项]，现将有关事项通知如下：

一、[第一项内容]

二、[第二项内容]

三、[第三项内容]

请各部门认真贯彻执行。

特此通知。

[部门名称]
[日期]`,
    report: `关于[事项]的报告

[领导/部门]：

现将[具体事项]的情况报告如下：

一、基本情况
[详细描述]

二、主要问题
[问题分析]

三、建议措施
[具体建议]

以上报告，请审阅。

[部门名称]
[日期]`,
    request: `关于[事项]的请示

[领导/部门]：

[背景说明]

现请示如下：

一、[请示事项一]

二、[请示事项二]

以上请示，请批示。

[部门名称]
[日期]`
  }
  
  documentForm.docContent = templates[type] || ''
}

const saveDraft = async () => {
  saving.value = true
  try {
    // 这里调用API保存草稿
    // await saveDraftDocument(documentForm)
    
    ElMessage.success('草稿保存成功')
    loadDraftList()
  } catch (error) {
    ElMessage.error('保存草稿失败')
  } finally {
    saving.value = false
  }
}

const submitDocument = async () => {
  if (!documentFormRef.value) return
  
  await documentFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        await ElMessageBox.confirm('确定要提交此公文进行审批吗？', '确认提交', {
          type: 'warning'
        })
        
        // 这里调用API提交公文
        // await submitDocumentForApproval(documentForm)
        
        ElMessage.success('公文提交成功，已进入审批流程')
        resetForm()
        router.push('/user/document/receive')
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('提交失败')
        }
      } finally {
        submitting.value = false
      }
    }
  })
}

const loadDraftList = async () => {
  loadingDrafts.value = true
  try {
    // 这里调用API获取草稿列表
    // const response = await getDraftDocuments()
    // draftList.value = response.data
    
    // 模拟数据
    draftList.value = [
      {
        docId: 'draft1',
        docTitle: '关于设备维护的通知',
        docType: '通知',
        docLevel: '普通',
        updateTime: '2025-01-30 09:30'
      }
    ]
  } catch (error) {
    ElMessage.error('获取草稿列表失败')
  } finally {
    loadingDrafts.value = false
  }
}

const editDraft = (draft) => {
  // 加载草稿内容到表单
  Object.assign(documentForm, draft)
}

const submitDraft = async (draft) => {
  try {
    await ElMessageBox.confirm('确定要提交此草稿进行审批吗？', '确认提交', {
      type: 'warning'
    })
    
    // 这里调用API提交草稿
    // await submitDraftForApproval(draft.docId)
    
    ElMessage.success('草稿提交成功')
    loadDraftList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('提交失败')
    }
  }
}

const deleteDraft = async (draft) => {
  try {
    await ElMessageBox.confirm('确定要删除此草稿吗？删除后无法恢复。', '确认删除', {
      type: 'warning'
    })
    
    // 这里调用API删除草稿
    // await deleteDraftDocument(draft.docId)
    
    ElMessage.success('草稿删除成功')
    loadDraftList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const resetForm = () => {
  Object.assign(documentForm, {
    docTitle: '',
    docType: '',
    docLevel: '普通',
    docContent: '',
    deadline: null,
    remark: ''
  })
  fileList.value = []
}

const getLevelType = (level) => {
  const types = {
    '特急': 'danger',
    '紧急': 'warning',
    '普通': 'info'
  }
  return types[level] || 'info'
}

// 生命周期
onMounted(() => {
  loadDraftList()
})
</script>

<style scoped>
.document-draft {
  padding: 20px;
}

.content-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.editor-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.editor-toolbar {
  padding: 10px;
  border-bottom: 1px solid #dcdfe6;
  background-color: #f5f7fa;
}

.content-editor {
  border: none;
}

.content-editor :deep(.el-textarea__inner) {
  border: none;
  border-radius: 0;
  resize: vertical;
}

.draft-list-card {
  background: white;
}

.danger-text {
  color: #f56c6c;
}

.danger-text:hover {
  color: #f56c6c;
}

.upload-demo {
  width: 100%;
}
</style>
