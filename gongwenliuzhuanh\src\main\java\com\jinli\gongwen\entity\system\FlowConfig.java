package com.jinli.gongwen.entity.system;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 流程配置实体类
 */
@Data
@TableName("sys_flow_config")
public class FlowConfig {

    @TableId(type = IdType.ASSIGN_ID)
    private String configId;

    private String flowName;

    private String flowDescription;

    private String flowSteps;

    private Boolean isEnabled;

    private String remark;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String createUserId;

    private String updateUserId;
}
