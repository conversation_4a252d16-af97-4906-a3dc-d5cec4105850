<template>
  <div class="dashboard">
    <div class="page-header">
      <h1 class="page-title">工作台</h1>
      <span class="welcome-text">欢迎您，{{ userInfo?.realName }}</span>
    </div>
    
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="stat-card pending">
          <div class="stat-icon">
            <el-icon><Clock /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ statistics.pendingCount || 0 }}</div>
            <div class="stat-label">待处理公文</div>
          </div>
        </div>
      </el-col>
      
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="stat-card processed">
          <div class="stat-icon">
            <el-icon><Check /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ statistics.processedCount || 0 }}</div>
            <div class="stat-label">已处理公文</div>
          </div>
        </div>
      </el-col>
      
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="stat-card created">
          <div class="stat-icon">
            <el-icon><EditPen /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ statistics.createdCount || 0 }}</div>
            <div class="stat-label">我创建的</div>
          </div>
        </div>
      </el-col>
      
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="stat-card total">
          <div class="stat-icon">
            <el-icon><Document /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ statistics.totalCount || 0 }}</div>
            <div class="stat-label">公文总数</div>
          </div>
        </div>
      </el-col>
    </el-row>
    
    <!-- 内容区域 -->
    <el-row :gutter="20" class="content-row">
      <!-- 待处理公文列表 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="content-card">
          <template #header>
            <div class="card-header">
              <span>待处理公文</span>
              <el-button type="primary" link @click="$router.push('/document/pending')">
                查看更多 <el-icon><ArrowRight /></el-icon>
              </el-button>
            </div>
          </template>
          
          <div class="document-list">
            <div
              v-for="doc in pendingDocuments"
              :key="doc.docId"
              class="document-item"
              @click="viewDocument(doc.docId)"
            >
              <div class="doc-title">{{ doc.docTitle }}</div>
              <div class="doc-info">
                <span class="doc-type">{{ doc.docTypeName }}</span>
                <span class="doc-time">{{ formatTime(doc.createTime) }}</span>
              </div>
              <div class="doc-status">
                <el-tag :type="getStatusType(doc.docStatus)" size="small">
                  {{ getStatusText(doc.docStatus) }}
                </el-tag>
              </div>
            </div>
            
            <div v-if="pendingDocuments.length === 0" class="empty-state">
              <el-icon><DocumentRemove /></el-icon>
              <p>暂无待处理公文</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 最近公文 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="content-card">
          <template #header>
            <div class="card-header">
              <span>最近公文</span>
              <el-button type="primary" link @click="$router.push('/document/search')">
                查看更多 <el-icon><ArrowRight /></el-icon>
              </el-button>
            </div>
          </template>
          
          <div class="document-list">
            <div
              v-for="doc in recentDocuments"
              :key="doc.docId"
              class="document-item"
              @click="viewDocument(doc.docId)"
            >
              <div class="doc-title">{{ doc.docTitle }}</div>
              <div class="doc-info">
                <span class="doc-creator">{{ doc.createUserName }}</span>
                <span class="doc-time">{{ formatTime(doc.createTime) }}</span>
              </div>
              <div class="doc-status">
                <el-tag :type="getStatusType(doc.docStatus)" size="small">
                  {{ getStatusText(doc.docStatus) }}
                </el-tag>
              </div>
            </div>
            
            <div v-if="recentDocuments.length === 0" class="empty-state">
              <el-icon><DocumentRemove /></el-icon>
              <p>暂无最近公文</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 快捷操作 -->
    <el-row :gutter="20" class="actions-row">
      <el-col :span="24">
        <el-card class="content-card">
          <template #header>
            <span>快捷操作</span>
          </template>
          
          <div class="quick-actions">
            <el-button
              type="primary"
              icon="EditPen"
              @click="$router.push('/document/create')"
            >
              拟制公文
            </el-button>
            
            <el-button
              type="success"
              icon="Search"
              @click="$router.push('/document/search')"
            >
              查询公文
            </el-button>
            
            <el-button
              type="info"
              icon="Clock"
              @click="$router.push('/document/pending')"
            >
              待处理公文
            </el-button>
            
            <el-button
              type="warning"
              icon="User"
              @click="$router.push('/profile')"
            >
              个人中心
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { getPendingDocuments, getDocumentPage, getDocumentStatistics } from '@/api/document'
import dayjs from 'dayjs'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const statistics = ref({})
const pendingDocuments = ref([])
const recentDocuments = ref([])

// 计算属性
const userInfo = computed(() => userStore.userInfo)

// 方法
const loadStatistics = async () => {
  try {
    const response = await getDocumentStatistics({
      userId: userInfo.value?.userId
    })
    if (response.code === 200) {
      statistics.value = response.data
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const loadPendingDocuments = async () => {
  try {
    const response = await getPendingDocuments({
      pageNum: 1,
      pageSize: 5,
      userId: userInfo.value?.userId
    })
    if (response.code === 200) {
      pendingDocuments.value = response.data.records || []
    }
  } catch (error) {
    console.error('加载待处理公文失败:', error)
  }
}

const loadRecentDocuments = async () => {
  try {
    const response = await getDocumentPage({
      pageNum: 1,
      pageSize: 5,
      createUserId: userInfo.value?.userId
    })
    if (response.code === 200) {
      recentDocuments.value = response.data.records || []
    }
  } catch (error) {
    console.error('加载最近公文失败:', error)
  }
}

const viewDocument = (docId) => {
  router.push(`/document/detail/${docId}`)
}

const formatTime = (time) => {
  return dayjs(time).format('MM-DD HH:mm')
}

const getStatusType = (status) => {
  const statusMap = {
    'DRAFT': 'info',
    'PROCESSING': 'warning',
    'APPROVED': 'success',
    'REJECTED': 'danger',
    'COMPLETED': 'primary'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'DRAFT': '草稿',
    'PROCESSING': '处理中',
    'APPROVED': '已通过',
    'REJECTED': '已拒绝',
    'COMPLETED': '已完成'
  }
  return statusMap[status] || status
}

// 生命周期
onMounted(() => {
  loadStatistics()
  loadPendingDocuments()
  loadRecentDocuments()
})
</script>

<style lang="scss" scoped>
.dashboard {
  .page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    
    .welcome-text {
      color: #606266;
      font-size: 14px;
    }
  }
  
  .stats-row {
    margin-bottom: 20px;
  }
  
  .stat-card {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
    }
    
    .stat-icon {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
      font-size: 24px;
      color: #fff;
    }
    
    .stat-content {
      flex: 1;
      
      .stat-number {
        font-size: 24px;
        font-weight: 600;
        color: #303133;
        line-height: 1;
        margin-bottom: 4px;
      }
      
      .stat-label {
        font-size: 14px;
        color: #909399;
      }
    }
    
    &.pending .stat-icon {
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    
    &.processed .stat-icon {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
    
    &.created .stat-icon {
      background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }
    
    &.total .stat-icon {
      background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }
  }
  
  .content-row {
    margin-bottom: 20px;
  }
  
  .content-card {
    height: 400px;
    
    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-weight: 500;
    }
    
    .document-list {
      height: 320px;
      overflow-y: auto;
      
      .document-item {
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
        cursor: pointer;
        transition: all 0.3s;
        
        &:hover {
          background-color: #f5f7fa;
          margin: 0 -12px;
          padding: 12px;
          border-radius: 4px;
        }
        
        &:last-child {
          border-bottom: none;
        }
        
        .doc-title {
          font-size: 14px;
          color: #303133;
          margin-bottom: 8px;
          font-weight: 500;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        
        .doc-info {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 8px;
          
          .doc-type,
          .doc-creator {
            font-size: 12px;
            color: #909399;
          }
          
          .doc-time {
            font-size: 12px;
            color: #c0c4cc;
          }
        }
        
        .doc-status {
          text-align: right;
        }
      }
      
      .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 200px;
        color: #c0c4cc;
        
        .el-icon {
          font-size: 48px;
          margin-bottom: 16px;
        }
        
        p {
          margin: 0;
          font-size: 14px;
        }
      }
    }
  }
  
  .actions-row {
    .quick-actions {
      display: flex;
      gap: 16px;
      flex-wrap: wrap;
      
      .el-button {
        flex: 1;
        min-width: 120px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dashboard {
    .stat-card {
      margin-bottom: 16px;
    }
    
    .content-card {
      height: auto;
      margin-bottom: 16px;
      
      .document-list {
        height: auto;
        max-height: 300px;
      }
    }
    
    .quick-actions {
      .el-button {
        width: 100%;
        margin-bottom: 8px;
      }
    }
  }
}
</style>
