import request from '@/utils/request'

// 获取部门经理仪表板数据
export function getDeptManagerDashboard() {
  return request({
    url: '/api/dept/manager/dashboard',
    method: 'get'
  })
}

// 获取待签收公文列表
export function getPendingReceiveDocuments(params) {
  return request({
    url: '/api/dept/manager/pendingReceive',
    method: 'get',
    params
  })
}

// 获取本部门公文列表
export function getDeptDocuments(params) {
  return request({
    url: '/api/dept/manager/documents',
    method: 'get',
    params
  })
}

// 获取部门统计信息
export function getDeptStatistics() {
  return request({
    url: '/api/dept/manager/statistics',
    method: 'get'
  })
}
