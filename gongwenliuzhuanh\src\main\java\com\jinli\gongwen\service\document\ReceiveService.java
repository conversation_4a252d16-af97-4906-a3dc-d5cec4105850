package com.jinli.gongwen.service.document;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinli.gongwen.dto.document.DocumentReceiveDTO;

import java.util.List;

/**
 * 签收服务接口
 */
public interface ReceiveService {

    /**
     * 签收公文
     */
    void receiveDocument(DocumentReceiveDTO receiveDTO);

    /**
     * 批量签收公文
     */
    void batchReceiveDocuments(List<String> docIds, String userId, String deptId);

    /**
     * 获取签收记录
     */
    IPage<Object> getReceiveRecords(Page<Object> page, String deptId);
}
