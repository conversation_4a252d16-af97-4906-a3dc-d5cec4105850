package com.jinli.gongwen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 公文签收记录表
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
@ApiModel(description = "公文签收记录")
@TableName("DOC_RECEIVE_RECORD")
public class DocReceiveRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("签收ID")
    @TableId(value = "RECEIVE_ID", type = IdType.ASSIGN_UUID)
    private String receiveId;

    @ApiModelProperty("公文ID")
    @TableField("DOC_ID")
    private String docId;

    @ApiModelProperty("签收人ID")
    @TableField("RECEIVE_USER_ID")
    private String receiveUserId;

    @ApiModelProperty("签收部门ID")
    @TableField("RECEIVE_DEPT_ID")
    private String receiveDeptId;

    @ApiModelProperty("签收时间")
    @TableField("RECEIVE_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime receiveTime;

    @ApiModelProperty("阅读时间")
    @TableField("READ_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime readTime;

    @ApiModelProperty("状态")
    @TableField("STATUS")
    private String status;

    @ApiModelProperty("备注")
    @TableField("REMARK")
    private String remark;

    // 非数据库字段
    @ApiModelProperty("签收人姓名")
    @TableField(exist = false)
    private String receiveUserName;

    @ApiModelProperty("签收部门名称")
    @TableField(exist = false)
    private String receiveDeptName;

    // 构造方法
    public DocReceiveRecord() {}

    // Getter和Setter方法
    public String getReceiveId() {
        return receiveId;
    }

    public void setReceiveId(String receiveId) {
        this.receiveId = receiveId;
    }

    public String getDocId() {
        return docId;
    }

    public void setDocId(String docId) {
        this.docId = docId;
    }

    public String getReceiveUserId() {
        return receiveUserId;
    }

    public void setReceiveUserId(String receiveUserId) {
        this.receiveUserId = receiveUserId;
    }

    public String getReceiveDeptId() {
        return receiveDeptId;
    }

    public void setReceiveDeptId(String receiveDeptId) {
        this.receiveDeptId = receiveDeptId;
    }

    public LocalDateTime getReceiveTime() {
        return receiveTime;
    }

    public void setReceiveTime(LocalDateTime receiveTime) {
        this.receiveTime = receiveTime;
    }

    public LocalDateTime getReadTime() {
        return readTime;
    }

    public void setReadTime(LocalDateTime readTime) {
        this.readTime = readTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getReceiveUserName() {
        return receiveUserName;
    }

    public void setReceiveUserName(String receiveUserName) {
        this.receiveUserName = receiveUserName;
    }

    public String getReceiveDeptName() {
        return receiveDeptName;
    }

    public void setReceiveDeptName(String receiveDeptName) {
        this.receiveDeptName = receiveDeptName;
    }

    @Override
    public String toString() {
        return "DocReceiveRecord{" +
                "receiveId='" + receiveId + '\'' +
                ", docId='" + docId + '\'' +
                ", receiveUserId='" + receiveUserId + '\'' +
                ", receiveDeptId='" + receiveDeptId + '\'' +
                ", receiveTime=" + receiveTime +
                ", readTime=" + readTime +
                ", status='" + status + '\'' +
                ", remark='" + remark + '\'' +
                '}';
    }
}
