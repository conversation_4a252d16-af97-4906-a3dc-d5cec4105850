<template>
  <div class="login-container">
    <div class="login-form">
      <div class="login-header">
        <img src="/logo.svg" alt="Logo" class="logo">
        <h2 class="title">河北金力集团公文流转系统</h2>
        <p class="subtitle">Document Management System</p>
      </div>
      
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form-content"
        @keyup.enter="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            size="large"
            prefix-icon="User"
            clearable
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            prefix-icon="Lock"
            show-password
            clearable
          />
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            :loading="loading"
            @click="handleLogin"
            class="login-button"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="login-tips">
        <h4>测试账号：</h4>
        <div class="tips-content">
          <div class="tip-item">
            <span class="role">系统管理员：</span>
            <span class="account">admin / 123456</span>
          </div>
          <div class="tip-item">
            <span class="role">厂长：</span>
            <span class="account">director / 123456</span>
          </div>
          <div class="tip-item">
            <span class="role">副厂长：</span>
            <span class="account">vice_prod / 123456</span>
          </div>
          <div class="tip-item">
            <span class="role">办公室：</span>
            <span class="account">office / 123456</span>
          </div>
          <div class="tip-item">
            <span class="role">部门用户：</span>
            <span class="account">main_user / 123456</span>
          </div>
        </div>
      </div>
    </div>
    
    <div class="login-footer">
      <p>&copy; 2025 河北金力集团. All rights reserved.</p>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import { setToken } from '@/utils/auth'
import { getDefaultHomePath } from '@/utils/permission'
import axios from 'axios'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const loginFormRef = ref()

// 登录表单
const loginForm = reactive({
  username: '',
  password: ''
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 20, message: '用户名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}

// 登录处理
const handleLogin = () => {
  loginFormRef.value?.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        // 打印登录表单数据，便于调试
        console.log('登录表单数据:', loginForm)

        // 使用fetch API发送请求
        const response = await fetch('http://localhost:8080/api/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          body: JSON.stringify({
            username: loginForm.username,
            password: loginForm.password
          }),
          mode: 'cors'
        });

        const data = await response.json();
        console.log('登录响应:', data);

        if (data.code === 200) {
          // 登录成功，保存token和用户信息
          const { token, userInfo } = data.data;
          setToken(token);
          userStore.$patch({
            token,
            userInfo
          });

          ElMessage.success('登录成功');

          // 根据用户角色跳转到对应的首页
          const defaultPath = getDefaultHomePath(userInfo.roleKey);
          router.push(defaultPath);
        } else {
          ElMessage.error(data.message || '登录失败');
        }
      } catch (error) {
        console.error('登录失败:', error)
        ElMessage.error('登录失败，请检查网络连接或服务器状态')
      } finally {
        loading.value = false
      }
    }
  })
}

// 快速登录
const quickLogin = (username, password) => {
  loginForm.username = username
  loginForm.password = password
  handleLogin()
}
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/bg-pattern.svg') repeat;
    opacity: 0.1;
    z-index: 0;
  }
}

.login-form {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 400px;
  position: relative;
  z-index: 1;
  
  .login-header {
    text-align: center;
    margin-bottom: 30px;
    
    .logo {
      width: 64px;
      height: 64px;
      margin-bottom: 16px;
    }
    
    .title {
      color: #303133;
      font-size: 24px;
      font-weight: 600;
      margin: 0 0 8px 0;
    }
    
    .subtitle {
      color: #909399;
      font-size: 14px;
      margin: 0;
    }
  }
  
  .login-form-content {
    .el-form-item {
      margin-bottom: 24px;
    }
    
    .login-button {
      width: 100%;
      height: 48px;
      font-size: 16px;
      font-weight: 500;
      border-radius: 8px;
    }
  }
  
  .login-tips {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #ebeef5;
    
    h4 {
      color: #606266;
      font-size: 14px;
      margin: 0 0 12px 0;
    }
    
    .tips-content {
      .tip-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 4px 0;
        font-size: 12px;
        
        .role {
          color: #909399;
        }
        
        .account {
          color: #409eff;
          cursor: pointer;
          
          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }
}

.login-footer {
  position: absolute;
  bottom: 20px;
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  z-index: 1;
  
  p {
    margin: 0;
  }
}

// 响应式设计
@media (max-width: 480px) {
  .login-container {
    padding: 10px;
  }
  
  .login-form {
    padding: 30px 20px;
    
    .login-header {
      .title {
        font-size: 20px;
      }
    }
  }
}

// 动画效果
.login-form {
  animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
