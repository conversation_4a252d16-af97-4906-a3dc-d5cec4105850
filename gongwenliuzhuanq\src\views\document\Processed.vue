<template>
  <div class="processed-documents">
    <div class="page-header">
      <h1 class="page-title">已处理公文</h1>
      <div class="page-actions">
        <el-button type="primary" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="公文标题">
          <el-input
            v-model="searchForm.title"
            placeholder="请输入公文标题"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        
        <el-form-item label="处理时间">
          <el-date-picker
            v-model="searchForm.processTime"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="docId" label="公文编号" width="120" />
        
        <el-table-column prop="docTitle" label="公文标题" min-width="200" show-overflow-tooltip />
        
        <el-table-column prop="docType" label="公文类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getDocTypeColor(row.docType)" size="small">
              {{ getDocTypeName(row.docType) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="createUserName" label="发起人" width="100" />
        
        <el-table-column prop="processTime" label="处理时间" width="160">
          <template #default="{ row }">
            {{ formatTime(row.processTime) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="processResult" label="处理结果" width="100">
          <template #default="{ row }">
            <el-tag :type="getResultColor(row.processResult)" size="small">
              {{ row.processResult }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="processComment" label="处理意见" min-width="150" show-overflow-tooltip />
        
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="info" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button type="primary" size="small" @click="handleDetail(row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getDocumentPage } from '@/api/document'
import dayjs from 'dayjs'
import { Refresh, Search } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const tableData = ref([])

// 搜索表单
const searchForm = reactive({
  title: '',
  processTime: null
})

// 分页数据
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 方法
const formatTime = (timestamp) => {
  return timestamp ? dayjs(timestamp).format('YYYY-MM-DD HH:mm') : '-'
}

const getDocTypeName = (type) => {
  const typeMap = {
    'NOTICE': '通知',
    'REQUEST': '请示',
    'REPORT': '报告',
    'OPINION': '意见'
  }
  return typeMap[type] || type
}

const getDocTypeColor = (type) => {
  const colorMap = {
    'NOTICE': 'primary',
    'REQUEST': 'warning',
    'REPORT': 'success',
    'OPINION': 'info'
  }
  return colorMap[type] || 'info'
}

const getResultColor = (result) => {
  const colorMap = {
    '同意': 'success',
    '不同意': 'danger',
    '需修改': 'warning',
    '已阅': 'info'
  }
  return colorMap[result] || 'info'
}

const loadData = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: pagination.current,
      pageSize: pagination.size,
      status: 'PROCESSED',
      ...searchForm
    }
    
    const response = await getDocumentPage(params)
    if (response.code === 200) {
      // 模拟已处理公文数据
      const processedData = (response.data.records || []).map(item => ({
        ...item,
        processTime: Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000,
        processResult: ['同意', '不同意', '需修改', '已阅'][Math.floor(Math.random() * 4)],
        processComment: ['同意该方案，请按计划执行', '建议进一步完善细节', '内容详实，予以通过', '已知悉，请继续跟进'][Math.floor(Math.random() * 4)]
      }))
      
      tableData.value = processedData
      pagination.total = response.data.total || 0
    }
  } catch (error) {
    console.error('加载已处理公文失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  loadData()
}

const handleSearch = () => {
  pagination.current = 1
  loadData()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    title: '',
    processTime: null
  })
  pagination.current = 1
  loadData()
}

const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  loadData()
}

const handleCurrentChange = (current) => {
  pagination.current = current
  loadData()
}

const handleView = (row) => {
  ElMessage.info(`查看公文: ${row.docTitle}`)
}

const handleDetail = (row) => {
  ElMessage.info(`查看详情: ${row.docTitle}`)
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.processed-documents {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .page-title {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .search-card {
    margin-bottom: 20px;
    
    .search-form {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }
  
  .table-card {
    .pagination-container {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }
  }
}
</style>
