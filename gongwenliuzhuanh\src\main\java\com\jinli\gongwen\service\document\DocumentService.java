package com.jinli.gongwen.service.document;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinli.gongwen.dto.document.DocumentDraftDTO;
import com.jinli.gongwen.dto.document.DocumentEditDTO;
import com.jinli.gongwen.dto.document.DocumentSendDTO;
import com.jinli.gongwen.dto.document.SimpleDocumentDTO;
import com.jinli.gongwen.entity.document.DocDocument;
import com.jinli.gongwen.vo.dept.DeptDashboardVO;
import com.jinli.gongwen.vo.director.DirectorDashboardVO;
import com.jinli.gongwen.vo.document.DocumentVO;
import com.jinli.gongwen.vo.employee.EmployeeDashboardVO;
import com.jinli.gongwen.vo.office.OfficeDashboardVO;
import com.jinli.gongwen.vo.vice.ViceDashboardVO;

import java.util.List;

/**
 * 公文服务接口
 */
public interface DocumentService {

    /**
     * 获取公文列表
     */
    IPage<DocumentVO> getDocumentList(Page<DocDocument> page, QueryWrapper<DocDocument> queryWrapper);

    /**
     * 根据ID获取公文详情
     */
    DocumentVO getDocumentById(String docId);

    /**
     * 编辑公文
     */
    void editDocument(DocumentEditDTO editDTO);

    /**
     * 发送公文
     */
    void sendDocument(DocumentSendDTO sendDTO);

    /**
     * 批量发送公文
     */
    void batchSendDocuments(List<DocumentSendDTO> sendDTOList);

    /**
     * 删除公文
     */
    void deleteDocument(String docId);

    /**
     * 创建草稿
     */
    String createDraft(DocumentDraftDTO draftDTO);

    /**
     * 更新草稿
     */
    void updateDraft(DocumentDraftDTO draftDTO);

    /**
     * 提交到办公室
     */
    void submitToOffice(String docId, String submitRemark);

    /**
     * 删除草稿
     */
    void deleteDraft(String docId, String userId);

    /**
     * 创建简单草稿
     */
    String createSimpleDraft(SimpleDocumentDTO simpleDocumentDTO);

    /**
     * 获取办公室主任仪表板
     */
    OfficeDashboardVO getOfficeDashboard();

    /**
     * 获取部门经理仪表板
     */
    DeptDashboardVO getDeptManagerDashboard(String userId, String deptId);

    /**
     * 获取副厂长仪表板
     */
    ViceDashboardVO getViceDirectorDashboard(String userId, List<String> managedDeptIds);

    /**
     * 获取厂长仪表板
     */
    DirectorDashboardVO getDirectorDashboard(String userId);

    /**
     * 获取员工仪表板
     */
    EmployeeDashboardVO getEmployeeDashboard(String userId, String deptId);

    /**
     * 获取公文模板
     */
    List<Object> getDocumentTemplates(String deptId);

    /**
     * 获取简化公文模板
     */
    List<Object> getSimpleDocumentTemplates();

    /**
     * 获取部门统计
     */
    Object getDeptStatistics(String deptId);

    /**
     * 获取副厂长部门统计
     */
    Object getViceDeptStatistics(String userId, List<String> managedDeptIds);

    /**
     * 获取公司公文概览
     */
    Object getCompanyDocumentOverview();

    /**
     * 获取决策支持数据
     */
    Object getDecisionSupportData();

    /**
     * 获取发送目标
     */
    List<Object> getSendTargets();

    /**
     * 获取发送记录
     */
    IPage<Object> getSendRecords(Page<Object> page);

    /**
     * 获取员工待办事项
     */
    List<Object> getEmployeeTodoList(String userId);

    /**
     * 获取员工通知
     */
    IPage<Object> getEmployeeNotifications(Page<Object> page, String userId);

    /**
     * 标记通知为已读
     */
    void markNotificationAsRead(String notificationId, String userId);

    /**
     * 获取员工工作统计
     */
    Object getEmployeeWorkStatistics(String userId);

    /**
     * 提交员工协助意见
     */
    void submitEmployeeAssistance(String docId, String userId, String assistance);
}
