package com.jinli.gongwen.vo.office;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 办公室主任仪表板视图对象
 */
@Data
@ApiModel("办公室主任仪表板视图对象")
public class OfficeDashboardVO {

    @ApiModelProperty("待处理公文数量")
    private Integer pendingCount;

    @ApiModelProperty("今日处理公文数量")
    private Integer todayProcessed;

    @ApiModelProperty("本月处理公文数量")
    private Integer monthProcessed;

    @ApiModelProperty("待编辑公文数量")
    private Integer editPendingCount;

    @ApiModelProperty("待发送公文数量")
    private Integer sendPendingCount;

    @ApiModelProperty("流转中公文数量")
    private Integer flowingCount;

    @ApiModelProperty("已完成公文数量")
    private Integer completedCount;

    @ApiModelProperty("公文类型统计")
    private Map<String, Integer> docTypeStats;

    @ApiModelProperty("公文级别统计")
    private Map<String, Integer> docLevelStats;

    @ApiModelProperty("部门处理效率统计")
    private Map<String, Double> deptEfficiencyStats;

    @ApiModelProperty("最近处理的公文列表")
    private List<RecentDocumentVO> recentDocuments;

    @ApiModelProperty("流转状态统计")
    private Map<String, Integer> flowStatusStats;

    @ApiModelProperty("本周处理趋势")
    private List<DailyProcessVO> weeklyTrend;

    /**
     * 最近处理公文视图对象
     */
    @Data
    @ApiModel("最近处理公文视图对象")
    public static class RecentDocumentVO {
        @ApiModelProperty("公文ID")
        private String docId;

        @ApiModelProperty("公文标题")
        private String docTitle;

        @ApiModelProperty("公文类型")
        private String docType;

        @ApiModelProperty("当前状态")
        private String currentStatus;

        @ApiModelProperty("处理时间")
        private String processTime;

        @ApiModelProperty("创建部门")
        private String createDept;
    }

    /**
     * 每日处理统计视图对象
     */
    @Data
    @ApiModel("每日处理统计视图对象")
    public static class DailyProcessVO {
        @ApiModelProperty("日期")
        private String date;

        @ApiModelProperty("处理数量")
        private Integer count;

        @ApiModelProperty("完成数量")
        private Integer completed;
    }
}
