<template>
  <div class="document-create">
    <div class="page-header">
      <h1 class="page-title">拟制公文</h1>
      <div class="header-actions">
        <el-button @click="$router.back()">返回</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          保存草稿
        </el-button>
        <el-button type="success" @click="handleSubmit" :loading="submitting">
          提交审核
        </el-button>
      </div>
    </div>
    
    <div class="form-container">
      <el-form
        ref="documentFormRef"
        :model="documentForm"
        :rules="documentRules"
        label-width="100px"
        class="document-form"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="公文标题" prop="docTitle">
              <el-input
                v-model="documentForm.docTitle"
                placeholder="请输入公文标题"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="公文类型" prop="docType">
              <el-select
                v-model="documentForm.docType"
                placeholder="请选择公文类型"
                style="width: 100%"
              >
                <el-option
                  v-for="type in docTypes"
                  :key="type.value"
                  :label="type.label"
                  :value="type.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="紧急程度" prop="docLevel">
              <el-select
                v-model="documentForm.docLevel"
                placeholder="请选择紧急程度"
                style="width: 100%"
              >
                <el-option label="普通" value="普通" />
                <el-option label="紧急" value="紧急" />
                <el-option label="特急" value="特急" />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="办理期限" prop="deadline">
              <el-date-picker
                v-model="documentForm.deadline"
                type="datetime"
                placeholder="请选择办理期限"
                style="width: 100%"
                :disabled-date="disabledDate"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="公文内容" prop="docContent">
          <el-input
            v-model="documentForm.docContent"
            type="textarea"
            :rows="12"
            placeholder="请输入公文内容"
            maxlength="5000"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="附件上传">
          <el-upload
            ref="uploadRef"
            :action="uploadAction"
            :headers="uploadHeaders"
            :file-list="fileList"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :on-remove="handleFileRemove"
            :before-upload="beforeUpload"
            multiple
            drag
          >
            <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持 doc/docx/pdf/txt/xls/xlsx/ppt/pptx/jpg/jpeg/png/gif 格式，单个文件不超过10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input
            v-model="documentForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息（可选）"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { createDocument, submitDocument } from '@/api/document'
import { getToken } from '@/utils/auth'
import { ElMessage, ElMessageBox } from 'element-plus'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const documentFormRef = ref()
const uploadRef = ref()
const saving = ref(false)
const submitting = ref(false)
const fileList = ref([])

// 表单数据
const documentForm = reactive({
  docTitle: '',
  docType: '',
  docLevel: '普通',
  docContent: '',
  deadline: null,
  remark: '',
  createUserId: '',
  createDeptId: ''
})

// 表单验证规则
const documentRules = {
  docTitle: [
    { required: true, message: '请输入公文标题', trigger: 'blur' },
    { min: 5, max: 200, message: '标题长度在 5 到 200 个字符', trigger: 'blur' }
  ],
  docType: [
    { required: true, message: '请选择公文类型', trigger: 'change' }
  ],
  docLevel: [
    { required: true, message: '请选择紧急程度', trigger: 'change' }
  ],
  docContent: [
    { required: true, message: '请输入公文内容', trigger: 'blur' },
    { min: 10, max: 5000, message: '内容长度在 10 到 5000 个字符', trigger: 'blur' }
  ]
}

// 公文类型选项
const docTypes = ref([
  { label: '通知', value: 'NOTICE' },
  { label: '通报', value: 'BULLETIN' },
  { label: '决定', value: 'DECISION' },
  { label: '指示', value: 'INSTRUCTION' },
  { label: '报告', value: 'REPORT' },
  { label: '请示', value: 'REQUEST' },
  { label: '批复', value: 'REPLY' },
  { label: '意见', value: 'OPINION' },
  { label: '函', value: 'LETTER' },
  { label: '会议纪要', value: 'MINUTES' }
])

// 计算属性
const uploadAction = computed(() => '/api/file/upload')
const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${getToken()}`
}))

// 方法
const disabledDate = (time) => {
  return time.getTime() < Date.now() - 8.64e7 // 不能选择今天之前的日期
}

const beforeUpload = (file) => {
  const allowedTypes = ['doc', 'docx', 'pdf', 'txt', 'xls', 'xlsx', 'ppt', 'pptx', 'jpg', 'jpeg', 'png', 'gif']
  const fileExtension = file.name.split('.').pop().toLowerCase()
  
  if (!allowedTypes.includes(fileExtension)) {
    ElMessage.error('不支持的文件格式')
    return false
  }
  
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB')
    return false
  }
  
  return true
}

const handleUploadSuccess = (response, file) => {
  if (response.code === 200) {
    ElMessage.success('文件上传成功')
  } else {
    ElMessage.error(response.message || '文件上传失败')
  }
}

const handleUploadError = (error) => {
  ElMessage.error('文件上传失败')
  console.error('Upload error:', error)
}

const handleFileRemove = (file) => {
  // 处理文件删除逻辑
}

const handleSave = () => {
  documentFormRef.value?.validate(async (valid) => {
    if (valid) {
      saving.value = true
      try {
        const formData = {
          ...documentForm,
          docStatus: 'DRAFT',
          createUserId: userStore.userInfo?.userId,
          createDeptId: userStore.userInfo?.departmentId
        }
        
        const response = await createDocument(formData)
        if (response.code === 200) {
          ElMessage.success('公文保存成功')
          router.push('/document/pending')
        }
      } catch (error) {
        ElMessage.error(error.message || '保存失败')
      } finally {
        saving.value = false
      }
    }
  })
}

const handleSubmit = () => {
  documentFormRef.value?.validate(async (valid) => {
    if (valid) {
      ElMessageBox.confirm(
        '确定要提交此公文进入审核流程吗？提交后将无法修改。',
        '确认提交',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        submitting.value = true
        try {
          // 先保存公文
          const formData = {
            ...documentForm,
            docStatus: 'DRAFT',
            createUserId: userStore.userInfo?.userId,
            createDeptId: userStore.userInfo?.departmentId
          }
          
          const createResponse = await createDocument(formData)
          if (createResponse.code === 200) {
            // 再提交审核
            const submitResponse = await submitDocument(
              createResponse.data.docId,
              userStore.userInfo?.userId
            )
            
            if (submitResponse.code === 200) {
              ElMessage.success('公文提交成功')
              router.push('/document/pending')
            }
          }
        } catch (error) {
          ElMessage.error(error.message || '提交失败')
        } finally {
          submitting.value = false
        }
      })
    }
  })
}

// 生命周期
onMounted(() => {
  // 初始化表单数据
  documentForm.createUserId = userStore.userInfo?.userId
  documentForm.createDeptId = userStore.userInfo?.departmentId
})
</script>

<style lang="scss" scoped>
.document-create {
  .page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e4e7ed;
    
    .header-actions {
      .el-button + .el-button {
        margin-left: 10px;
      }
    }
  }
  
  .form-container {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 30px;
    
    .document-form {
      .el-form-item {
        margin-bottom: 24px;
      }
      
      .el-textarea {
        :deep(.el-textarea__inner) {
          font-family: inherit;
          line-height: 1.6;
        }
      }
      
      .el-upload {
        width: 100%;
        
        :deep(.el-upload-dragger) {
          width: 100%;
          height: 120px;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .document-create {
    .page-header {
      flex-direction: column;
      align-items: flex-start;
      
      .header-actions {
        margin-top: 12px;
        width: 100%;
        
        .el-button {
          width: calc(33.33% - 7px);
        }
      }
    }
    
    .form-container {
      padding: 20px;
    }
  }
}
</style>
