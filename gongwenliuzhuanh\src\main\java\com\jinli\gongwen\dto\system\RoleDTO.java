package com.jinli.gongwen.dto.system;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 角色数据传输对象
 */
@Data
@ApiModel("角色数据传输对象")
public class RoleDTO {

    @ApiModelProperty("角色ID")
    private String roleId;

    @ApiModelProperty(value = "角色名称", required = true)
    @NotBlank(message = "角色名称不能为空")
    @Size(max = 50, message = "角色名称长度不能超过50个字符")
    private String roleName;

    @ApiModelProperty(value = "角色编码", required = true)
    @NotBlank(message = "角色编码不能为空")
    @Size(max = 50, message = "角色编码长度不能超过50个字符")
    private String roleCode;

    @ApiModelProperty("角色描述")
    @Size(max = 200, message = "角色描述长度不能超过200个字符")
    private String roleDescription;

    @ApiModelProperty("是否启用")
    private Boolean isEnabled = true;

    @ApiModelProperty("排序")
    private Integer sortOrder;

    @ApiModelProperty("菜单ID列表")
    private List<String> menuIds;

    @ApiModelProperty("备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;
}
