package com.jinli.gongwen.controller;

import com.jinli.gongwen.common.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
@Api(tags = "系统测试")
@RestController
@RequestMapping("/api/test")
@CrossOrigin(origins = "*", allowCredentials = "false")
public class TestController {

    @Autowired
    private DataSource dataSource;

    @ApiOperation("健康检查")
    @GetMapping("/health")
    public Result<Map<String, Object>> health() {
        Map<String, Object> data = new HashMap<>();
        data.put("status", "UP");
        data.put("timestamp", LocalDateTime.now());
        data.put("message", "河北金力集团公文流转系统运行正常");
        return Result.success(data);
    }

    @ApiOperation("系统信息")
    @GetMapping("/info")
    public Result<Map<String, Object>> info() {
        Map<String, Object> data = new HashMap<>();
        data.put("name", "河北金力集团公文流转系统");
        data.put("version", "1.0.0");
        data.put("author", "jinli");
        data.put("description", "基于Spring Boot + Vue.js的公文流转管理系统");
        return Result.success(data);
    }

    @ApiOperation("数据库连接测试")
    @GetMapping("/db")
    public Result<Map<String, Object>> testDatabase() {
        Map<String, Object> data = new HashMap<>();
        
        try (Connection connection = dataSource.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            
            data.put("status", "SUCCESS");
            data.put("databaseProductName", metaData.getDatabaseProductName());
            data.put("databaseProductVersion", metaData.getDatabaseProductVersion());
            data.put("driverName", metaData.getDriverName());
            data.put("driverVersion", metaData.getDriverVersion());
            data.put("url", metaData.getURL());
            data.put("userName", metaData.getUserName());
            data.put("message", "达梦数据库连接成功");
            
            return Result.success(data);
        } catch (Exception e) {
            data.put("status", "ERROR");
            data.put("message", "数据库连接失败: " + e.getMessage());
            data.put("error", e.getClass().getSimpleName());
            
            return Result.error("数据库连接失败", data);
        }
    }
}
