/**
 * 权限验证工具
 * 确保系统管理员与业务用户的权限边界明确
 */

import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'

/**
 * 系统管理员专属路径
 */
const ADMIN_ONLY_PATHS = [
  '/system/user',
  '/system/role', 
  '/system/department',
  '/system/log',
  '/system/config',
  '/system/monitor'
]

/**
 * 业务功能路径（系统管理员不能访问）
 */
const BUSINESS_PATHS = [
  '/dashboard',
  '/document',
  '/statistics',
  '/management'
]

/**
 * 公共路径（所有用户都可以访问）
 */
const PUBLIC_PATHS = [
  '/profile',
  '/login',
  '/404',
  '/403'
]

/**
 * 检查系统管理员是否试图访问业务功能
 * @param {string} path - 要访问的路径
 * @param {string} roleKey - 用户角色标识
 * @returns {boolean} 是否有权限访问
 */
export const checkAdminBusinessAccess = (path, roleKey) => {
  // 如果不是系统管理员，直接返回true
  if (roleKey !== 'ADMIN') {
    return true
  }
  
  // 检查是否是业务路径
  const isBusinessPath = BUSINESS_PATHS.some(businessPath => 
    path.startsWith(businessPath)
  )
  
  if (isBusinessPath) {
    ElMessage.error('系统管理员无权访问业务功能模块')
    return false
  }
  
  return true
}

/**
 * 检查业务用户是否试图访问系统管理功能
 * @param {string} path - 要访问的路径
 * @param {string} roleKey - 用户角色标识
 * @returns {boolean} 是否有权限访问
 */
export const checkBusinessUserSystemAccess = (path, roleKey) => {
  // 如果是系统管理员，直接返回true
  if (roleKey === 'ADMIN') {
    return true
  }
  
  // 检查是否是系统管理专属路径
  const isAdminOnlyPath = ADMIN_ONLY_PATHS.some(adminPath => 
    path.startsWith(adminPath)
  )
  
  if (isAdminOnlyPath) {
    ElMessage.error('您无权访问系统管理功能')
    return false
  }
  
  return true
}

/**
 * 综合权限检查
 * @param {string} path - 要访问的路径
 * @param {string} roleKey - 用户角色标识
 * @returns {boolean} 是否有权限访问
 */
export const checkPermission = (path, roleKey) => {
  // 检查是否是公共路径
  const isPublicPath = PUBLIC_PATHS.some(publicPath => 
    path.startsWith(publicPath)
  )
  
  if (isPublicPath) {
    return true
  }
  
  // 检查系统管理员访问业务功能
  if (!checkAdminBusinessAccess(path, roleKey)) {
    return false
  }
  
  // 检查业务用户访问系统管理功能
  if (!checkBusinessUserSystemAccess(path, roleKey)) {
    return false
  }
  
  return true
}

/**
 * 获取用户默认首页路径
 * @param {string} roleKey - 用户角色标识
 * @returns {string} 默认首页路径
 */
export const getDefaultHomePath = (roleKey) => {
  if (roleKey === 'ADMIN') {
    return '/system/user'  // 系统管理员默认进入用户管理
  }
  
  return '/dashboard'  // 业务用户默认进入工作台
}

/**
 * 路由守卫中使用的权限检查
 * @param {Object} to - 目标路由
 * @param {Object} from - 来源路由
 * @param {Function} next - 路由跳转函数
 */
export const routePermissionGuard = (to, from, next) => {
  const userStore = useUserStore()
  const roleKey = userStore.userInfo?.roleKey
  
  if (!roleKey) {
    // 未登录，跳转到登录页
    next('/login')
    return
  }
  
  // 检查权限
  if (!checkPermission(to.path, roleKey)) {
    // 无权限，跳转到默认首页
    const defaultPath = getDefaultHomePath(roleKey)
    next(defaultPath)
    return
  }
  
  next()
}

/**
 * 菜单项权限过滤
 * @param {Array} menuItems - 菜单项数组
 * @param {string} roleKey - 用户角色标识
 * @returns {Array} 过滤后的菜单项
 */
export const filterMenuByPermission = (menuItems, roleKey) => {
  return menuItems.filter(item => {
    // 如果有路径，检查权限
    if (item.path) {
      return checkPermission(item.path, roleKey)
    }
    
    // 如果有子菜单，递归过滤
    if (item.children) {
      item.children = filterMenuByPermission(item.children, roleKey)
      return item.children.length > 0
    }
    
    return true
  })
}

/**
 * 检查用户是否有特定功能的权限
 * @param {string} feature - 功能标识
 * @param {string} roleKey - 用户角色标识
 * @returns {boolean} 是否有权限
 */
export const hasFeaturePermission = (feature, roleKey) => {
  const featurePermissions = {
    // 系统管理功能
    'user_management': ['ADMIN'],
    'role_management': ['ADMIN'],
    'department_management': ['ADMIN'],
    'system_log': ['ADMIN'],
    'system_config': ['ADMIN'],
    'system_monitor': ['ADMIN'],
    
    // 业务功能
    'document_management': ['FACTORY_DIRECTOR', 'VICE_DIRECTOR_PROD', 'VICE_DIRECTOR_TECH', 'VICE_DIRECTOR_SAFETY', 'SALES_DIRECTOR', 'FINANCE_DIRECTOR', 'OFFICE_DIRECTOR', 'FACTORY_MANAGER'],
    'document_approval': ['FACTORY_DIRECTOR', 'VICE_DIRECTOR_PROD', 'VICE_DIRECTOR_TECH', 'VICE_DIRECTOR_SAFETY'],
    'statistics': ['FACTORY_DIRECTOR', 'VICE_DIRECTOR_PROD', 'VICE_DIRECTOR_TECH', 'VICE_DIRECTOR_SAFETY'],
    'management_overview': ['FACTORY_DIRECTOR']
  }
  
  const allowedRoles = featurePermissions[feature] || []
  return allowedRoles.includes(roleKey)
}

/**
 * 角色权限级别定义
 */
export const ROLE_LEVELS = {
  'ADMIN': 0,                    // 系统级
  'FACTORY_DIRECTOR': 1,         // 最高级
  'VICE_DIRECTOR_PROD': 2,       // 高级
  'VICE_DIRECTOR_TECH': 2,       // 高级
  'VICE_DIRECTOR_SAFETY': 2,     // 高级
  'SALES_DIRECTOR': 3,           // 中级
  'FINANCE_DIRECTOR': 3,         // 中级
  'OFFICE_DIRECTOR': 3,          // 中级
  'FACTORY_MANAGER': 4           // 基础级
}

/**
 * 检查角色权限级别
 * @param {string} currentRole - 当前用户角色
 * @param {string} targetRole - 目标角色
 * @returns {boolean} 是否有权限操作目标角色
 */
export const checkRoleLevel = (currentRole, targetRole) => {
  const currentLevel = ROLE_LEVELS[currentRole] || 999
  const targetLevel = ROLE_LEVELS[targetRole] || 999
  
  // 系统管理员可以管理所有业务角色，但不能管理其他系统管理员
  if (currentRole === 'ADMIN') {
    return targetRole !== 'ADMIN'
  }
  
  // 其他角色只能管理比自己级别低的角色
  return currentLevel < targetLevel
}
