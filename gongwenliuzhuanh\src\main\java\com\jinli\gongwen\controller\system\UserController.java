package com.jinli.gongwen.controller.system;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinli.gongwen.common.Result;
import com.jinli.gongwen.dto.system.UserDTO;
import com.jinli.gongwen.dto.system.UserQueryDTO;
import com.jinli.gongwen.entity.system.SysUser;
import com.jinli.gongwen.service.system.UserService;
import com.jinli.gongwen.vo.system.UserVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 用户管理控制器
 * 系统管理员专用接口
 */
@Api(tags = "用户管理")
@RestController
@RequestMapping("/api/system/user")
@PreAuthorize("hasRole('ADMIN')")
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * 用户列表查询（分页）
     */
    @ApiOperation("用户列表查询")
    @GetMapping("/list")
    public Result<IPage<UserVO>> list(UserQueryDTO queryDTO) {
        Page<SysUser> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
        
        // 构建查询条件
        if (queryDTO.getUsername() != null && !queryDTO.getUsername().trim().isEmpty()) {
            queryWrapper.like("username", queryDTO.getUsername().trim());
        }
        if (queryDTO.getRealName() != null && !queryDTO.getRealName().trim().isEmpty()) {
            queryWrapper.like("real_name", queryDTO.getRealName().trim());
        }
        if (queryDTO.getDepartmentId() != null && !queryDTO.getDepartmentId().trim().isEmpty()) {
            queryWrapper.eq("department_id", queryDTO.getDepartmentId());
        }
        if (queryDTO.getStatus() != null && !queryDTO.getStatus().trim().isEmpty()) {
            queryWrapper.eq("status", queryDTO.getStatus());
        }
        
        queryWrapper.orderByDesc("create_time");
        
        IPage<UserVO> result = userService.getUserList(page, new UserQueryDTO());
        return Result.success(result);
    }

    /**
     * 获取用户详情
     */
    @ApiOperation("获取用户详情")
    @GetMapping("/{userId}")
    public Result<UserVO> getUser(@PathVariable String userId) {
        UserVO userVO = userService.getUserById(userId);
        return Result.success(userVO);
    }

    /**
     * 新增用户
     */
    @ApiOperation("新增用户")
    @PostMapping
    public Result<Void> add(@Valid @RequestBody UserDTO userDTO) {
        userService.createUser(userDTO);
        return Result.success();
    }

    /**
     * 修改用户
     */
    @ApiOperation("修改用户")
    @PutMapping("/{userId}")
    public Result<Void> update(@PathVariable String userId, @Valid @RequestBody UserDTO userDTO) {
        userService.updateUser(userId, userDTO);
        return Result.success();
    }

    /**
     * 删除用户（逻辑删除）
     */
    @ApiOperation("删除用户")
    @DeleteMapping("/{userId}")
    public Result<Void> delete(@PathVariable String userId) {
        userService.deleteUser(userId);
        return Result.success();
    }

    /**
     * 启用/停用用户
     */
    @ApiOperation("启用/停用用户")
    @PutMapping("/{userId}/status")
    public Result<Void> changeStatus(@PathVariable String userId, @RequestParam Boolean isEnabled) {
        userService.toggleUserStatus(userId, isEnabled);
        return Result.success();
    }

    /**
     * 重置用户密码
     */
    @ApiOperation("重置用户密码")
    @PutMapping("/{userId}/resetPassword")
    public Result<Void> resetPassword(@PathVariable String userId, @RequestParam String newPassword) {
        userService.resetPassword(userId, newPassword);
        return Result.success();
    }

    /**
     * 分配用户角色
     */
    @ApiOperation("分配用户角色")
    @PutMapping("/{userId}/roles")
    public Result<Void> assignRoles(@PathVariable String userId, @RequestBody List<String> roleIds) {
        userService.assignUserRoles(userId, roleIds);
        return Result.success();
    }

    /**
     * 获取用户角色列表
     */
    @ApiOperation("获取用户角色列表")
    @GetMapping("/{userId}/roles")
    public Result<List<String>> getUserRoles(@PathVariable String userId) {
        List<String> roleIds = userService.getUserRoles(userId);
        return Result.success(roleIds);
    }
}
