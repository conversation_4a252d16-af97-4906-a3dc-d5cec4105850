/**
 * 基于角色的菜单配置 - 重构版
 */

// 系统管理员菜单 - 优化版（仅系统管理功能）
export const adminMenus = [
  {
    id: 'system',
    title: '系统管理',
    icon: 'Setting',
    children: [
      {
        id: 'user',
        title: '用户管理',
        path: '/system/user',
        icon: 'User'
      },
      {
        id: 'role',
        title: '角色管理',
        path: '/system/role',
        icon: 'UserFilled'
      },
      {
        id: 'department',
        title: '部门管理',
        path: '/system/department',
        icon: 'OfficeBuilding'
      },
      {
        id: 'log',
        title: '系统日志',
        path: '/system/log',
        icon: 'Document'
      },
      {
        id: 'config',
        title: '系统配置',
        path: '/system/config',
        icon: 'Tools'
      },
      {
        id: 'monitor',
        title: '系统监控',
        path: '/system/monitor',
        icon: 'Monitor'
      }
    ]
  },
  {
    id: 'profile',
    title: '个人中心',
    path: '/profile',
    icon: 'User'
  }
]

// 厂长菜单
export const factoryDirectorMenus = [
  {
    id: 'dashboard',
    title: '工作台',
    path: '/dashboard',
    icon: 'Monitor'
  },
  {
    id: 'document',
    title: '公文管理',
    icon: 'Document',
    children: [
      {
        id: 'pending',
        title: '待处理公文',
        path: '/document/pending',
        icon: 'Clock'
      },
      {
        id: 'approval',
        title: '公文审批',
        path: '/document/approval',
        icon: 'Check'
      },
      {
        id: 'all-documents',
        title: '全部公文',
        path: '/document/all',
        icon: 'FolderOpened'
      },
      {
        id: 'create',
        title: '拟制公文',
        path: '/document/create',
        icon: 'EditPen'
      }
    ]
  },
  {
    id: 'statistics',
    title: '统计报表',
    path: '/statistics',
    icon: 'DataAnalysis'
  },
  {
    id: 'management',
    title: '管理监督',
    icon: 'Monitor',
    children: [
      {
        id: 'department-overview',
        title: '部门概览',
        path: '/management/departments',
        icon: 'OfficeBuilding'
      },
      {
        id: 'performance',
        title: '绩效监控',
        path: '/management/performance',
        icon: 'TrendCharts'
      }
    ]
  },
  {
    id: 'profile',
    title: '个人中心',
    path: '/profile',
    icon: 'User'
  }
]

// 副厂长菜单（生产、技术、安全）
export const viceDirectorMenus = [
  {
    id: 'dashboard',
    title: '工作台',
    path: '/dashboard',
    icon: 'Monitor'
  },
  {
    id: 'document',
    title: '公文管理',
    icon: 'Document',
    children: [
      {
        id: 'pending',
        title: '待处理公文',
        path: '/document/pending',
        icon: 'Clock'
      },
      {
        id: 'approval',
        title: '公文审批',
        path: '/document/approval',
        icon: 'Check'
      },
      {
        id: 'department-list',
        title: '部门公文',
        path: '/document/department',
        icon: 'FolderOpened'
      },
      {
        id: 'create',
        title: '拟制公文',
        path: '/document/create',
        icon: 'EditPen'
      }
    ]
  },
  {
    id: 'statistics',
    title: '统计报表',
    path: '/statistics',
    icon: 'DataAnalysis'
  },
  {
    id: 'profile',
    title: '个人中心',
    path: '/profile',
    icon: 'User'
  }
]

// 主任级菜单（销售、财务、办公室）
export const directorMenus = [
  {
    id: 'dashboard',
    title: '工作台',
    path: '/dashboard',
    icon: 'Monitor'
  },
  {
    id: 'document',
    title: '公文管理',
    icon: 'Document',
    children: [
      {
        id: 'pending',
        title: '待处理公文',
        path: '/document/pending',
        icon: 'Clock'
      },
      {
        id: 'department-list',
        title: '部门公文',
        path: '/document/department',
        icon: 'FolderOpened'
      },
      {
        id: 'create',
        title: '拟制公文',
        path: '/document/create',
        icon: 'EditPen'
      }
    ]
  },
  {
    id: 'profile',
    title: '个人中心',
    path: '/profile',
    icon: 'User'
  }
]

// 分厂厂长和部门主任菜单
export const userWorkspaceMenus = [
  {
    id: 'workspace',
    title: '工作台',
    path: '/user/workspace',
    icon: 'House'
  },
  {
    id: 'document-draft',
    title: '公文拟制',
    path: '/user/document/draft',
    icon: 'EditPen'
  },
  {
    id: 'document-receive',
    title: '签收公文',
    path: '/user/document/receive',
    icon: 'Finished'
  },
  {
    id: 'document-browse',
    title: '浏览公文',
    path: '/user/document/browse',
    icon: 'Document'
  },
  {
    id: 'profile',
    title: '个人中心',
    path: '/profile',
    icon: 'User'
  }
]

/**
 * 根据角色获取菜单配置
 * @param {string} roleKey - 角色标识
 * @returns {Array} 菜单配置数组
 */
export const getMenusByRole = (roleKey) => {
  const menuMap = {
    'ADMIN': adminMenus,
    'FACTORY_DIRECTOR': factoryDirectorMenus,
    'VICE_DIRECTOR_PROD': viceDirectorMenus,
    'VICE_DIRECTOR_TECH': viceDirectorMenus,
    'VICE_DIRECTOR_SAFETY': viceDirectorMenus,
    'SALES_DIRECTOR': userWorkspaceMenus,
    'FINANCE_DIRECTOR': userWorkspaceMenus,
    'OFFICE_DIRECTOR': directorMenus,
    'FACTORY_MANAGER': userWorkspaceMenus
  }

  return menuMap[roleKey] || []
}

/**
 * 检查用户是否有访问某个路径的权限
 * @param {string} path - 路径
 * @param {string} roleKey - 角色标识
 * @returns {boolean} 是否有权限
 */
export const hasPermission = (path, roleKey) => {
  const menus = getMenusByRole(roleKey)
  
  const checkPath = (menuItems, targetPath) => {
    for (const item of menuItems) {
      if (item.path === targetPath) {
        return true
      }
      if (item.children && checkPath(item.children, targetPath)) {
        return true
      }
    }
    return false
  }
  
  return checkPath(menus, path)
}
