package com.jinli.gongwen.controller.director;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinli.gongwen.common.Result;
import com.jinli.gongwen.dto.document.DocumentApprovalDTO;
import com.jinli.gongwen.dto.document.DocumentQueryDTO;
import com.jinli.gongwen.entity.document.DocDocument;
import com.jinli.gongwen.service.document.ApprovalService;
import com.jinli.gongwen.service.document.DocumentService;
import com.jinli.gongwen.vo.director.DirectorDashboardVO;
import com.jinli.gongwen.vo.document.DocumentVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 厂长控制器
 * 负责公文最终审批功能
 */
@Api(tags = "厂长功能")
@RestController
@RequestMapping("/api/director")
@PreAuthorize("hasRole('DIRECTOR')")
public class DirectorController {

    @Autowired
    private DocumentService documentService;

    @Autowired
    private ApprovalService approvalService;

    /**
     * 厂长仪表板
     */
    @ApiOperation("厂长仪表板")
    @GetMapping("/dashboard")
    public Result<DirectorDashboardVO> getDashboard() {
        DirectorDashboardVO dashboard = documentService.getDirectorDashboard(getCurrentUserId());
        return Result.success(dashboard);
    }

    /**
     * 获取待审签公文列表
     */
    @ApiOperation("获取待审签公文列表")
    @GetMapping("/pendingApproval")
    public Result<IPage<DocumentVO>> getPendingApprovalDocuments(DocumentQueryDTO queryDTO) {
        Page<DocDocument> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        QueryWrapper<DocDocument> queryWrapper = new QueryWrapper<>();
        
        // 查询待厂长审签的公文
        queryWrapper.eq("doc_status", "DIRECTOR_APPROVE");
        queryWrapper.eq("current_handler", getCurrentUserId());
        
        // 构建查询条件
        if (queryDTO.getDocTitle() != null && !queryDTO.getDocTitle().trim().isEmpty()) {
            queryWrapper.like("doc_title", queryDTO.getDocTitle().trim());
        }
        if (queryDTO.getDocType() != null && !queryDTO.getDocType().trim().isEmpty()) {
            queryWrapper.eq("doc_type", queryDTO.getDocType());
        }
        if (queryDTO.getDocLevel() != null && !queryDTO.getDocLevel().trim().isEmpty()) {
            queryWrapper.eq("doc_level", queryDTO.getDocLevel());
        }
        if (queryDTO.getCreateDeptId() != null && !queryDTO.getCreateDeptId().trim().isEmpty()) {
            queryWrapper.eq("create_dept_id", queryDTO.getCreateDeptId());
        }
        
        queryWrapper.orderByDesc("create_time");
        
        IPage<DocumentVO> result = documentService.getDocumentList(page, queryWrapper);
        return Result.success(result);
    }

    /**
     * 获取已审签公文列表
     */
    @ApiOperation("获取已审签公文列表")
    @GetMapping("/approved")
    public Result<IPage<DocumentVO>> getApprovedDocuments(DocumentQueryDTO queryDTO) {
        Page<DocDocument> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        QueryWrapper<DocDocument> queryWrapper = new QueryWrapper<>();
        
        // 查询厂长已审签的公文
        queryWrapper.exists("SELECT 1 FROM doc_flow_record dfr WHERE dfr.doc_id = doc_document.doc_id " +
                           "AND dfr.from_user_id = '" + getCurrentUserId() + "' " +
                           "AND dfr.action IN ('APPROVE', 'REJECT')");
        
        // 构建查询条件
        if (queryDTO.getDocTitle() != null && !queryDTO.getDocTitle().trim().isEmpty()) {
            queryWrapper.like("doc_title", queryDTO.getDocTitle().trim());
        }
        if (queryDTO.getDocType() != null && !queryDTO.getDocType().trim().isEmpty()) {
            queryWrapper.eq("doc_type", queryDTO.getDocType());
        }
        if (queryDTO.getApprovalResult() != null && !queryDTO.getApprovalResult().trim().isEmpty()) {
            queryWrapper.eq("approval_result", queryDTO.getApprovalResult());
        }
        if (queryDTO.getStartTime() != null && !queryDTO.getStartTime().trim().isEmpty()) {
            queryWrapper.ge("create_time", queryDTO.getStartTime());
        }
        if (queryDTO.getEndTime() != null && !queryDTO.getEndTime().trim().isEmpty()) {
            queryWrapper.le("create_time", queryDTO.getEndTime());
        }
        
        queryWrapper.orderByDesc("update_time");
        
        IPage<DocumentVO> result = documentService.getDocumentList(page, queryWrapper);
        return Result.success(result);
    }

    /**
     * 审签公文
     */
    @ApiOperation("审签公文")
    @PostMapping("/approve/{docId}")
    public Result<Void> approveDocument(@PathVariable String docId, @Valid @RequestBody DocumentApprovalDTO approvalDTO) {
        approvalDTO.setDocId(docId);
        approvalDTO.setApprovalUserId(getCurrentUserId());
        approvalService.approveDocument(approvalDTO);
        return Result.success();
    }

    /**
     * 获取公文详情
     */
    @ApiOperation("获取公文详情")
    @GetMapping("/document/{docId}")
    public Result<DocumentVO> getDocument(@PathVariable String docId) {
        DocumentVO documentVO = documentService.getDocumentById(docId);
        return Result.success(documentVO);
    }

    /**
     * 批量审签公文
     */
    @ApiOperation("批量审签公文")
    @PostMapping("/batchApprove")
    public Result<Void> batchApproveDocuments(@RequestBody List<DocumentApprovalDTO> approvalDTOList) {
        for (DocumentApprovalDTO approvalDTO : approvalDTOList) {
            approvalDTO.setApprovalUserId(getCurrentUserId());
        }
        approvalService.batchApproveDocuments(approvalDTOList);
        return Result.success();
    }

    /**
     * 获取所有已发公文（厂长具有最高查看权限）
     */
    @ApiOperation("获取所有已发公文")
    @GetMapping("/allDocuments")
    public Result<IPage<DocumentVO>> getAllDocuments(DocumentQueryDTO queryDTO) {
        Page<DocDocument> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        QueryWrapper<DocDocument> queryWrapper = new QueryWrapper<>();
        
        // 厂长可以查看所有公文（除草稿外）
        queryWrapper.ne("doc_status", "DRAFT");
        
        // 构建查询条件
        if (queryDTO.getDocTitle() != null && !queryDTO.getDocTitle().trim().isEmpty()) {
            queryWrapper.like("doc_title", queryDTO.getDocTitle().trim());
        }
        if (queryDTO.getDocType() != null && !queryDTO.getDocType().trim().isEmpty()) {
            queryWrapper.eq("doc_type", queryDTO.getDocType());
        }
        if (queryDTO.getDocStatus() != null && !queryDTO.getDocStatus().trim().isEmpty()) {
            queryWrapper.eq("doc_status", queryDTO.getDocStatus());
        }
        if (queryDTO.getCreateDeptId() != null && !queryDTO.getCreateDeptId().trim().isEmpty()) {
            queryWrapper.eq("create_dept_id", queryDTO.getCreateDeptId());
        }
        if (queryDTO.getStartTime() != null && !queryDTO.getStartTime().trim().isEmpty()) {
            queryWrapper.ge("create_time", queryDTO.getStartTime());
        }
        if (queryDTO.getEndTime() != null && !queryDTO.getEndTime().trim().isEmpty()) {
            queryWrapper.le("create_time", queryDTO.getEndTime());
        }
        
        queryWrapper.orderByDesc("create_time");
        
        IPage<DocumentVO> result = documentService.getDocumentList(page, queryWrapper);
        return Result.success(result);
    }

    /**
     * 获取审签统计信息
     */
    @ApiOperation("获取审签统计信息")
    @GetMapping("/statistics")
    public Result<Object> getApprovalStatistics() {
        Object statistics = approvalService.getApprovalStatistics(getCurrentUserId());
        return Result.success(statistics);
    }

    /**
     * 获取审签历史记录
     */
    @ApiOperation("获取审签历史记录")
    @GetMapping("/approvalHistory")
    public Result<IPage<Object>> getApprovalHistory(DocumentQueryDTO queryDTO) {
        Page<Object> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        IPage<Object> result = approvalService.getApprovalHistory(page, getCurrentUserId());
        return Result.success(result);
    }

    /**
     * 获取全公司公文流转概览
     */
    @ApiOperation("获取全公司公文流转概览")
    @GetMapping("/companyOverview")
    public Result<Object> getCompanyOverview() {
        Object overview = documentService.getCompanyDocumentOverview();
        return Result.success(overview);
    }

    /**
     * 退回公文
     */
    @ApiOperation("退回公文")
    @PostMapping("/return/{docId}")
    public Result<Void> returnDocument(@PathVariable String docId, @RequestParam String reason) {
        approvalService.returnDocument(docId, reason, getCurrentUserId());
        return Result.success();
    }

    /**
     * 获取决策支持数据
     */
    @ApiOperation("获取决策支持数据")
    @GetMapping("/decisionSupport")
    public Result<Object> getDecisionSupportData() {
        Object data = documentService.getDecisionSupportData();
        return Result.success(data);
    }

    /**
     * 获取当前用户ID（从安全上下文中获取）
     */
    private String getCurrentUserId() {
        // 从Spring Security上下文中获取当前用户ID
        // 这里简化处理，实际应该从SecurityContextHolder中获取
        return "USER002"; // 厂长的用户ID
    }
}
