package com.jinli.gongwen.dto.system;

import com.jinli.gongwen.common.BaseQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户查询数据传输对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("用户查询数据传输对象")
public class UserQueryDTO extends BaseQueryDTO {

    @ApiModelProperty("用户名")
    private String username;

    @ApiModelProperty("真实姓名")
    private String realName;

    @ApiModelProperty("部门ID")
    private String departmentId;

    @ApiModelProperty("状态（1正常 0停用）")
    private String status;

    @ApiModelProperty("开始时间")
    private String startTime;

    @ApiModelProperty("结束时间")
    private String endTime;
}
