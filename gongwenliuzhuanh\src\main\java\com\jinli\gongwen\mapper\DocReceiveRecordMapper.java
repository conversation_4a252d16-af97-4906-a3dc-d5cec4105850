package com.jinli.gongwen.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jinli.gongwen.entity.DocReceiveRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 公文签收记录 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
@Mapper
public interface DocReceiveRecordMapper extends BaseMapper<DocReceiveRecord> {

    /**
     * 根据公文ID查询签收记录
     *
     * @param docId 公文ID
     * @return 签收记录列表
     */
    @Select("SELECT rr.*, " +
            "u.REAL_NAME as RECEIVE_USER_NAME, " +
            "d.DEPT_NAME as RECEIVE_DEPT_NAME " +
            "FROM DOC_RECEIVE_RECORD rr " +
            "LEFT JOIN SYS_USER u ON rr.RECEIVE_USER_ID = u.USER_ID " +
            "LEFT JOIN SYS_DEPARTMENT d ON rr.RECEIVE_DEPT_ID = d.DEPT_ID " +
            "WHERE rr.DOC_ID = #{docId} " +
            "ORDER BY rr.RECEIVE_TIME DESC")
    List<DocReceiveRecord> selectReceiveRecordsByDocId(@Param("docId") String docId);

    /**
     * 根据用户ID查询签收记录
     *
     * @param userId 用户ID
     * @return 签收记录列表
     */
    @Select("SELECT rr.*, " +
            "u.REAL_NAME as RECEIVE_USER_NAME, " +
            "d.DEPT_NAME as RECEIVE_DEPT_NAME " +
            "FROM DOC_RECEIVE_RECORD rr " +
            "LEFT JOIN SYS_USER u ON rr.RECEIVE_USER_ID = u.USER_ID " +
            "LEFT JOIN SYS_DEPARTMENT d ON rr.RECEIVE_DEPT_ID = d.DEPT_ID " +
            "WHERE rr.RECEIVE_USER_ID = #{userId} " +
            "ORDER BY rr.RECEIVE_TIME DESC")
    List<DocReceiveRecord> selectReceiveRecordsByUserId(@Param("userId") String userId);

    /**
     * 根据公文ID和用户ID查询签收记录
     *
     * @param docId  公文ID
     * @param userId 用户ID
     * @return 签收记录
     */
    @Select("SELECT rr.*, " +
            "u.REAL_NAME as RECEIVE_USER_NAME, " +
            "d.DEPT_NAME as RECEIVE_DEPT_NAME " +
            "FROM DOC_RECEIVE_RECORD rr " +
            "LEFT JOIN SYS_USER u ON rr.RECEIVE_USER_ID = u.USER_ID " +
            "LEFT JOIN SYS_DEPARTMENT d ON rr.RECEIVE_DEPT_ID = d.DEPT_ID " +
            "WHERE rr.DOC_ID = #{docId} AND rr.RECEIVE_USER_ID = #{userId}")
    DocReceiveRecord selectReceiveRecord(@Param("docId") String docId, @Param("userId") String userId);

    /**
     * 统计用户签收的公文数量
     *
     * @param userId 用户ID
     * @return 签收数量
     */
    @Select("SELECT COUNT(*) FROM DOC_RECEIVE_RECORD WHERE RECEIVE_USER_ID = #{userId}")
    Long countReceivedDocuments(@Param("userId") String userId);

    /**
     * 统计用户未读的公文数量
     *
     * @param userId 用户ID
     * @return 未读数量
     */
    @Select("SELECT COUNT(*) FROM DOC_RECEIVE_RECORD WHERE RECEIVE_USER_ID = #{userId} AND STATUS = '0'")
    Long countUnreadDocuments(@Param("userId") String userId);
}
