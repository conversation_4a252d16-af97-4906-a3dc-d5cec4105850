-- =====================================================
-- 金利科技有限公司组织架构重构SQL脚本
-- 数据库：达梦数据库
-- 执行顺序：按照脚本顺序执行
-- =====================================================

-- 1. 清理现有数据（保留系统管理员）
-- =====================================================

-- 删除用户角色关联（保留系统管理员）
DELETE FROM SYS_USER_ROLE WHERE USER_ID != 'USER001';

-- 删除普通用户（保留系统管理员）
DELETE FROM SYS_USER WHERE USER_ID != 'USER001';

-- 删除旧角色（保留系统管理员角色）
DELETE FROM SYS_ROLE WHERE ROLE_KEY != 'ADMIN';

-- 删除旧部门（保留公司根部门）
DELETE FROM SYS_DEPARTMENT WHERE DEPT_ID != 'DEPT001';

-- 2. 创建新的角色体系
-- =====================================================

-- 厂长角色
INSERT INTO SYS_ROLE (ROLE_ID, ROLE_NAME, ROLE_KEY, DESCRIPTION, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('ROLE002', '厂长', 'FACTORY_DIRECTOR', '公司最高管理者，负责全面管理', 1, SYSDATE, SYSDATE, '厂长角色，拥有最高决策权');

-- 生产副厂长角色
INSERT INTO SYS_ROLE (ROLE_ID, ROLE_NAME, ROLE_KEY, DESCRIPTION, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('ROLE003', '生产副厂长', 'VICE_DIRECTOR_PROD', '负责生产管理和协调', 1, SYSDATE, SYSDATE, '生产副厂长，负责生产相关事务');

-- 技术副厂长角色
INSERT INTO SYS_ROLE (ROLE_ID, ROLE_NAME, ROLE_KEY, DESCRIPTION, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('ROLE004', '技术副厂长', 'VICE_DIRECTOR_TECH', '负责技术管理和创新', 1, SYSDATE, SYSDATE, '技术副厂长，负责技术相关事务');

-- 安全副厂长角色
INSERT INTO SYS_ROLE (ROLE_ID, ROLE_NAME, ROLE_KEY, DESCRIPTION, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('ROLE005', '安全副厂长', 'VICE_DIRECTOR_SAFETY', '负责安全管理和监督', 1, SYSDATE, SYSDATE, '安全副厂长，负责安全相关事务');

-- 销售主任角色
INSERT INTO SYS_ROLE (ROLE_ID, ROLE_NAME, ROLE_KEY, DESCRIPTION, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('ROLE006', '销售主任', 'SALES_DIRECTOR', '负责销售管理和市场开拓', 1, SYSDATE, SYSDATE, '销售主任，负责销售相关事务');

-- 财务主任角色
INSERT INTO SYS_ROLE (ROLE_ID, ROLE_NAME, ROLE_KEY, DESCRIPTION, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('ROLE007', '财务主任', 'FINANCE_DIRECTOR', '负责财务管理和资金监督', 1, SYSDATE, SYSDATE, '财务主任，负责财务相关事务');

-- 办公室主任角色
INSERT INTO SYS_ROLE (ROLE_ID, ROLE_NAME, ROLE_KEY, DESCRIPTION, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('ROLE008', '办公室主任', 'OFFICE_DIRECTOR', '负责行政管理和综合协调', 1, SYSDATE, SYSDATE, '办公室主任，负责行政相关事务');

-- 分厂厂长角色
INSERT INTO SYS_ROLE (ROLE_ID, ROLE_NAME, ROLE_KEY, DESCRIPTION, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('ROLE009', '分厂厂长', 'FACTORY_MANAGER', '负责分厂日常管理', 1, SYSDATE, SYSDATE, '分厂厂长，负责分厂管理');

-- 3. 创建新的部门结构
-- =====================================================

-- 更新公司根部门信息
UPDATE SYS_DEPARTMENT SET 
    DEPT_NAME = '金利科技有限公司',
    DEPT_CODE = 'JINLI_TECH',
    LEADER = '厂长',
    PHONE = '010-12345678',
    EMAIL = '<EMAIL>',
    UPDATE_TIME = SYSDATE
WHERE DEPT_ID = 'DEPT001';

-- 厂长办公室
INSERT INTO SYS_DEPARTMENT (DEPT_ID, DEPT_NAME, DEPT_CODE, PARENT_ID, LEADER, PHONE, EMAIL, SORT, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('DEPT010', '厂长办公室', 'DIRECTOR_OFFICE', 'DEPT001', '厂长', '010-12345679', '<EMAIL>', 1, 1, SYSDATE, SYSDATE, '厂长办公室');

-- 生产管理部
INSERT INTO SYS_DEPARTMENT (DEPT_ID, DEPT_NAME, DEPT_CODE, PARENT_ID, LEADER, PHONE, EMAIL, SORT, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('DEPT011', '生产管理部', 'PRODUCTION_DEPT', 'DEPT001', '生产副厂长', '010-12345680', '<EMAIL>', 2, 1, SYSDATE, SYSDATE, '生产管理部门');

-- 技术管理部
INSERT INTO SYS_DEPARTMENT (DEPT_ID, DEPT_NAME, DEPT_CODE, PARENT_ID, LEADER, PHONE, EMAIL, SORT, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('DEPT012', '技术管理部', 'TECH_DEPT', 'DEPT001', '技术副厂长', '010-12345681', '<EMAIL>', 3, 1, SYSDATE, SYSDATE, '技术管理部门');

-- 安全管理部
INSERT INTO SYS_DEPARTMENT (DEPT_ID, DEPT_NAME, DEPT_CODE, PARENT_ID, LEADER, PHONE, EMAIL, SORT, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('DEPT013', '安全管理部', 'SAFETY_DEPT', 'DEPT001', '安全副厂长', '010-12345682', '<EMAIL>', 4, 1, SYSDATE, SYSDATE, '安全管理部门');

-- 销售部
INSERT INTO SYS_DEPARTMENT (DEPT_ID, DEPT_NAME, DEPT_CODE, PARENT_ID, LEADER, PHONE, EMAIL, SORT, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('DEPT014', '销售部', 'SALES_DEPT', 'DEPT001', '销售主任', '010-12345683', '<EMAIL>', 5, 1, SYSDATE, SYSDATE, '销售部门');

-- 财务部
INSERT INTO SYS_DEPARTMENT (DEPT_ID, DEPT_NAME, DEPT_CODE, PARENT_ID, LEADER, PHONE, EMAIL, SORT, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('DEPT015', '财务部', 'FINANCE_DEPT', 'DEPT001', '财务主任', '010-12345684', '<EMAIL>', 6, 1, SYSDATE, SYSDATE, '财务部门');

-- 办公室
INSERT INTO SYS_DEPARTMENT (DEPT_ID, DEPT_NAME, DEPT_CODE, PARENT_ID, LEADER, PHONE, EMAIL, SORT, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('DEPT016', '办公室', 'OFFICE_DEPT', 'DEPT001', '办公室主任', '010-12345685', '<EMAIL>', 7, 1, SYSDATE, SYSDATE, '办公室部门');

-- 一分厂
INSERT INTO SYS_DEPARTMENT (DEPT_ID, DEPT_NAME, DEPT_CODE, PARENT_ID, LEADER, PHONE, EMAIL, SORT, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('DEPT017', '一分厂', 'FACTORY_ONE', 'DEPT011', '一分厂厂长', '010-12345686', '<EMAIL>', 1, 1, SYSDATE, SYSDATE, '第一分厂');

-- 二分厂
INSERT INTO SYS_DEPARTMENT (DEPT_ID, DEPT_NAME, DEPT_CODE, PARENT_ID, LEADER, PHONE, EMAIL, SORT, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('DEPT018', '二分厂', 'FACTORY_TWO', 'DEPT011', '二分厂厂长', '010-12345687', '<EMAIL>', 2, 1, SYSDATE, SYSDATE, '第二分厂');

-- 三分厂
INSERT INTO SYS_DEPARTMENT (DEPT_ID, DEPT_NAME, DEPT_CODE, PARENT_ID, LEADER, PHONE, EMAIL, SORT, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('DEPT019', '三分厂', 'FACTORY_THREE', 'DEPT011', '三分厂厂长', '010-12345688', '<EMAIL>', 3, 1, SYSDATE, SYSDATE, '第三分厂');

-- 4. 创建新用户
-- =====================================================

-- 厂长
INSERT INTO SYS_USER (USER_ID, USERNAME, PASSWORD, REAL_NAME, EMAIL, PHONE, DEPARTMENT_ID, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('USER010', 'factory_director', '123456', '张厂长', '<EMAIL>', '13800000010', 'DEPT010', 1, SYSDATE, SYSDATE, '厂长账号');

-- 生产副厂长
INSERT INTO SYS_USER (USER_ID, USERNAME, PASSWORD, REAL_NAME, EMAIL, PHONE, DEPARTMENT_ID, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('USER011', 'vice_director_prod', '123456', '李副厂长', '<EMAIL>', '13800000011', 'DEPT011', 1, SYSDATE, SYSDATE, '生产副厂长账号');

-- 技术副厂长
INSERT INTO SYS_USER (USER_ID, USERNAME, PASSWORD, REAL_NAME, EMAIL, PHONE, DEPARTMENT_ID, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('USER012', 'vice_director_tech', '123456', '王副厂长', '<EMAIL>', '13800000012', 'DEPT012', 1, SYSDATE, SYSDATE, '技术副厂长账号');

-- 安全副厂长
INSERT INTO SYS_USER (USER_ID, USERNAME, PASSWORD, REAL_NAME, EMAIL, PHONE, DEPARTMENT_ID, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('USER013', 'vice_director_safety', '123456', '赵副厂长', '<EMAIL>', '13800000013', 'DEPT013', 1, SYSDATE, SYSDATE, '安全副厂长账号');

-- 销售主任
INSERT INTO SYS_USER (USER_ID, USERNAME, PASSWORD, REAL_NAME, EMAIL, PHONE, DEPARTMENT_ID, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('USER014', 'sales_director', '123456', '刘主任', '<EMAIL>', '13800000014', 'DEPT014', 1, SYSDATE, SYSDATE, '销售主任账号');

-- 财务主任
INSERT INTO SYS_USER (USER_ID, USERNAME, PASSWORD, REAL_NAME, EMAIL, PHONE, DEPARTMENT_ID, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('USER015', 'finance_director', '123456', '陈主任', '<EMAIL>', '13800000015', 'DEPT015', 1, SYSDATE, SYSDATE, '财务主任账号');

-- 办公室主任
INSERT INTO SYS_USER (USER_ID, USERNAME, PASSWORD, REAL_NAME, EMAIL, PHONE, DEPARTMENT_ID, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('USER016', 'office_director', '123456', '孙主任', '<EMAIL>', '13800000016', 'DEPT016', 1, SYSDATE, SYSDATE, '办公室主任账号');

-- 一分厂厂长
INSERT INTO SYS_USER (USER_ID, USERNAME, PASSWORD, REAL_NAME, EMAIL, PHONE, DEPARTMENT_ID, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('USER017', 'factory1_manager', '123456', '周厂长', '<EMAIL>', '13800000017', 'DEPT017', 1, SYSDATE, SYSDATE, '一分厂厂长账号');

-- 二分厂厂长
INSERT INTO SYS_USER (USER_ID, USERNAME, PASSWORD, REAL_NAME, EMAIL, PHONE, DEPARTMENT_ID, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('USER018', 'factory2_manager', '123456', '吴厂长', '<EMAIL>', '13800000018', 'DEPT018', 1, SYSDATE, SYSDATE, '二分厂厂长账号');

-- 三分厂厂长
INSERT INTO SYS_USER (USER_ID, USERNAME, PASSWORD, REAL_NAME, EMAIL, PHONE, DEPARTMENT_ID, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) 
VALUES ('USER019', 'factory3_manager', '123456', '郑厂长', '<EMAIL>', '13800000019', 'DEPT019', 1, SYSDATE, SYSDATE, '三分厂厂长账号');

-- 5. 分配用户角色
-- =====================================================

-- 厂长角色分配
INSERT INTO SYS_USER_ROLE (USER_ID, ROLE_ID) VALUES ('USER010', 'ROLE002');

-- 生产副厂长角色分配
INSERT INTO SYS_USER_ROLE (USER_ID, ROLE_ID) VALUES ('USER011', 'ROLE003');

-- 技术副厂长角色分配
INSERT INTO SYS_USER_ROLE (USER_ID, ROLE_ID) VALUES ('USER012', 'ROLE004');

-- 安全副厂长角色分配
INSERT INTO SYS_USER_ROLE (USER_ID, ROLE_ID) VALUES ('USER013', 'ROLE005');

-- 销售主任角色分配
INSERT INTO SYS_USER_ROLE (USER_ID, ROLE_ID) VALUES ('USER014', 'ROLE006');

-- 财务主任角色分配
INSERT INTO SYS_USER_ROLE (USER_ID, ROLE_ID) VALUES ('USER015', 'ROLE007');

-- 办公室主任角色分配
INSERT INTO SYS_USER_ROLE (USER_ID, ROLE_ID) VALUES ('USER016', 'ROLE008');

-- 分厂厂长角色分配
INSERT INTO SYS_USER_ROLE (USER_ID, ROLE_ID) VALUES ('USER017', 'ROLE009');
INSERT INTO SYS_USER_ROLE (USER_ID, ROLE_ID) VALUES ('USER018', 'ROLE009');
INSERT INTO SYS_USER_ROLE (USER_ID, ROLE_ID) VALUES ('USER019', 'ROLE009');

-- 6. 提交事务
-- =====================================================
COMMIT;

-- 验证数据
SELECT '角色信息' AS 类型, ROLE_NAME AS 名称, ROLE_KEY AS 标识 FROM SYS_ROLE
UNION ALL
SELECT '部门信息' AS 类型, DEPT_NAME AS 名称, DEPT_CODE AS 标识 FROM SYS_DEPARTMENT
UNION ALL
SELECT '用户信息' AS 类型, REAL_NAME AS 名称, USERNAME AS 标识 FROM SYS_USER;

-- =====================================================
-- 职位职责说明
-- =====================================================

/*
1. 厂长 (FACTORY_DIRECTOR)
   - 公司最高决策者，负责公司整体战略规划
   - 审批重要公文和决策文件
   - 监督各部门工作执行情况
   - 对外代表公司形象

2. 生产副厂长 (VICE_DIRECTOR_PROD)
   - 负责生产计划制定和执行
   - 协调各分厂生产任务
   - 审批生产相关公文
   - 监督生产安全和质量

3. 技术副厂长 (VICE_DIRECTOR_TECH)
   - 负责技术创新和研发管理
   - 审批技术改造方案
   - 指导技术培训和人才培养
   - 监督技术标准执行

4. 安全副厂长 (VICE_DIRECTOR_SAFETY)
   - 负责安全生产监督管理
   - 审批安全相关制度和措施
   - 组织安全检查和培训
   - 处理安全事故和隐患

5. 销售主任 (SALES_DIRECTOR)
   - 负责市场开拓和客户维护
   - 制定销售策略和计划
   - 审批销售合同和价格
   - 管理销售团队

6. 财务主任 (FINANCE_DIRECTOR)
   - 负责财务管理和资金监督
   - 审批财务支出和预算
   - 编制财务报表和分析
   - 监督成本控制

7. 办公室主任 (OFFICE_DIRECTOR)
   - 负责行政管理和综合协调
   - 处理日常行政事务
   - 组织会议和活动
   - 管理档案和文件

8. 分厂厂长 (FACTORY_MANAGER)
   - 负责分厂日常生产管理
   - 执行生产计划和任务
   - 管理分厂员工
   - 上报生产情况

9. 系统管理员 (ADMIN)
   - 负责系统维护和用户管理
   - 配置权限和角色
   - 监控系统运行状态
   - 处理技术问题
*/
