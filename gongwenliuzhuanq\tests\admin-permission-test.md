# 系统管理员权限测试方案

## 🎯 **测试目标**
验证系统管理员模块的权限边界和功能完整性，确保职责分离原则得到正确实施。

## 📋 **测试清单**

### **1. 登录测试**
- [ ] 使用admin账号登录系统
- [ ] 验证登录后自动跳转到 `/system/user` 页面
- [ ] 确认不会跳转到 `/dashboard` 工作台

### **2. 菜单显示测试**
系统管理员应该只能看到以下菜单：

#### **✅ 应该显示的菜单**
- [ ] 系统管理
  - [ ] 用户管理 (`/system/user`)
  - [ ] 角色管理 (`/system/role`)
  - [ ] 部门管理 (`/system/department`)
  - [ ] 系统日志 (`/system/log`)
  - [ ] 系统配置 (`/system/config`)
  - [ ] 系统监控 (`/system/monitor`)
- [ ] 个人中心 (`/profile`)

#### **❌ 不应该显示的菜单**
- [ ] 工作台 (`/dashboard`)
- [ ] 公文管理相关功能
- [ ] 统计报表
- [ ] 管理监督

### **3. 功能访问测试**

#### **✅ 可以访问的功能**
- [ ] 用户管理
  - [ ] 查看用户列表
  - [ ] 创建新用户
  - [ ] 编辑用户信息
  - [ ] 删除用户（除自己外）
  - [ ] 重置用户密码
  - [ ] 启用/禁用用户

- [ ] 角色管理
  - [ ] 查看角色列表
  - [ ] 创建新角色
  - [ ] 编辑角色信息
  - [ ] 删除角色（除ADMIN外）
  - [ ] 配置角色权限

- [ ] 部门管理
  - [ ] 查看部门树形结构
  - [ ] 创建新部门
  - [ ] 编辑部门信息
  - [ ] 删除部门
  - [ ] 调整部门层级

- [ ] 系统日志
  - [ ] 查看操作日志
  - [ ] 筛选日志记录
  - [ ] 导出日志数据
  - [ ] 清理历史日志

- [ ] 系统配置
  - [ ] 基础配置管理
  - [ ] 安全策略配置
  - [ ] 流程规则配置
  - [ ] 邮件服务配置

- [ ] 系统监控
  - [ ] 查看系统状态
  - [ ] 监控在线用户
  - [ ] 查看实时日志
  - [ ] 强制用户下线

#### **❌ 不能访问的功能**
- [ ] 直接访问 `/dashboard` 应该被拦截
- [ ] 直接访问 `/document/*` 应该被拦截
- [ ] 直接访问 `/statistics` 应该被拦截
- [ ] 直接访问 `/management/*` 应该被拦截

### **4. 权限边界测试**

#### **URL直接访问测试**
在浏览器地址栏直接输入以下URL，验证访问控制：

```
# 应该可以访问
http://localhost:3000/system/user          ✅
http://localhost:3000/system/role          ✅
http://localhost:3000/system/department    ✅
http://localhost:3000/system/log           ✅
http://localhost:3000/system/config        ✅
http://localhost:3000/system/monitor       ✅
http://localhost:3000/profile              ✅

# 应该被拦截并跳转
http://localhost:3000/dashboard            ❌ → /system/user
http://localhost:3000/document/pending     ❌ → /system/user
http://localhost:3000/document/create      ❌ → /system/user
http://localhost:3000/statistics           ❌ → /system/user
```

#### **权限提示测试**
- [ ] 访问被拦截的页面时显示权限错误提示
- [ ] 错误提示内容准确："系统管理员无权访问业务功能模块"

### **5. 数据操作测试**

#### **用户管理测试**
- [ ] 创建新的业务用户（厂长、副厂长等）
- [ ] 为新用户分配正确的角色
- [ ] 验证新用户可以正常登录
- [ ] 验证新用户看到正确的菜单

#### **角色管理测试**
- [ ] 查看所有角色列表（包括新创建的9个角色）
- [ ] 编辑角色信息
- [ ] 不能删除ADMIN角色
- [ ] 不能删除已分配给用户的角色

#### **部门管理测试**
- [ ] 查看完整的部门树形结构
- [ ] 验证部门层级关系正确
- [ ] 编辑部门信息
- [ ] 创建新的子部门

### **6. 系统功能测试**

#### **系统配置测试**
- [ ] 修改基础配置并保存
- [ ] 配置安全策略
- [ ] 设置流程规则
- [ ] 测试邮件配置

#### **系统监控测试**
- [ ] 查看系统资源使用情况
- [ ] 监控在线用户列表
- [ ] 查看实时日志更新
- [ ] 强制其他用户下线

### **7. 安全性测试**

#### **会话管理测试**
- [ ] 验证管理员会话超时机制
- [ ] 测试并发登录限制
- [ ] 验证密码策略执行

#### **操作审计测试**
- [ ] 验证所有管理操作都有日志记录
- [ ] 检查日志记录的完整性和准确性
- [ ] 验证敏感操作的审计跟踪

## 🔧 **测试执行步骤**

### **步骤1：环境准备**
1. 确保数据库中已执行组织架构重构SQL
2. 启动前端开发服务器：`npm run dev`
3. 确保后端服务正常运行

### **步骤2：登录测试**
1. 打开浏览器访问 `http://localhost:3000`
2. 使用admin账号登录
3. 验证登录后的页面跳转

### **步骤3：菜单功能测试**
1. 逐一点击每个菜单项
2. 验证页面正常加载
3. 测试各项功能操作

### **步骤4：权限边界测试**
1. 在地址栏直接输入业务功能URL
2. 验证访问控制和错误提示
3. 确认跳转行为正确

### **步骤5：数据操作测试**
1. 测试用户、角色、部门的增删改查
2. 验证数据完整性和关联关系
3. 测试权限分配功能

## 📊 **测试结果记录**

### **测试环境**
- 浏览器：Chrome/Firefox/Edge
- 前端版本：Vue 3 + Element Plus
- 后端版本：Spring Boot
- 数据库：达梦数据库 v8.0
- 测试时间：____年__月__日

### **测试结果**
- [ ] 所有测试项目通过
- [ ] 发现问题并已修复
- [ ] 需要进一步优化的功能

### **问题记录**
| 序号 | 问题描述 | 严重程度 | 状态 | 备注 |
|------|----------|----------|------|------|
| 1    |          |          |      |      |
| 2    |          |          |      |      |
| 3    |          |          |      |      |

## ✅ **验收标准**

系统管理员模块通过验收需要满足：

1. **功能完整性**：所有系统管理功能正常工作
2. **权限边界**：严格限制业务功能访问
3. **安全性**：操作审计和权限控制到位
4. **用户体验**：界面友好，操作流畅
5. **数据完整性**：所有操作不影响数据一致性

## 🚀 **后续优化建议**

1. **性能优化**：大数据量下的查询优化
2. **功能增强**：批量操作和高级筛选
3. **监控告警**：系统异常自动告警
4. **备份恢复**：数据备份和恢复机制
