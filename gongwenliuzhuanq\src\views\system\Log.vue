<template>
  <div class="log-management">
    <div class="page-header">
      <h1 class="page-title">系统日志</h1>
      <div class="page-actions">
        <el-button type="danger" @click="handleClearLogs">
          <el-icon><Delete /></el-icon>
          清空日志
        </el-button>
        <el-button type="primary" @click="handleExport">
          <el-icon><Download /></el-icon>
          导出日志
        </el-button>
      </div>
    </div>

    <!-- 搜索表单 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchForm" class="search-form" inline>
        <el-form-item label="操作用户">
          <el-input v-model="searchForm.username" placeholder="请输入用户名" clearable />
        </el-form-item>
        <el-form-item label="操作模块">
          <el-select v-model="searchForm.module" placeholder="请选择模块" clearable>
            <el-option label="用户管理" value="USER" />
            <el-option label="角色管理" value="ROLE" />
            <el-option label="部门管理" value="DEPT" />
            <el-option label="公文管理" value="DOCUMENT" />
            <el-option label="系统设置" value="SYSTEM" />
          </el-select>
        </el-form-item>
        <el-form-item label="操作类型">
          <el-select v-model="searchForm.operationType" placeholder="请选择操作类型" clearable>
            <el-option label="登录" value="LOGIN" />
            <el-option label="登出" value="LOGOUT" />
            <el-option label="新增" value="CREATE" />
            <el-option label="修改" value="UPDATE" />
            <el-option label="删除" value="DELETE" />
            <el-option label="查询" value="SELECT" />
          </el-select>
        </el-form-item>
        <el-form-item label="操作状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="成功" value="SUCCESS" />
            <el-option label="失败" value="FAIL" />
          </el-select>
        </el-form-item>
        <el-form-item label="操作时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <el-table v-loading="loading" :data="tableData" stripe style="width: 100%">
        <el-table-column prop="logId" label="日志ID" width="120" />
        <el-table-column prop="username" label="操作用户" width="120" />
        <el-table-column prop="realName" label="用户姓名" width="120" />
        <el-table-column prop="module" label="操作模块" width="120">
          <template #default="{ row }">
            <el-tag size="small">{{ getModuleName(row.module) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="operationType" label="操作类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getOperationTypeColor(row.operationType)" size="small">
              {{ getOperationTypeName(row.operationType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="操作描述" min-width="200" />
        <el-table-column prop="ip" label="IP地址" width="130" />
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.status === 'SUCCESS' ? 'success' : 'danger'" size="small">
              {{ row.status === 'SUCCESS' ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="操作时间" width="160">
          <template #default="{ row }">
            {{ formatTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleViewDetail(row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Delete, Download, Search } from '@element-plus/icons-vue'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const tableData = ref([])

// 搜索表单
const searchForm = reactive({
  username: '',
  module: '',
  operationType: '',
  status: '',
  dateRange: []
})

// 分页数据
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 方法
const formatTime = (timestamp) => {
  return timestamp ? dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss') : '-'
}

const getModuleName = (module) => {
  const moduleMap = {
    USER: '用户管理',
    ROLE: '角色管理',
    DEPT: '部门管理',
    DOCUMENT: '公文管理',
    SYSTEM: '系统设置'
  }
  return moduleMap[module] || module
}

const getOperationTypeName = (type) => {
  const typeMap = {
    LOGIN: '登录',
    LOGOUT: '登出',
    CREATE: '新增',
    UPDATE: '修改',
    DELETE: '删除',
    SELECT: '查询'
  }
  return typeMap[type] || type
}

const getOperationTypeColor = (type) => {
  const colorMap = {
    LOGIN: 'success',
    LOGOUT: 'info',
    CREATE: 'primary',
    UPDATE: 'warning',
    DELETE: 'danger',
    SELECT: ''
  }
  return colorMap[type] || ''
}

const loadData = async () => {
  loading.value = true
  try {
    // 模拟数据
    const mockData = [
      {
        logId: 'LOG001',
        username: 'admin',
        realName: '系统管理员',
        module: 'USER',
        operationType: 'LOGIN',
        description: '用户登录系统',
        ip: '127.0.0.1',
        status: 'SUCCESS',
        createTime: Date.now() - 3600000
      },
      {
        logId: 'LOG002',
        username: 'admin',
        realName: '系统管理员',
        module: 'DOCUMENT',
        operationType: 'SELECT',
        description: '查询公文列表',
        ip: '127.0.0.1',
        status: 'SUCCESS',
        createTime: Date.now() - 7200000
      },
      {
        logId: 'LOG003',
        username: 'zhangsan',
        realName: '张三',
        module: 'DOCUMENT',
        operationType: 'CREATE',
        description: '创建新公文',
        ip: '*************',
        status: 'SUCCESS',
        createTime: Date.now() - 10800000
      },
      {
        logId: 'LOG004',
        username: 'lisi',
        realName: '李四',
        module: 'USER',
        operationType: 'UPDATE',
        description: '修改用户信息',
        ip: '*************',
        status: 'FAIL',
        createTime: Date.now() - 14400000
      }
    ]
    
    tableData.value = mockData
    pagination.total = mockData.length
  } catch (error) {
    console.error('加载日志数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadData()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    username: '',
    module: '',
    operationType: '',
    status: '',
    dateRange: []
  })
  pagination.current = 1
  loadData()
}

const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  loadData()
}

const handleCurrentChange = (current) => {
  pagination.current = current
  loadData()
}

const handleViewDetail = (row) => {
  ElMessage.info(`查看日志详情: ${row.description}`)
}

const handleClearLogs = () => {
  ElMessageBox.confirm(
    '确定要清空所有系统日志吗？此操作不可恢复！',
    '清空日志',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('日志清空成功')
    loadData()
  }).catch(() => {
    ElMessage.info('已取消清空日志')
  })
}

const handleExport = () => {
  ElMessage.info('导出日志功能开发中...')
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.log-management {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .page-title {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }
  }

  .search-card {
    margin-bottom: 20px;

    .search-form {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }

  .table-card {
    .pagination-container {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }
  }
}
</style>
