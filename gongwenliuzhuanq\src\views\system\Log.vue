<template>
  <div class="log-management">
    <div class="page-header">
      <h1 class="page-title">系统日志</h1>
      <div class="page-actions">
        <el-button type="danger" @click="handleClearLogs">
          <el-icon><Delete /></el-icon>
          清空日志
        </el-button>
        <el-button type="primary" @click="handleExport">
          <el-icon><Download /></el-icon>
          导出日志
        </el-button>
      </div>
    </div>

    <!-- 搜索表单 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchForm" class="search-form" inline>
        <el-form-item label="操作用户">
          <el-input v-model="searchForm.username" placeholder="请输入用户名" clearable />
        </el-form-item>
        <el-form-item label="操作模块">
          <el-select v-model="searchForm.module" placeholder="请选择模块" clearable>
            <el-option label="用户管理" value="USER" />
            <el-option label="角色管理" value="ROLE" />
            <el-option label="部门管理" value="DEPT" />
            <el-option label="公文管理" value="DOCUMENT" />
            <el-option label="系统设置" value="SYSTEM" />
          </el-select>
        </el-form-item>
        <el-form-item label="操作类型">
          <el-select v-model="searchForm.operationType" placeholder="请选择操作类型" clearable>
            <el-option label="登录" value="LOGIN" />
            <el-option label="登出" value="LOGOUT" />
            <el-option label="新增" value="CREATE" />
            <el-option label="修改" value="UPDATE" />
            <el-option label="删除" value="DELETE" />
            <el-option label="查询" value="SELECT" />
          </el-select>
        </el-form-item>
        <el-form-item label="操作状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="成功" value="SUCCESS" />
            <el-option label="失败" value="FAIL" />
          </el-select>
        </el-form-item>
        <el-form-item label="操作时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <el-table v-loading="loading" :data="tableData" stripe style="width: 100%">
        <el-table-column prop="logId" label="日志ID" width="120" />
        <el-table-column prop="username" label="操作用户" width="120" />
        <el-table-column prop="realName" label="用户姓名" width="120" />
        <el-table-column prop="module" label="操作模块" width="120">
          <template #default="{ row }">
            <el-tag :type="getModuleColor(row.module)" size="small">{{ getModuleName(row.module) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="operationType" label="操作类型" width="100">
          <template #default="{ row }">
            <el-tag
              :type="getOperationTypeColor(row.operationType) || undefined"
              size="small"
            >
              {{ getOperationTypeName(row.operationType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="操作描述" min-width="200" />
        <el-table-column prop="ip" label="IP地址" width="130" />
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.status === 'SUCCESS' ? 'success' : 'danger'" size="small">
              {{ row.status === 'SUCCESS' ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="操作时间" width="160">
          <template #default="{ row }">
            {{ formatTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleViewDetail(row)">详情</el-button>
            <el-button type="danger" size="small" @click="handleDeleteSingle(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 日志详情对话框 -->
    <LogDetailDialog
      v-model:visible="detailDialogVisible"
      :log-id="currentLogId"
      @deleted="handleLogDeleted"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Delete, Download, Search } from '@element-plus/icons-vue'
import { getLogList, clearAllLogs, exportLogs, deleteLog } from '@/api/system/log'
import dayjs from 'dayjs'
import LogDetailDialog from './components/LogDetailDialog.vue'

// 响应式数据
const loading = ref(false)
const tableData = ref([])

// 搜索表单
const searchForm = reactive({
  username: '',
  module: '',
  operationType: '',
  status: '',
  dateRange: []
})

// 分页数据
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 对话框相关数据
const detailDialogVisible = ref(false)
const currentLogId = ref('')

// 方法
const formatTime = (timestamp) => {
  return timestamp ? dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss') : '-'
}

const getModuleName = (module) => {
  const moduleMap = {
    USER: '用户管理',
    ROLE: '角色管理',
    DEPT: '部门管理',
    DOCUMENT: '公文管理',
    SYSTEM: '系统设置'
  }
  return moduleMap[module] || module
}

const getModuleColor = (module) => {
  const colorMap = {
    USER: 'primary',
    ROLE: 'success',
    DEPT: 'warning',
    DOCUMENT: 'info',
    SYSTEM: 'danger'
  }
  return colorMap[module] || 'primary'
}

const getOperationTypeName = (type) => {
  const typeMap = {
    LOGIN: '登录',
    LOGOUT: '登出',
    CREATE: '新增',
    UPDATE: '修改',
    DELETE: '删除',
    SELECT: '查询'
  }
  return typeMap[type] || type
}

const getOperationTypeColor = (type) => {
  const colorMap = {
    LOGIN: 'success',
    LOGOUT: 'info',
    CREATE: 'primary',
    UPDATE: 'warning',
    DELETE: 'danger',
    SELECT: 'info'  // 改为info而不是空字符串
  }
  return colorMap[type] || 'info'  // 默认返回info而不是空字符串
}

const loadData = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: pagination.current,
      pageSize: pagination.size,
      username: searchForm.username,
      module: searchForm.module,
      operationType: searchForm.operationType,
      status: searchForm.status
    }

    // 处理时间范围
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.startTime = searchForm.dateRange[0]
      params.endTime = searchForm.dateRange[1]
    }

    const response = await getLogList(params)
    if (response.code === 200) {
      const data = response.data || {}
      tableData.value = data.records || []
      pagination.total = data.total || 0
    } else {
      ElMessage.error(response.message || '获取日志数据失败')
      tableData.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('加载日志数据失败:', error)
    ElMessage.error('加载日志数据失败，请检查网络连接')
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadData()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    username: '',
    module: '',
    operationType: '',
    status: '',
    dateRange: []
  })
  pagination.current = 1
  loadData()
}

const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  loadData()
}

const handleCurrentChange = (current) => {
  pagination.current = current
  loadData()
}

const handleViewDetail = (row) => {
  currentLogId.value = row.logId
  detailDialogVisible.value = true
}

const handleDeleteSingle = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除日志 ${row.logId} 吗？删除后不可恢复！`,
      '删除日志',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await deleteLog(row.logId)
    if (response.code === 200) {
      ElMessage.success('删除成功')
      loadData()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleClearLogs = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有系统日志吗？此操作不可恢复！',
      '清空日志',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await clearAllLogs()
    if (response.code === 200) {
      ElMessage.success('日志清空成功')
      loadData()
    } else {
      ElMessage.error(response.message || '清空日志失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('清空日志失败')
    }
  }
}

const handleExport = async () => {
  try {
    const params = {
      username: searchForm.username,
      module: searchForm.module,
      operationType: searchForm.operationType,
      status: searchForm.status
    }

    // 处理时间范围
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.startTime = searchForm.dateRange[0]
      params.endTime = searchForm.dateRange[1]
    }

    ElMessage.info('正在导出日志，请稍候...')
    const response = await exportLogs(params)

    // 创建下载链接
    const blob = new Blob([response], {
      type: 'text/csv;charset=utf-8'
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `系统日志_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.csv`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出日志失败:', error)
    ElMessage.error('导出日志失败')
  }
}

const handleLogDeleted = () => {
  detailDialogVisible.value = false
  loadData()
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.log-management {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .page-title {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }
  }

  .search-card {
    margin-bottom: 20px;

    .search-form {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }

  .table-card {
    .pagination-container {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }
  }
}
</style>
