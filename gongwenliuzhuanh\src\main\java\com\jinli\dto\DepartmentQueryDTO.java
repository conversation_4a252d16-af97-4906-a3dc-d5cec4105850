package com.jinli.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 部门查询DTO
 * 
 * <AUTHOR>
 * @since 2025-01-30
 */
@Data
public class DepartmentQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 部门名称（模糊查询）
     */
    private String deptName;

    /**
     * 部门编码（模糊查询）
     */
    private String deptCode;

    /**
     * 父部门ID
     */
    private String parentId;

    /**
     * 负责人（模糊查询）
     */
    private String leader;

    /**
     * 部门状态（0正常 1停用）
     */
    private String status;

    /**
     * 是否包含子部门
     */
    private Boolean includeChildren = true;

    /**
     * 是否只查询启用的部门
     */
    private Boolean onlyEnabled = false;

    /**
     * 排除的部门ID（用于编辑时排除自己）
     */
    private String excludeDeptId;

    /**
     * 最大层级（限制查询深度）
     */
    private Integer maxLevel;

    /**
     * 是否返回用户数量
     */
    private Boolean includeUserCount = false;

    /**
     * 开始时间（创建时间范围查询）
     */
    private String startTime;

    /**
     * 结束时间（创建时间范围查询）
     */
    private String endTime;

    /**
     * 排序字段
     */
    private String orderBy = "ORDER_NUM ASC, CREATE_TIME ASC";

    /**
     * 是否返回树形结构
     */
    private Boolean returnTree = true;

    /**
     * 检查是否有查询条件
     */
    public boolean hasQueryCondition() {
        return (deptName != null && !deptName.trim().isEmpty()) ||
               (deptCode != null && !deptCode.trim().isEmpty()) ||
               (leader != null && !leader.trim().isEmpty()) ||
               (status != null && !status.trim().isEmpty()) ||
               (parentId != null && !parentId.trim().isEmpty());
    }

    /**
     * 获取处理后的部门名称（去除空格）
     */
    public String getTrimmedDeptName() {
        return deptName != null ? deptName.trim() : null;
    }

    /**
     * 获取处理后的部门编码（去除空格并转大写）
     */
    public String getTrimmedDeptCode() {
        return deptCode != null ? deptCode.trim().toUpperCase() : null;
    }

    /**
     * 获取处理后的负责人（去除空格）
     */
    public String getTrimmedLeader() {
        return leader != null ? leader.trim() : null;
    }

    /**
     * 检查是否只查询根部门
     */
    public boolean isQueryRootOnly() {
        return parentId != null && ("0".equals(parentId) || parentId.isEmpty());
    }

    /**
     * 检查是否有时间范围查询
     */
    public boolean hasTimeRange() {
        return (startTime != null && !startTime.trim().isEmpty()) ||
               (endTime != null && !endTime.trim().isEmpty());
    }

    // 手动添加getter/setter方法
    public Boolean getReturnTree() { return returnTree; }
    public void setReturnTree(Boolean returnTree) { this.returnTree = returnTree; }
}
