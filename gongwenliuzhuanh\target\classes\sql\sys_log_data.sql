-- 达梦数据库 - 系统日志测试数据

-- 插入测试数据
INSERT INTO sys_log (log_id, username, real_name, module, operation_type, description, request_url, request_method, request_params, ip, user_agent, status, create_time) 
VALUES ('LOG001', 'admin', '系统管理员', 'USER', 'LOGIN', '用户登录系统', '/auth/login', 'POST', '{"username":"admin"}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'SUCCESS', SYSDATE - 1/24);

INSERT INTO sys_log (log_id, username, real_name, module, operation_type, description, request_url, request_method, request_params, ip, user_agent, status, create_time) 
VALUES ('LOG002', 'admin', '系统管理员', 'DOCUMENT', 'SELECT', '查询公文列表', '/system/document/list', 'GET', '{"pageNum":1,"pageSize":10}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'SUCCESS', SYSDATE - 2/24);

INSERT INTO sys_log (log_id, username, real_name, module, operation_type, description, request_url, request_method, request_params, ip, user_agent, status, create_time) 
VALUES ('LOG003', 'zhangsan', '张三', 'DOCUMENT', 'CREATE', '创建新公文', '/system/document', 'POST', '{"title":"测试公文","content":"测试内容"}', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'SUCCESS', SYSDATE - 3/24);

INSERT INTO sys_log (log_id, username, real_name, module, operation_type, description, request_url, request_method, request_params, ip, user_agent, status, create_time) 
VALUES ('LOG004', 'lisi', '李四', 'USER', 'UPDATE', '修改用户信息', '/system/user', 'PUT', '{"userId":"123","realName":"李四"}', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'FAIL', SYSDATE - 4/24);

INSERT INTO sys_log (log_id, username, real_name, module, operation_type, description, request_url, request_method, request_params, ip, user_agent, status, create_time) 
VALUES ('LOG005', 'wangwu', '王五', 'DEPT', 'CREATE', '新增部门', '/system/department', 'POST', '{"deptName":"测试部门","deptCode":"TEST_DEPT"}', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'SUCCESS', SYSDATE - 5/24);

INSERT INTO sys_log (log_id, username, real_name, module, operation_type, description, request_url, request_method, request_params, ip, user_agent, status, create_time) 
VALUES ('LOG006', 'admin', '系统管理员', 'ROLE', 'UPDATE', '修改角色权限', '/system/role', 'PUT', '{"roleId":"456","permissions":["user:list","user:add"]}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'SUCCESS', SYSDATE - 6/24);

INSERT INTO sys_log (log_id, username, real_name, module, operation_type, description, request_url, request_method, request_params, ip, user_agent, status, create_time) 
VALUES ('LOG007', 'zhangsan', '张三', 'DOCUMENT', 'DELETE', '删除公文', '/system/document/789', 'DELETE', '{}', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'SUCCESS', SYSDATE - 7/24);

INSERT INTO sys_log (log_id, username, real_name, module, operation_type, description, request_url, request_method, request_params, ip, user_agent, status, create_time) 
VALUES ('LOG008', 'lisi', '李四', 'SYSTEM', 'UPDATE', '修改系统配置', '/system/config', 'PUT', '{"configKey":"system.title","configValue":"公文流转系统"}', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'SUCCESS', SYSDATE - 8/24);

INSERT INTO sys_log (log_id, username, real_name, module, operation_type, description, request_url, request_method, request_params, ip, user_agent, status, create_time) 
VALUES ('LOG009', 'wangwu', '王五', 'USER', 'LOGIN', '用户登录系统', '/auth/login', 'POST', '{"username":"wangwu"}', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'SUCCESS', SYSDATE - 9/24);

INSERT INTO sys_log (log_id, username, real_name, module, operation_type, description, request_url, request_method, request_params, ip, user_agent, status, create_time) 
VALUES ('LOG010', 'admin', '系统管理员', 'DOCUMENT', 'SELECT', '导出公文列表', '/system/document/export', 'GET', '{"startTime":"2025-01-01","endTime":"2025-01-31"}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'SUCCESS', SYSDATE - 10/24);

-- 提交事务
COMMIT;
