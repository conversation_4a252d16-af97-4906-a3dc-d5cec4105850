package com.jinli.gongwen.service.document.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinli.gongwen.dto.document.DocumentReceiveDTO;
import com.jinli.gongwen.service.document.ReceiveService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 签收服务实现类
 */
@Service
public class ReceiveServiceImpl implements ReceiveService {

    @Override
    public void receiveDocument(DocumentReceiveDTO receiveDTO) {
        // TODO: 实现具体逻辑
    }

    @Override
    public void batchReceiveDocuments(List<String> docIds, String userId, String deptId) {
        // TODO: 实现具体逻辑
    }

    @Override
    public IPage<Object> getReceiveRecords(Page<Object> page, String deptId) {
        // TODO: 实现具体逻辑
        return new Page<>();
    }
}
