package com.jinli.controller;

import com.jinli.common.Result;
import com.jinli.dto.RoleQueryDTO;
import com.jinli.entity.SysRole;
import com.jinli.service.impl.SysRoleServiceSimple;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 角色管理控制器
 * 
 * <AUTHOR>
 * @since 2025-01-30
 */
@RestController
@RequestMapping("/system/role")
public class SysRoleController {

    private static final Logger log = LoggerFactory.getLogger(SysRoleController.class);

    @Autowired
    private SysRoleServiceSimple roleService;

    /**
     * 分页查询角色列表
     */
    @GetMapping("/list")
    public Result<Map<String, Object>> list(RoleQueryDTO queryDTO) {
        log.info("分页查询角色列表，查询条件：{}", queryDTO);
        
        try {
            Map<String, Object> result = roleService.selectRolePage(queryDTO);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询角色列表失败", e);
            return Result.error("查询角色列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据角色ID查询角色详情
     */
    @GetMapping("/{roleId}")
    public Result<SysRole> getInfo(@PathVariable String roleId) {
        log.info("查询角色详情，角色ID：{}", roleId);
        
        try {
            if (!StringUtils.hasText(roleId)) {
                return Result.error("角色ID不能为空");
            }
            
            SysRole role = roleService.selectRoleById(roleId);
            if (role == null) {
                return Result.error("角色不存在");
            }
            
            return Result.success(role);
        } catch (Exception e) {
            log.error("查询角色详情失败", e);
            return Result.error("查询角色详情失败：" + e.getMessage());
        }
    }

    /**
     * 新增角色
     */
    @PostMapping
    public Result<Void> add(@RequestBody SysRole role) {
        log.info("新增角色，角色信息：{}", role);
        
        try {
            // 验证角色数据
            String validateResult = validateRole(role);
            if (StringUtils.hasText(validateResult)) {
                return Result.error(validateResult);
            }
            
            boolean success = roleService.insertRole(role);
            if (success) {
                return Result.success("新增角色成功");
            } else {
                return Result.error("新增角色失败");
            }
        } catch (Exception e) {
            log.error("新增角色失败", e);
            return Result.error("新增角色失败：" + e.getMessage());
        }
    }

    /**
     * 修改角色
     */
    @PutMapping
    public Result<Void> edit(@RequestBody SysRole role) {
        log.info("修改角色，角色信息：{}", role);
        
        try {
            if (!StringUtils.hasText(role.getRoleId())) {
                return Result.error("角色ID不能为空");
            }
            
            // 验证角色数据
            String validateResult = validateRole(role);
            if (StringUtils.hasText(validateResult)) {
                return Result.error(validateResult);
            }
            
            boolean success = roleService.updateRole(role);
            if (success) {
                return Result.success("修改角色成功");
            } else {
                return Result.error("修改角色失败");
            }
        } catch (Exception e) {
            log.error("修改角色失败", e);
            return Result.error("修改角色失败：" + e.getMessage());
        }
    }

    /**
     * 删除角色
     */
    @DeleteMapping("/{roleId}")
    public Result<Void> remove(@PathVariable String roleId) {
        log.info("删除角色，角色ID：{}", roleId);
        
        try {
            if (!StringUtils.hasText(roleId)) {
                return Result.error("角色ID不能为空");
            }
            
            // 检查是否可以删除
            String checkResult = checkCanDeleteRole(roleId);
            if (StringUtils.hasText(checkResult)) {
                return Result.error(checkResult);
            }
            
            boolean success = roleService.deleteRole(roleId);
            if (success) {
                return Result.success("删除角色成功");
            } else {
                return Result.error("删除角色失败");
            }
        } catch (Exception e) {
            log.error("删除角色失败", e);
            return Result.error("删除角色失败：" + e.getMessage());
        }
    }

    /**
     * 更新角色状态
     */
    @PutMapping("/{roleId}/status")
    public Result<Void> updateStatus(@PathVariable String roleId, @RequestParam String status) {
        log.info("更新角色状态，角色ID：{}，状态：{}", roleId, status);
        
        try {
            if (!StringUtils.hasText(roleId)) {
                return Result.error("角色ID不能为空");
            }
            
            if (!StringUtils.hasText(status)) {
                return Result.error("状态不能为空");
            }
            
            boolean success = roleService.updateRoleStatus(roleId, status);
            if (success) {
                return Result.success("更新角色状态成功");
            } else {
                return Result.error("更新角色状态失败");
            }
        } catch (Exception e) {
            log.error("更新角色状态失败", e);
            return Result.error("更新角色状态失败：" + e.getMessage());
        }
    }

    /**
     * 检查角色名称是否存在
     */
    @GetMapping("/check-name")
    public Result<Boolean> checkRoleName(@RequestParam String roleName, 
                                        @RequestParam(required = false) String roleId) {
        log.info("检查角色名称是否存在，角色名称：{}，排除角色ID：{}", roleName, roleId);
        
        try {
            if (!StringUtils.hasText(roleName)) {
                return Result.error("角色名称不能为空");
            }
            
            boolean exists = roleService.checkRoleNameExists(roleName, roleId);
            return Result.success(exists);
        } catch (Exception e) {
            log.error("检查角色名称失败", e);
            return Result.error("检查角色名称失败：" + e.getMessage());
        }
    }

    /**
     * 检查角色标识是否存在
     */
    @GetMapping("/check-key")
    public Result<Boolean> checkRoleKey(@RequestParam String roleKey, 
                                       @RequestParam(required = false) String roleId) {
        log.info("检查角色标识是否存在，角色标识：{}，排除角色ID：{}", roleKey, roleId);
        
        try {
            if (!StringUtils.hasText(roleKey)) {
                return Result.error("角色标识不能为空");
            }
            
            boolean exists = roleService.checkRoleKeyExists(roleKey, roleId);
            return Result.success(exists);
        } catch (Exception e) {
            log.error("检查角色标识失败", e);
            return Result.error("检查角色标识失败：" + e.getMessage());
        }
    }

    /**
     * 测试接口
     */
    @GetMapping("/test")
    public Result<String> test() {
        log.info("角色管理测试接口被调用");
        return Result.success("角色管理API测试成功！");
    }

    /**
     * 验证角色数据
     */
    private String validateRole(SysRole role) {
        if (role == null) {
            return "角色信息不能为空";
        }
        
        if (!StringUtils.hasText(role.getRoleName())) {
            return "角色名称不能为空";
        }
        
        if (!StringUtils.hasText(role.getRoleKey())) {
            return "角色标识不能为空";
        }
        
        // 检查角色名称是否重复
        if (roleService.checkRoleNameExists(role.getRoleName(), role.getRoleId())) {
            return "角色名称已存在";
        }
        
        // 检查角色标识是否重复
        if (roleService.checkRoleKeyExists(role.getRoleKey(), role.getRoleId())) {
            return "角色标识已存在";
        }
        
        return null;
    }

    /**
     * 检查是否可以删除角色
     */
    private String checkCanDeleteRole(String roleId) {
        if (!StringUtils.hasText(roleId)) {
            return "角色ID不能为空";
        }
        
        // 检查是否为内置角色
        SysRole role = roleService.selectRoleById(roleId);
        if (role != null && role.isBuiltIn()) {
            return "内置角色不能删除";
        }
        
        return null;
    }
}
