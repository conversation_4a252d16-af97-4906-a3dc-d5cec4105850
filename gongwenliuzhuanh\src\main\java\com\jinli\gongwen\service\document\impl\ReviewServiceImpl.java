package com.jinli.gongwen.service.document.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinli.gongwen.dto.document.DocumentReviewDTO;
import com.jinli.gongwen.service.document.ReviewService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 审核服务实现类
 */
@Service
public class ReviewServiceImpl implements ReviewService {

    @Override
    public void reviewDocument(DocumentReviewDTO reviewDTO) {
        // TODO: 实现具体逻辑
    }

    @Override
    public void batchReviewDocuments(List<DocumentReviewDTO> reviewDTOList) {
        // TODO: 实现具体逻辑
    }

    @Override
    public IPage<Object> getReviewHistory(Page<Object> page, String userId) {
        // TODO: 实现具体逻辑
        return new Page<>();
    }

    @Override
    public void returnDocument(String docId, String reason, String userId) {
        // TODO: 实现具体逻辑
    }
}
