<template>
  <div class="role-manage">
    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="queryForm" inline class="search-form">
        <el-form-item label="角色名称">
          <el-input
            v-model="queryForm.roleName"
            placeholder="请输入角色名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="角色标识">
          <el-input
            v-model="queryForm.roleKey"
            placeholder="请输入角色标识"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="queryForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="正常" value="1" />
            <el-option label="停用" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <i class="el-icon-search"></i> 查询
          </el-button>
          <el-button @click="resetQuery">
            <i class="el-icon-refresh"></i> 重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作工具栏 -->
    <el-card class="toolbar-card">
      <div class="toolbar">
        <div class="toolbar-left">
          <el-button type="primary" @click="handleAdd">
            <i class="el-icon-plus"></i> 新增角色
          </el-button>
          <el-button
            type="danger"
            :disabled="!multipleSelection.length"
            @click="handleBatchDelete"
          >
            <i class="el-icon-delete"></i> 批量删除
          </el-button>
        </div>
        <div class="toolbar-right">
          <el-button @click="handleRefresh">
            <i class="el-icon-refresh"></i> 刷新
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 角色表格 -->
    <el-card class="table-card">
      <el-table
        :data="roleList"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="roleName" label="角色名称" width="150" />
        <el-table-column prop="roleKey" label="角色标识" width="150" />
        <el-table-column prop="roleSort" label="显示顺序" width="100" />
        <el-table-column prop="status" label="状态" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === '1' ? 'success' : 'danger'">
              {{ scope.row.status === '1' ? '正常' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="userCount" label="用户数量" width="100" />
        <el-table-column prop="createTime" label="创建时间" width="160" />
        <el-table-column prop="remark" label="备注" min-width="200" />
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleView(scope.row)">
              查看
            </el-button>
            <el-button size="mini" type="primary" @click="handleEdit(scope.row)">
              编辑
            </el-button>

            <el-dropdown @command="handleCommand" trigger="click">
              <el-button size="mini">
                更多<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  :command="{action: 'changeStatus', row: scope.row}"
                >
                  {{ scope.row.status === '1' ? '停用' : '启用' }}
                </el-dropdown-item>
                <el-dropdown-item
                  :command="{action: 'delete', row: scope.row}"
                  :divided="true"
                  style="color: #f56c6c"
                >
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="queryForm.pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="queryForm.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        />
      </div>
    </el-card>

    <!-- 角色表单对话框 -->
    <RoleForm
      :visible.sync="formVisible"
      :form-data="formData"
      :is-edit="isEdit"
      @success="handleFormSuccess"
    />


  </div>
</template>

<script>
import { getRoleList, deleteRole, changeRoleStatus } from '@/api/system/role'
import RoleForm from './components/RoleForm'

export default {
  name: 'RoleManage',
  components: {
    RoleForm
  },
  data() {
    return {
      loading: false,
      roleList: [],
      total: 0,
      multipleSelection: [],
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        roleName: '',
        roleKey: '',
        status: ''
      },
      formVisible: false,
      formData: {},
      isEdit: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 获取角色列表
    async getList() {
      this.loading = true
      try {
        const response = await getRoleList(this.queryForm)
        this.roleList = response.data.records
        this.total = response.data.total
      } catch (error) {
        this.$message.error('获取角色列表失败')
      } finally {
        this.loading = false
      }
    },

    // 查询
    handleQuery() {
      this.queryForm.pageNum = 1
      this.getList()
    },

    // 重置查询
    resetQuery() {
      this.queryForm = {
        pageNum: 1,
        pageSize: 10,
        roleName: '',
        roleKey: '',
        status: ''
      }
      this.getList()
    },

    // 刷新
    handleRefresh() {
      this.getList()
    },

    // 新增角色
    handleAdd() {
      this.formData = {}
      this.isEdit = false
      this.formVisible = true
    },

    // 编辑角色
    handleEdit(row) {
      this.formData = { ...row }
      this.isEdit = true
      this.formVisible = true
    },

    // 查看角色
    handleView(row) {
      this.formData = { ...row }
      this.isEdit = false
      this.formVisible = true
    },

    // 表单成功回调
    handleFormSuccess() {
      this.formVisible = false
      this.getList()
    },

    // 多选变化
    handleSelectionChange(selection) {
      this.multipleSelection = selection
    },

    // 分页大小变化
    handleSizeChange(size) {
      this.queryForm.pageSize = size
      this.getList()
    },

    // 当前页变化
    handleCurrentChange(page) {
      this.queryForm.pageNum = page
      this.getList()
    },

    // 下拉菜单命令处理
    async handleCommand(command) {
      const { action, row } = command
      switch (action) {
        case 'changeStatus':
          await this.handleChangeStatus(row)
          break
        case 'delete':
          await this.handleDelete(row)
          break
      }
    },

    // 改变状态
    async handleChangeStatus(row) {
      const status = row.status === '1' ? '0' : '1'
      const statusText = status === '1' ? '启用' : '停用'
      try {
        await this.$confirm(`确认${statusText}该角色？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await changeRoleStatus(row.roleId, status)
        this.$message.success(`${statusText}成功`)
        this.getList()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error(`${statusText}失败`)
        }
      }
    },

    // 删除角色
    async handleDelete(row) {
      try {
        await this.$confirm('确认删除该角色？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await deleteRole(row.roleId)
        this.$message.success('删除成功')
        this.getList()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败')
        }
      }
    },

    // 批量删除
    async handleBatchDelete() {
      if (!this.multipleSelection.length) {
        this.$message.warning('请选择要删除的角色')
        return
      }
      try {
        await this.$confirm(`确认删除选中的${this.multipleSelection.length}个角色？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        // 批量删除逻辑
        for (const role of this.multipleSelection) {
          await deleteRole(role.roleId)
        }
        this.$message.success('批量删除成功')
        this.getList()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('批量删除失败')
        }
      }
    }
  }
}
</script>

<style scoped>
.role-manage {
  padding: 20px;
}

.search-card,
.toolbar-card,
.table-card {
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 0;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .role-manage {
    padding: 10px;
  }
  
  .search-form .el-form-item {
    margin-bottom: 10px;
  }
}

@media (max-width: 800px) {
  .toolbar {
    flex-direction: column;
    gap: 10px;
  }
  
  .toolbar-left,
  .toolbar-right {
    width: 100%;
  }
}
</style>
