package com.jinli.gongwen.service.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinli.gongwen.dto.system.FlowConfigDTO;
import com.jinli.gongwen.dto.system.FlowQueryDTO;
import com.jinli.gongwen.entity.system.FlowConfig;

import java.util.List;

/**
 * 流程配置服务接口
 */
public interface FlowConfigService {

    /**
     * 获取流程配置列表
     */
    IPage<FlowConfig> getFlowConfigList(Page<FlowConfig> page, FlowQueryDTO queryDTO);

    /**
     * 根据ID获取流程配置
     */
    FlowConfig getFlowConfigById(String configId);

    /**
     * 创建流程配置
     */
    String createFlowConfig(FlowConfigDTO configDTO);

    /**
     * 更新流程配置
     */
    void updateFlowConfig(String configId, FlowConfigDTO configDTO);

    /**
     * 删除流程配置
     */
    void deleteFlowConfig(String configId);

    /**
     * 获取所有启用的流程配置
     */
    List<FlowConfig> getAllEnabledFlowConfigs();
}
