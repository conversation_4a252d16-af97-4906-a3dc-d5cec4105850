package com.jinli.gongwen.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jinli.gongwen.entity.DocFlowRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 公文流转记录 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
@Mapper
public interface DocFlowRecordMapper extends BaseMapper<DocFlowRecord> {

    /**
     * 根据公文ID查询流转记录
     *
     * @param docId 公文ID
     * @return 流转记录列表
     */
    @Select("SELECT fr.*, " +
            "fu.REAL_NAME as FROM_USER_NAME, " +
            "tu.REAL_NAME as TO_USER_NAME, " +
            "fd.DEPT_NAME as FROM_DEPT_NAME, " +
            "td.DEPT_NAME as TO_DEPT_NAME " +
            "FROM DOC_FLOW_RECORD fr " +
            "LEFT JOIN SYS_USER fu ON fr.FROM_USER_ID = fu.USER_ID " +
            "LEFT JOIN SYS_USER tu ON fr.TO_USER_ID = tu.USER_ID " +
            "LEFT JOIN SYS_DEPARTMENT fd ON fr.FROM_DEPT_ID = fd.DEPT_ID " +
            "LEFT JOIN SYS_DEPARTMENT td ON fr.TO_DEPT_ID = td.DEPT_ID " +
            "WHERE fr.DOC_ID = #{docId} " +
            "ORDER BY fr.PROCESS_TIME ASC")
    List<DocFlowRecord> selectFlowRecordsByDocId(@Param("docId") String docId);

    /**
     * 根据用户ID查询处理过的公文流转记录
     *
     * @param userId 用户ID
     * @return 流转记录列表
     */
    @Select("SELECT fr.*, " +
            "fu.REAL_NAME as FROM_USER_NAME, " +
            "tu.REAL_NAME as TO_USER_NAME, " +
            "fd.DEPT_NAME as FROM_DEPT_NAME, " +
            "td.DEPT_NAME as TO_DEPT_NAME " +
            "FROM DOC_FLOW_RECORD fr " +
            "LEFT JOIN SYS_USER fu ON fr.FROM_USER_ID = fu.USER_ID " +
            "LEFT JOIN SYS_USER tu ON fr.TO_USER_ID = tu.USER_ID " +
            "LEFT JOIN SYS_DEPARTMENT fd ON fr.FROM_DEPT_ID = fd.DEPT_ID " +
            "LEFT JOIN SYS_DEPARTMENT td ON fr.TO_DEPT_ID = td.DEPT_ID " +
            "WHERE fr.FROM_USER_ID = #{userId} " +
            "ORDER BY fr.PROCESS_TIME DESC")
    List<DocFlowRecord> selectFlowRecordsByUserId(@Param("userId") String userId);

    /**
     * 根据公文ID和用户ID查询用户的处理记录
     *
     * @param docId  公文ID
     * @param userId 用户ID
     * @return 流转记录
     */
    @Select("SELECT fr.*, " +
            "fu.REAL_NAME as FROM_USER_NAME, " +
            "tu.REAL_NAME as TO_USER_NAME, " +
            "fd.DEPT_NAME as FROM_DEPT_NAME, " +
            "td.DEPT_NAME as TO_DEPT_NAME " +
            "FROM DOC_FLOW_RECORD fr " +
            "LEFT JOIN SYS_USER fu ON fr.FROM_USER_ID = fu.USER_ID " +
            "LEFT JOIN SYS_USER tu ON fr.TO_USER_ID = tu.USER_ID " +
            "LEFT JOIN SYS_DEPARTMENT fd ON fr.FROM_DEPT_ID = fd.DEPT_ID " +
            "LEFT JOIN SYS_DEPARTMENT td ON fr.TO_DEPT_ID = td.DEPT_ID " +
            "WHERE fr.DOC_ID = #{docId} AND fr.FROM_USER_ID = #{userId} " +
            "ORDER BY fr.PROCESS_TIME DESC " +
            "LIMIT 1")
    DocFlowRecord selectUserFlowRecord(@Param("docId") String docId, @Param("userId") String userId);

    /**
     * 统计用户处理的公文数量
     *
     * @param userId 用户ID
     * @return 处理数量
     */
    @Select("SELECT COUNT(DISTINCT DOC_ID) FROM DOC_FLOW_RECORD WHERE FROM_USER_ID = #{userId}")
    Long countProcessedDocuments(@Param("userId") String userId);
}
