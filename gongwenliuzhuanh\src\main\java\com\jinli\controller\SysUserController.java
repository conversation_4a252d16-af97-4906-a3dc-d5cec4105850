package com.jinli.controller;

import com.jinli.common.Result;
import com.jinli.dto.UserQueryDTO;
import com.jinli.entity.SysUser;
import com.jinli.service.impl.SysUserServiceSimple;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 用户管理控制器
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
@RestController
@RequestMapping("/system/user")
public class SysUserController {

    private static final Logger log = LoggerFactory.getLogger(SysUserController.class);

    @Autowired
    private SysUserServiceSimple userService;

    /**
     * 分页查询用户列表
     */
    @GetMapping("/list")
    public Result<Map<String, Object>> list(UserQueryDTO queryDTO) {
        log.info("分页查询用户列表，查询条件：{}", queryDTO);
        
        try {
            Map<String, Object> result = userService.selectUserPage(queryDTO);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询用户列表失败", e);
            return Result.error("查询用户列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据用户ID查询用户详情
     */
    @GetMapping("/{userId}")
    public Result<SysUser> getInfo(@PathVariable String userId) {
        log.info("查询用户详情，用户ID：{}", userId);
        
        try {
            if (!StringUtils.hasText(userId)) {
                return Result.error("用户ID不能为空");
            }
            
            SysUser user = userService.selectUserById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }
            
            return Result.success(user);
        } catch (Exception e) {
            log.error("查询用户详情失败", e);
            return Result.error("查询用户详情失败：" + e.getMessage());
        }
    }

    /**
     * 更新用户状态
     */
    @PutMapping("/{userId}/status")
    public Result<Void> updateStatus(@PathVariable String userId, @RequestParam String status) {
        log.info("更新用户状态，用户ID：{}，状态：{}", userId, status);
        
        try {
            if (!StringUtils.hasText(userId)) {
                return Result.error("用户ID不能为空");
            }
            
            if (!StringUtils.hasText(status)) {
                return Result.error("状态不能为空");
            }
            
            boolean success = userService.updateUserStatus(userId, status);
            if (success) {
                return Result.success("更新用户状态成功");
            } else {
                return Result.error("更新用户状态失败");
            }
        } catch (Exception e) {
            log.error("更新用户状态失败", e);
            return Result.error("更新用户状态失败：" + e.getMessage());
        }
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/{userId}")
    public Result<Void> remove(@PathVariable String userId) {
        log.info("删除用户，用户ID：{}", userId);
        
        try {
            if (!StringUtils.hasText(userId)) {
                return Result.error("用户ID不能为空");
            }
            
            boolean success = userService.deleteUser(userId);
            if (success) {
                return Result.success("删除用户成功");
            } else {
                return Result.error("删除用户失败");
            }
        } catch (Exception e) {
            log.error("删除用户失败", e);
            return Result.error("删除用户失败：" + e.getMessage());
        }
    }

    /**
     * 测试接口
     */
    @GetMapping("/test")
    public Result<String> test() {
        log.info("用户管理测试接口被调用");
        return Result.success("用户管理API测试成功！");
    }
}
