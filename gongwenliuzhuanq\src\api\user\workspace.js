import request from '@/utils/request'

/**
 * 用户工作台API
 * 为分厂厂长和部门主任提供统一的API接口
 */

/**
 * 获取用户工作台统计数据
 */
export function getUserStatistics() {
  return request({
    url: '/user/workspace/statistics',
    method: 'get'
  })
}

/**
 * 获取用户最近公文
 */
export function getRecentDocuments(params) {
  return request({
    url: '/user/workspace/recent-documents',
    method: 'get',
    params
  })
}

/**
 * 获取用户待处理公文列表
 */
export function getPendingDocuments(params) {
  return request({
    url: '/user/workspace/pending-documents',
    method: 'get',
    params
  })
}

/**
 * 获取用户已处理公文列表
 */
export function getProcessedDocuments(params) {
  return request({
    url: '/user/workspace/processed-documents',
    method: 'get',
    params
  })
}

/**
 * 获取用户草稿列表
 */
export function getDraftDocuments(params) {
  return request({
    url: '/user/workspace/draft-documents',
    method: 'get',
    params
  })
}

/**
 * 获取公文详情
 */
export function getDocumentDetail(docId) {
  return request({
    url: `/user/workspace/document/${docId}`,
    method: 'get'
  })
}

/**
 * 获取公文流转记录
 */
export function getDocumentFlowHistory(docId) {
  return request({
    url: `/user/workspace/document/${docId}/flow-history`,
    method: 'get'
  })
}

/**
 * 创建公文草稿
 */
export function createDocumentDraft(data) {
  return request({
    url: '/user/workspace/document/draft',
    method: 'post',
    data
  })
}

/**
 * 更新公文草稿
 */
export function updateDocumentDraft(docId, data) {
  return request({
    url: `/user/workspace/document/draft/${docId}`,
    method: 'put',
    data
  })
}

/**
 * 提交公文
 */
export function submitDocument(data) {
  return request({
    url: '/user/workspace/document/submit',
    method: 'post',
    data
  })
}

/**
 * 提交草稿
 */
export function submitDraft(docId) {
  return request({
    url: `/user/workspace/document/draft/${docId}/submit`,
    method: 'post'
  })
}

/**
 * 删除草稿
 */
export function deleteDraft(docId) {
  return request({
    url: `/user/workspace/document/draft/${docId}`,
    method: 'delete'
  })
}

/**
 * 处理公文（审批/退回/转发）
 */
export function processDocument(docId, data) {
  return request({
    url: `/user/workspace/document/${docId}/process`,
    method: 'post',
    data
  })
}

/**
 * 签收公文
 */
export function receiveDocument(docId) {
  return request({
    url: `/user/workspace/document/${docId}/receive`,
    method: 'post'
  })
}

/**
 * 获取可转发的人员列表
 */
export function getTransferableUsers() {
  return request({
    url: '/user/workspace/transferable-users',
    method: 'get'
  })
}

/**
 * 获取公文类型列表
 */
export function getDocumentTypes() {
  return request({
    url: '/user/workspace/document-types',
    method: 'get'
  })
}

/**
 * 下载公文
 */
export function downloadDocument(docId) {
  return request({
    url: `/user/workspace/document/${docId}/download`,
    method: 'get',
    responseType: 'blob'
  })
}

/**
 * 下载附件
 */
export function downloadAttachment(attachmentId) {
  return request({
    url: `/user/workspace/attachment/${attachmentId}/download`,
    method: 'get',
    responseType: 'blob'
  })
}

/**
 * 导出公文列表
 */
export function exportDocuments(params) {
  return request({
    url: '/user/workspace/documents/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 获取公文统计信息
 */
export function getDocumentStatistics() {
  return request({
    url: '/user/workspace/document-statistics',
    method: 'get'
  })
}
