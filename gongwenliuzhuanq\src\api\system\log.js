/**
 * 系统日志相关API
 */
import request from '@/utils/request'

/**
 * 获取系统日志列表
 * @param {Object} params - 查询参数
 * @returns {Promise} API响应
 */
export const getLogList = (params = {}) => {
  return request({
    url: '/system/log/list',
    method: 'get',
    params
  })
}

/**
 * 获取日志详情
 * @param {string} logId - 日志ID
 * @returns {Promise} API响应
 */
export const getLogDetail = (logId) => {
  return request({
    url: `/system/log/${logId}`,
    method: 'get'
  })
}

/**
 * 删除日志
 * @param {string} logId - 日志ID
 * @returns {Promise} API响应
 */
export const deleteLog = (logId) => {
  return request({
    url: `/system/log/${logId}`,
    method: 'delete'
  })
}

/**
 * 批量删除日志
 * @param {Array} logIds - 日志ID数组
 * @returns {Promise} API响应
 */
export const batchDeleteLogs = (logIds) => {
  return request({
    url: '/system/log/batch',
    method: 'delete',
    data: { logIds }
  })
}

/**
 * 清空所有日志
 * @returns {Promise} API响应
 */
export const clearAllLogs = () => {
  return request({
    url: '/system/log/clear',
    method: 'delete'
  })
}

/**
 * 导出日志
 * @param {Object} params - 导出参数
 * @returns {Promise} API响应
 */
export const exportLogs = (params = {}) => {
  return request({
    url: '/system/log/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 获取日志统计信息
 * @returns {Promise} API响应
 */
export const getLogStatistics = () => {
  return request({
    url: '/system/log/statistics',
    method: 'get'
  })
}

/**
 * 获取操作模块列表
 * @returns {Promise} API响应
 */
export const getLogModules = () => {
  return request({
    url: '/system/log/modules',
    method: 'get'
  })
}

/**
 * 获取操作类型列表
 * @returns {Promise} API响应
 */
export const getLogOperationTypes = () => {
  return request({
    url: '/system/log/operation-types',
    method: 'get'
  })
}
