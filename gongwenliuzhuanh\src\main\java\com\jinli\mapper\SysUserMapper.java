package com.jinli.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jinli.dto.UserQueryDTO;
import com.jinli.entity.SysUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-30
 */
@Mapper
public interface SysUserMapper extends BaseMapper<SysUser> {

    /**
     * 查询用户列表
     * 
     * @param queryDTO 查询条件
     * @return 用户列表
     */
    List<SysUser> selectUserList(@Param("query") UserQueryDTO queryDTO);

    /**
     * 查询用户总数
     * 
     * @param queryDTO 查询条件
     * @return 用户总数
     */
    Long selectUserCount(@Param("query") UserQueryDTO queryDTO);

    /**
     * 根据用户ID查询用户详情
     * 
     * @param userId 用户ID
     * @return 用户信息
     */
    SysUser selectUserById(@Param("userId") String userId);

    /**
     * 根据用户名查询用户
     * 
     * @param username 用户名
     * @return 用户信息
     */
    SysUser selectUserByUsername(@Param("username") String username);

    /**
     * 检查用户名是否存在
     * 
     * @param username 用户名
     * @param excludeUserId 排除的用户ID
     * @return 存在的数量
     */
    Integer checkUsernameExists(@Param("username") String username, @Param("excludeUserId") String excludeUserId);

    /**
     * 检查邮箱是否存在
     * 
     * @param email 邮箱
     * @param excludeUserId 排除的用户ID
     * @return 存在的数量
     */
    Integer checkEmailExists(@Param("email") String email, @Param("excludeUserId") String excludeUserId);

    /**
     * 检查手机号是否存在
     * 
     * @param phone 手机号
     * @param excludeUserId 排除的用户ID
     * @return 存在的数量
     */
    Integer checkPhoneExists(@Param("phone") String phone, @Param("excludeUserId") String excludeUserId);

    /**
     * 更新用户状态
     * 
     * @param userId 用户ID
     * @param status 状态
     * @return 更新数量
     */
    Integer updateUserStatus(@Param("userId") String userId, @Param("status") String status);

    /**
     * 更新用户登录信息
     * 
     * @param userId 用户ID
     * @param loginIp 登录IP
     * @return 更新数量
     */
    Integer updateUserLoginInfo(@Param("userId") String userId, @Param("loginIp") String loginIp);

    /**
     * 重置用户密码
     * 
     * @param userId 用户ID
     * @param password 新密码
     * @return 更新数量
     */
    Integer resetUserPassword(@Param("userId") String userId, @Param("password") String password);

    /**
     * 批量删除用户
     * 
     * @param userIds 用户ID列表
     * @return 删除数量
     */
    Integer batchDeleteUsers(@Param("userIds") List<String> userIds);

    /**
     * 查询部门下的用户列表
     * 
     * @param departmentId 部门ID
     * @return 用户列表
     */
    List<SysUser> selectUsersByDepartmentId(@Param("departmentId") String departmentId);

    /**
     * 查询角色下的用户列表
     * 
     * @param roleId 角色ID
     * @return 用户列表
     */
    List<SysUser> selectUsersByRoleId(@Param("roleId") String roleId);

    /**
     * 查询用户的角色列表
     * 
     * @param userId 用户ID
     * @return 角色ID列表
     */
    List<String> selectUserRoleIds(@Param("userId") String userId);

    /**
     * 查询用户的权限列表
     * 
     * @param userId 用户ID
     * @return 权限列表
     */
    List<String> selectUserPermissions(@Param("userId") String userId);

    /**
     * 删除用户角色关联
     * 
     * @param userId 用户ID
     * @return 删除数量
     */
    Integer deleteUserRoles(@Param("userId") String userId);

    /**
     * 批量插入用户角色关联
     * 
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 插入数量
     */
    Integer insertUserRoles(@Param("userId") String userId, @Param("roleIds") List<String> roleIds);

    /**
     * 查询启用的用户列表
     * 
     * @return 启用的用户列表
     */
    List<SysUser> selectEnabledUsers();

    /**
     * 查询用户统计信息
     * 
     * @return 统计信息
     */
    List<SysUser> selectUserStatistics();
}
