package com.jinli.gongwen.dto.document;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 公文审批数据传输对象
 */
@Data
@ApiModel("公文审批数据传输对象")
public class DocumentApprovalDTO {

    @ApiModelProperty("公文ID")
    private String docId;

    @ApiModelProperty("审批用户ID")
    private String approvalUserId;

    @ApiModelProperty(value = "审批结果", required = true)
    @NotNull(message = "审批结果不能为空")
    private Boolean isApproved;

    @ApiModelProperty(value = "审批意见", required = true)
    @NotBlank(message = "审批意见不能为空")
    @Size(max = 500, message = "审批意见长度不能超过500个字符")
    private String approvalOpinion;

    @ApiModelProperty("审批类型")
    private String approvalType = "DIRECTOR_APPROVE"; // DIRECTOR_APPROVE

    @ApiModelProperty("决策级别")
    private String decisionLevel; // STRATEGIC, OPERATIONAL, ROUTINE

    @ApiModelProperty("执行指示")
    @Size(max = 500, message = "执行指示长度不能超过500个字符")
    private String executionInstruction;

    @ApiModelProperty("是否需要跟踪执行")
    private Boolean needTracking = false;

    @ApiModelProperty("跟踪要求")
    @Size(max = 500, message = "跟踪要求长度不能超过500个字符")
    private String trackingRequirement;

    @ApiModelProperty("优先级调整")
    private String priorityAdjustment; // HIGH, NORMAL, LOW

    @ApiModelProperty("相关部门建议")
    @Size(max = 500, message = "相关部门建议长度不能超过500个字符")
    private String departmentSuggestion;

    @ApiModelProperty("预算影响评估")
    @Size(max = 500, message = "预算影响评估长度不能超过500个字符")
    private String budgetImpact;

    @ApiModelProperty("风险评估")
    @Size(max = 500, message = "风险评估长度不能超过500个字符")
    private String riskAssessment;

    @ApiModelProperty("备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;
}
