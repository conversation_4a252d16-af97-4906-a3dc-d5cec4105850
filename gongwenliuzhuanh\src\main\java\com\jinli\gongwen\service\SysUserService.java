package com.jinli.gongwen.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jinli.gongwen.entity.SysUser;

import java.util.List;

/**
 * 用户信息表 服务类
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
public interface SysUserService extends IService<SysUser> {

    /**
     * 用户登录
     * 
     * @param username 用户名
     * @param password 密码
     * @param loginIp 登录IP
     * @return JWT Token
     */
    String login(String username, String password, String loginIp);

    /**
     * 根据用户名查询用户信息
     * 
     * @param username 用户名
     * @return 用户信息
     */
    SysUser getUserByUsername(String username);

    /**
     * 根据用户ID查询用户详细信息
     * 
     * @param userId 用户ID
     * @return 用户详细信息
     */
    SysUser getUserDetailById(String userId);

    /**
     * 分页查询用户列表
     * 
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param username 用户名（模糊查询）
     * @param realName 真实姓名（模糊查询）
     * @param departmentId 部门ID
     * @param status 状态
     * @return 用户分页列表
     */
    IPage<SysUser> getUserPage(Integer pageNum, Integer pageSize, String username, 
                               String realName, String departmentId, String status);

    /**
     * 创建用户
     * 
     * @param user 用户信息
     * @param roleId 角色ID
     * @return 是否成功
     */
    boolean createUser(SysUser user, String roleId);

    /**
     * 更新用户信息
     * 
     * @param user 用户信息
     * @param roleId 角色ID
     * @return 是否成功
     */
    boolean updateUser(SysUser user, String roleId);

    /**
     * 删除用户
     * 
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean deleteUser(String userId);

    /**
     * 批量删除用户
     * 
     * @param userIds 用户ID列表
     * @return 是否成功
     */
    boolean deleteUsers(List<String> userIds);

    /**
     * 重置用户密码
     * 
     * @param userId 用户ID
     * @param newPassword 新密码
     * @return 是否成功
     */
    boolean resetPassword(String userId, String newPassword);

    /**
     * 修改用户密码
     * 
     * @param userId 用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 是否成功
     */
    boolean changePassword(String userId, String oldPassword, String newPassword);

    /**
     * 启用/禁用用户
     * 
     * @param userId 用户ID
     * @param status 状态
     * @return 是否成功
     */
    boolean changeUserStatus(String userId, String status);

    /**
     * 检查用户名是否唯一
     * 
     * @param username 用户名
     * @param userId 用户ID（排除自己）
     * @return 是否唯一
     */
    boolean checkUsernameUnique(String username, String userId);

    /**
     * 根据部门ID查询用户列表
     * 
     * @param departmentId 部门ID
     * @return 用户列表
     */
    List<SysUser> getUsersByDepartmentId(String departmentId);

    /**
     * 根据角色键查询用户列表
     * 
     * @param roleKey 角色键
     * @return 用户列表
     */
    List<SysUser> getUsersByRoleKey(String roleKey);

    /**
     * 获取所有正常状态的用户
     * 
     * @return 用户列表
     */
    List<SysUser> getAllActiveUsers();

    /**
     * 更新用户个人信息
     * 
     * @param user 用户信息
     * @return 是否成功
     */
    boolean updateProfile(SysUser user);

    /**
     * 获取用户统计信息
     * 
     * @return 统计信息
     */
    Object getUserStatistics();
}
