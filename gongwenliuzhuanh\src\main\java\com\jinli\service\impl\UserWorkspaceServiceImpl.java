package com.jinli.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinli.gongwen.entity.DocDocument;
import com.jinli.gongwen.entity.DocFlowRecord;
import com.jinli.gongwen.entity.DocReceiveRecord;
import com.jinli.gongwen.entity.SysUser;
import com.jinli.gongwen.mapper.DocDocumentMapper;
import com.jinli.gongwen.mapper.DocFlowRecordMapper;
import com.jinli.gongwen.mapper.DocReceiveRecordMapper;
import com.jinli.gongwen.mapper.SysUserMapper;
import com.jinli.service.IUserWorkspaceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.Date;

/**
 * 用户工作台服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
@Service
public class UserWorkspaceServiceImpl implements IUserWorkspaceService {

    private static final Logger log = LoggerFactory.getLogger(UserWorkspaceServiceImpl.class);

    @Autowired
    private DocDocumentMapper docDocumentMapper;

    @Autowired
    private DocFlowRecordMapper docFlowRecordMapper;

    @Autowired
    private DocReceiveRecordMapper docReceiveRecordMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Override
    public Map<String, Object> getUserStatistics(String userId) {
        log.info("获取用户统计数据，用户ID：{}", userId);
        
        Map<String, Object> statistics = new HashMap<>();
        
        try {
            // 待处理公文数量
            QueryWrapper<DocDocument> pendingWrapper = new QueryWrapper<>();
            pendingWrapper.eq("CURRENT_HANDLER", userId)
                         .in("DOC_STATUS", "PROCESSING");
            long pendingCount = docDocumentMapper.selectCount(pendingWrapper);
            statistics.put("pendingCount", pendingCount);

            // 已处理公文数量
            QueryWrapper<DocFlowRecord> processedWrapper = new QueryWrapper<>();
            processedWrapper.eq("FROM_USER_ID", userId);
            long processedCount = docFlowRecordMapper.selectCount(processedWrapper);
            statistics.put("processedCount", processedCount);

            // 草稿数量
            QueryWrapper<DocDocument> draftWrapper = new QueryWrapper<>();
            draftWrapper.eq("CREATE_USER_ID", userId)
                       .eq("DOC_STATUS", "DRAFT");
            long draftCount = docDocumentMapper.selectCount(draftWrapper);
            statistics.put("draftCount", draftCount);

            // 本月创建公文数量
            LocalDateTime monthStart = LocalDateTime.now().withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
            QueryWrapper<DocDocument> monthWrapper = new QueryWrapper<>();
            monthWrapper.eq("CREATE_USER_ID", userId)
                       .ge("CREATE_TIME", monthStart);
            long monthCount = docDocumentMapper.selectCount(monthWrapper);
            statistics.put("monthCount", monthCount);

        } catch (Exception e) {
            log.error("获取用户统计数据失败", e);
        }

        return statistics;
    }

    @Override
    public Map<String, Object> getRecentDocuments(String userId, Integer pageNum, Integer pageSize) {
        log.info("获取用户最近公文，用户ID：{}，页码：{}，每页大小：{}", userId, pageNum, pageSize);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            Page<DocDocument> page = new Page<>(pageNum, pageSize);
            
            // 查询用户相关的公文（创建的或处理过的）
            QueryWrapper<DocDocument> wrapper = new QueryWrapper<>();
            wrapper.and(w -> w.eq("CREATE_USER_ID", userId)
                           .or()
                           .eq("CURRENT_HANDLER", userId))
                   .orderByDesc("UPDATE_TIME");
            
            IPage<DocDocument> pageResult = docDocumentMapper.selectPage(page, wrapper);
            
            result.put("records", pageResult.getRecords());
            result.put("total", pageResult.getTotal());
            result.put("current", pageResult.getCurrent());
            result.put("size", pageResult.getSize());
            
        } catch (Exception e) {
            log.error("获取用户最近公文失败", e);
            result.put("records", new ArrayList<>());
            result.put("total", 0);
        }
        
        return result;
    }

    @Override
    public Map<String, Object> getPendingDocuments(String userId, Integer pageNum, Integer pageSize,
                                                  String docTitle, String docType, String docLevel) {
        log.info("获取用户待处理公文，用户ID：{}，页码：{}，每页大小：{}", userId, pageNum, pageSize);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            Page<DocDocument> page = new Page<>(pageNum, pageSize);
            
            QueryWrapper<DocDocument> wrapper = new QueryWrapper<>();
            wrapper.eq("CURRENT_HANDLER", userId)
                   .in("DOC_STATUS", "PROCESSING");
            
            // 添加筛选条件
            if (StringUtils.hasText(docTitle)) {
                wrapper.like("DOC_TITLE", docTitle);
            }
            if (StringUtils.hasText(docType)) {
                wrapper.eq("DOC_TYPE", docType);
            }
            if (StringUtils.hasText(docLevel)) {
                wrapper.eq("DOC_LEVEL", docLevel);
            }
            
            wrapper.orderByDesc("CREATE_TIME");
            
            IPage<DocDocument> pageResult = docDocumentMapper.selectPage(page, wrapper);
            
            result.put("records", pageResult.getRecords());
            result.put("total", pageResult.getTotal());
            result.put("current", pageResult.getCurrent());
            result.put("size", pageResult.getSize());
            
        } catch (Exception e) {
            log.error("获取用户待处理公文失败", e);
            result.put("records", new ArrayList<>());
            result.put("total", 0);
        }
        
        return result;
    }

    @Override
    public Map<String, Object> getProcessedDocuments(String userId, Integer pageNum, Integer pageSize,
                                                    String docTitle, String docType, String docStatus,
                                                    String startDate, String endDate) {
        log.info("获取用户已处理公文，用户ID：{}，页码：{}，每页大小：{}", userId, pageNum, pageSize);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            Page<DocDocument> page = new Page<>(pageNum, pageSize);
            
            // 通过流转记录查询用户处理过的公文
            QueryWrapper<DocFlowRecord> flowWrapper = new QueryWrapper<>();
            flowWrapper.eq("FROM_USER_ID", userId);
            
            List<DocFlowRecord> flowRecords = docFlowRecordMapper.selectList(flowWrapper);
            if (flowRecords.isEmpty()) {
                result.put("records", new ArrayList<>());
                result.put("total", 0);
                return result;
            }
            
            List<String> docIds = new ArrayList<>();
            for (DocFlowRecord record : flowRecords) {
                docIds.add(record.getDocId());
            }
            
            QueryWrapper<DocDocument> wrapper = new QueryWrapper<>();
            wrapper.in("DOC_ID", docIds);
            
            // 添加筛选条件
            if (StringUtils.hasText(docTitle)) {
                wrapper.like("DOC_TITLE", docTitle);
            }
            if (StringUtils.hasText(docType)) {
                wrapper.eq("DOC_TYPE", docType);
            }
            if (StringUtils.hasText(docStatus)) {
                wrapper.eq("DOC_STATUS", docStatus);
            }
            if (StringUtils.hasText(startDate)) {
                wrapper.ge("CREATE_TIME", startDate);
            }
            if (StringUtils.hasText(endDate)) {
                wrapper.le("CREATE_TIME", endDate);
            }
            
            wrapper.orderByDesc("UPDATE_TIME");
            
            IPage<DocDocument> pageResult = docDocumentMapper.selectPage(page, wrapper);
            
            result.put("records", pageResult.getRecords());
            result.put("total", pageResult.getTotal());
            result.put("current", pageResult.getCurrent());
            result.put("size", pageResult.getSize());
            
        } catch (Exception e) {
            log.error("获取用户已处理公文失败", e);
            result.put("records", new ArrayList<>());
            result.put("total", 0);
        }
        
        return result;
    }

    @Override
    public Map<String, Object> getDraftDocuments(String userId, Integer pageNum, Integer pageSize) {
        log.info("获取用户草稿列表，用户ID：{}，页码：{}，每页大小：{}", userId, pageNum, pageSize);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            Page<DocDocument> page = new Page<>(pageNum, pageSize);
            
            QueryWrapper<DocDocument> wrapper = new QueryWrapper<>();
            wrapper.eq("CREATE_USER_ID", userId)
                   .eq("DOC_STATUS", "DRAFT")
                   .orderByDesc("UPDATE_TIME");
            
            IPage<DocDocument> pageResult = docDocumentMapper.selectPage(page, wrapper);
            
            result.put("records", pageResult.getRecords());
            result.put("total", pageResult.getTotal());
            result.put("current", pageResult.getCurrent());
            result.put("size", pageResult.getSize());
            
        } catch (Exception e) {
            log.error("获取用户草稿列表失败", e);
            result.put("records", new ArrayList<>());
            result.put("total", 0);
        }
        
        return result;
    }

    @Override
    public Map<String, Object> getDocumentDetail(String docId, String userId) {
        log.info("获取公文详情，公文ID：{}，用户ID：{}", docId, userId);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            DocDocument document = docDocumentMapper.selectById(docId);
            if (document == null) {
                throw new RuntimeException("公文不存在");
            }
            
            result.put("document", document);
            
            // 获取流转记录
            QueryWrapper<DocFlowRecord> flowWrapper = new QueryWrapper<>();
            flowWrapper.eq("DOC_ID", docId).orderByAsc("PROCESS_TIME");
            List<DocFlowRecord> flowRecords = docFlowRecordMapper.selectList(flowWrapper);
            result.put("flowHistory", flowRecords);
            
            // 获取签收记录
            QueryWrapper<DocReceiveRecord> receiveWrapper = new QueryWrapper<>();
            receiveWrapper.eq("DOC_ID", docId).orderByDesc("RECEIVE_TIME");
            List<DocReceiveRecord> receiveRecords = docReceiveRecordMapper.selectList(receiveWrapper);
            result.put("receiveRecords", receiveRecords);
            
            // 获取用户的处理意见
            QueryWrapper<DocFlowRecord> myOpinionWrapper = new QueryWrapper<>();
            myOpinionWrapper.eq("DOC_ID", docId).eq("FROM_USER_ID", userId);
            DocFlowRecord myOpinion = docFlowRecordMapper.selectOne(myOpinionWrapper);
            result.put("myOpinion", myOpinion);
            
        } catch (Exception e) {
            log.error("获取公文详情失败", e);
            throw new RuntimeException("获取公文详情失败：" + e.getMessage());
        }
        
        return result;
    }

    @Override
    public Map<String, Object> getDocumentFlowHistory(String docId) {
        log.info("获取公文流转记录，公文ID：{}", docId);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            QueryWrapper<DocFlowRecord> wrapper = new QueryWrapper<>();
            wrapper.eq("DOC_ID", docId).orderByAsc("PROCESS_TIME");
            
            List<DocFlowRecord> flowRecords = docFlowRecordMapper.selectList(wrapper);
            result.put("flowHistory", flowRecords);
            
        } catch (Exception e) {
            log.error("获取公文流转记录失败", e);
            result.put("flowHistory", new ArrayList<>());
        }
        
        return result;
    }

    @Override
    @Transactional
    public String createDocumentDraft(Map<String, Object> documentData, String userId) {
        log.info("创建公文草稿，用户ID：{}", userId);
        
        try {
            DocDocument document = new DocDocument();
            document.setDocId(UUID.randomUUID().toString().replace("-", ""));
            document.setDocTitle((String) documentData.get("docTitle"));
            document.setDocType((String) documentData.get("docType"));
            document.setDocLevel((String) documentData.get("docLevel"));
            document.setDocContent((String) documentData.get("docContent"));
            document.setDocStatus("DRAFT");
            document.setCreateUserId(userId);
            
            // 获取用户部门信息
            SysUser user = sysUserMapper.selectById(userId);
            if (user != null) {
                document.setCreateDeptId(user.getDepartmentId());
            }
            
            document.setCreateTime(new Date());
            document.setUpdateTime(new Date());
            document.setRemark((String) documentData.get("remark"));
            
            docDocumentMapper.insert(document);
            
            log.info("公文草稿创建成功，公文ID：{}", document.getDocId());
            return document.getDocId();
            
        } catch (Exception e) {
            log.error("创建公文草稿失败", e);
            throw new RuntimeException("创建公文草稿失败：" + e.getMessage());
        }
    }

    // 其他方法的实现将在下一部分继续...
    
    @Override
    @Transactional
    public void updateDocumentDraft(String docId, Map<String, Object> documentData, String userId) {
        log.info("更新公文草稿，公文ID：{}，用户ID：{}", docId, userId);

        try {
            DocDocument document = docDocumentMapper.selectById(docId);
            if (document == null) {
                throw new RuntimeException("公文不存在");
            }

            if (!userId.equals(document.getCreateUserId())) {
                throw new RuntimeException("无权限修改此公文");
            }

            if (!"DRAFT".equals(document.getDocStatus())) {
                throw new RuntimeException("只能修改草稿状态的公文");
            }

            document.setDocTitle((String) documentData.get("docTitle"));
            document.setDocType((String) documentData.get("docType"));
            document.setDocLevel((String) documentData.get("docLevel"));
            document.setDocContent((String) documentData.get("docContent"));
            document.setUpdateTime(new Date());
            document.setRemark((String) documentData.get("remark"));

            docDocumentMapper.updateById(document);

            log.info("公文草稿更新成功，公文ID：{}", docId);

        } catch (Exception e) {
            log.error("更新公文草稿失败", e);
            throw new RuntimeException("更新公文草稿失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional
    public String submitDocument(Map<String, Object> documentData, String userId) {
        log.info("提交公文，用户ID：{}", userId);

        try {
            // 先创建草稿
            String docId = createDocumentDraft(documentData, userId);

            // 然后提交
            submitDraft(docId, userId);

            return docId;

        } catch (Exception e) {
            log.error("提交公文失败", e);
            throw new RuntimeException("提交公文失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional
    public void submitDraft(String docId, String userId) {
        log.info("提交草稿，公文ID：{}，用户ID：{}", docId, userId);

        try {
            DocDocument document = docDocumentMapper.selectById(docId);
            if (document == null) {
                throw new RuntimeException("公文不存在");
            }

            if (!userId.equals(document.getCreateUserId())) {
                throw new RuntimeException("无权限提交此公文");
            }

            if (!"DRAFT".equals(document.getDocStatus())) {
                throw new RuntimeException("只能提交草稿状态的公文");
            }

            // 生成公文编号
            String docNumber = generateDocumentNumber();
            document.setDocNumber(docNumber);
            document.setDocStatus("PROCESSING");
            document.setCurrentStep("VICE_REVIEW");

            // 根据用户部门确定下一步处理人
            String nextHandler = getNextHandler(userId);
            document.setCurrentHandler(nextHandler);
            document.setUpdateTime(new Date());

            docDocumentMapper.updateById(document);

            // 创建流转记录
            DocFlowRecord flowRecord = new DocFlowRecord();
            flowRecord.setRecordId(UUID.randomUUID().toString().replace("-", ""));
            flowRecord.setDocId(docId);
            flowRecord.setFromUserId(userId);
            flowRecord.setToUserId(nextHandler);
            flowRecord.setFlowStep("VICE_REVIEW");
            flowRecord.setAction("SUBMIT");
            flowRecord.setOpinion("提交公文进行审批");
            flowRecord.setProcessTime(LocalDateTime.now());

            docFlowRecordMapper.insert(flowRecord);

            log.info("草稿提交成功，公文ID：{}", docId);

        } catch (Exception e) {
            log.error("提交草稿失败", e);
            throw new RuntimeException("提交草稿失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional
    public void deleteDraft(String docId, String userId) {
        log.info("删除草稿，公文ID：{}，用户ID：{}", docId, userId);

        try {
            DocDocument document = docDocumentMapper.selectById(docId);
            if (document == null) {
                throw new RuntimeException("公文不存在");
            }

            if (!userId.equals(document.getCreateUserId())) {
                throw new RuntimeException("无权限删除此公文");
            }

            if (!"DRAFT".equals(document.getDocStatus())) {
                throw new RuntimeException("只能删除草稿状态的公文");
            }

            docDocumentMapper.deleteById(docId);

            log.info("草稿删除成功，公文ID：{}", docId);

        } catch (Exception e) {
            log.error("删除草稿失败", e);
            throw new RuntimeException("删除草稿失败：" + e.getMessage());
        }
    }

    @Override
    public void processDocument(String docId, Map<String, Object> processData, String userId) {
        // TODO: 实现处理公文逻辑
    }

    @Override
    public void receiveDocument(String docId, String userId) {
        // TODO: 实现签收公文逻辑
    }

    @Override
    public Map<String, Object> getTransferableUsers(String userId) {
        // TODO: 实现获取可转发人员逻辑
        return new HashMap<>();
    }

    @Override
    public Map<String, Object> getDocumentTypes() {
        // TODO: 实现获取公文类型逻辑
        return new HashMap<>();
    }

    @Override
    public Map<String, Object> downloadDocument(String docId, String userId) {
        // TODO: 实现下载公文逻辑
        return new HashMap<>();
    }

    @Override
    public Map<String, Object> downloadAttachment(String attachmentId, String userId) {
        // TODO: 实现下载附件逻辑
        return new HashMap<>();
    }

    @Override
    public Map<String, Object> exportDocuments(String userId, Map<String, Object> filterData) {
        // TODO: 实现导出公文逻辑
        return new HashMap<>();
    }

    @Override
    public Map<String, Object> getDocumentStatistics(String userId) {
        // TODO: 实现获取公文统计逻辑
        return new HashMap<>();
    }

    /**
     * 生成公文编号
     */
    private String generateDocumentNumber() {
        LocalDateTime now = LocalDateTime.now();
        String dateStr = now.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        // 简单的序号生成，实际应该从数据库获取当日最大序号
        String sequence = String.format("%03d", (int)(Math.random() * 999) + 1);
        return "JINLI-" + dateStr + "-" + sequence;
    }

    /**
     * 根据用户ID获取下一步处理人
     */
    private String getNextHandler(String userId) {
        try {
            SysUser user = sysUserMapper.selectById(userId);
            if (user == null) {
                throw new RuntimeException("用户不存在");
            }

            // 根据用户部门确定下一步处理人
            // 这里简化处理，实际应该根据具体的业务规则
            String deptId = user.getDepartmentId();

            // 查找对应的副厂长
            QueryWrapper<SysUser> wrapper = new QueryWrapper<>();
            wrapper.eq("DEPARTMENT_ID", "DEPT011") // 生产管理部
                   .eq("STATUS", "1");

            List<SysUser> viceDirectors = sysUserMapper.selectList(wrapper);
            if (!viceDirectors.isEmpty()) {
                return viceDirectors.get(0).getUserId();
            }

            // 如果没有找到副厂长，返回厂长
            wrapper.clear();
            wrapper.eq("DEPARTMENT_ID", "DEPT010") // 厂长办公室
                   .eq("STATUS", "1");

            List<SysUser> directors = sysUserMapper.selectList(wrapper);
            if (!directors.isEmpty()) {
                return directors.get(0).getUserId();
            }

            throw new RuntimeException("未找到合适的处理人");

        } catch (Exception e) {
            log.error("获取下一步处理人失败", e);
            throw new RuntimeException("获取下一步处理人失败：" + e.getMessage());
        }
    }
}
