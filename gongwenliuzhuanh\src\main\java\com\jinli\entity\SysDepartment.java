package com.jinli.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 部门实体类
 * 
 * <AUTHOR>
 * @since 2025-01-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYS_DEPARTMENT")
public class SysDepartment implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 部门ID
     */
    @TableId(value = "DEPT_ID", type = IdType.ASSIGN_ID)
    private String deptId;

    /**
     * 部门名称
     */
    @TableField("DEPT_NAME")
    private String deptName;

    /**
     * 部门编码
     */
    @TableField("DEPT_CODE")
    private String deptCode;

    /**
     * 父部门ID
     */
    @TableField("PARENT_ID")
    private String parentId;

    /**
     * 负责人
     */
    @TableField("LEADER")
    private String leader;

    /**
     * 联系电话
     */
    @TableField("PHONE")
    private String phone;

    /**
     * 邮箱
     */
    @TableField("EMAIL")
    private String email;

    /**
     * 显示顺序
     */
    @TableField("SORT")
    private Integer sort;

    /**
     * 部门状态（0正常 1停用）
     */
    @TableField("STATUS")
    private String status;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("UPDATE_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 子部门列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<SysDepartment> children = new ArrayList<>();

    /**
     * 父部门名称（非数据库字段）
     */
    @TableField(exist = false)
    private String parentName;

    /**
     * 是否有子节点（非数据库字段）
     */
    @TableField(exist = false)
    private Boolean hasChildren = false;

    /**
     * 部门层级（非数据库字段）
     */
    @TableField(exist = false)
    private Integer level;

    /**
     * 部门路径（非数据库字段）
     */
    @TableField(exist = false)
    private String deptPath;

    /**
     * 用户数量（非数据库字段）
     */
    @TableField(exist = false)
    private Integer userCount;

    /**
     * 是否为叶子节点
     */
    public Boolean isLeaf() {
        return children == null || children.isEmpty();
    }

    /**
     * 添加子部门
     */
    public void addChild(SysDepartment child) {
        if (children == null) {
            children = new ArrayList<>();
        }
        children.add(child);
        this.hasChildren = true;
    }

    /**
     * 获取所有祖先部门ID
     */
    public List<String> getAncestorIds() {
        List<String> ancestorIds = new ArrayList<>();
        if (parentId != null && !parentId.isEmpty()) {
            ancestorIds.add(parentId);
        }
        return ancestorIds;
    }

    /**
     * 检查是否为根部门
     */
    public Boolean isRoot() {
        return parentId == null || parentId.isEmpty() || "0".equals(parentId);
    }

    /**
     * 构建部门路径
     */
    public void buildDeptPath(String parentPath) {
        if (parentPath == null || parentPath.isEmpty()) {
            this.deptPath = this.deptName;
        } else {
            this.deptPath = parentPath + " > " + this.deptName;
        }
    }

    /**
     * 设置部门层级
     */
    public void setLevelByParent(Integer parentLevel) {
        this.level = parentLevel == null ? 0 : parentLevel + 1;
    }

    /**
     * 获取格式化的创建时间
     */
    public String getCreateTimeStr() {
        return createTime != null ? createTime.toString() : "";
    }

    /**
     * 获取格式化的更新时间
     */
    public String getUpdateTimeStr() {
        return updateTime != null ? updateTime.toString() : "";
    }

    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        return "1".equals(status) ? "正常" : "停用";
    }

    /**
     * 检查部门状态是否正常
     */
    public Boolean isNormal() {
        return "1".equals(status);
    }

    /**
     * 检查部门状态是否停用
     */
    public Boolean isDisabled() {
        return "0".equals(status);
    }

    // 手动添加getter/setter方法（以防Lombok不工作）
    public String getDeptId() { return deptId; }
    public void setDeptId(String deptId) { this.deptId = deptId; }

    public String getDeptName() { return deptName; }
    public void setDeptName(String deptName) { this.deptName = deptName; }

    public String getDeptCode() { return deptCode; }
    public void setDeptCode(String deptCode) { this.deptCode = deptCode; }

    public String getParentId() { return parentId; }
    public void setParentId(String parentId) { this.parentId = parentId; }

    public String getLeader() { return leader; }
    public void setLeader(String leader) { this.leader = leader; }

    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }

    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }

    public Integer getSort() { return sort; }
    public void setSort(Integer sort) { this.sort = sort; }

    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }

    public LocalDateTime getCreateTime() { return createTime; }
    public void setCreateTime(LocalDateTime createTime) { this.createTime = createTime; }

    public LocalDateTime getUpdateTime() { return updateTime; }
    public void setUpdateTime(LocalDateTime updateTime) { this.updateTime = updateTime; }

    public String getRemark() { return remark; }
    public void setRemark(String remark) { this.remark = remark; }

    public List<SysDepartment> getChildren() { return children; }
    public void setChildren(List<SysDepartment> children) { this.children = children; }

    public String getParentName() { return parentName; }
    public void setParentName(String parentName) { this.parentName = parentName; }

    public Boolean getHasChildren() { return hasChildren; }
    public void setHasChildren(Boolean hasChildren) { this.hasChildren = hasChildren; }

    public Integer getLevel() { return level; }
    public void setLevel(Integer level) { this.level = level; }

    public String getDeptPath() { return deptPath; }
    public void setDeptPath(String deptPath) { this.deptPath = deptPath; }

    public Integer getUserCount() { return userCount; }
    public void setUserCount(Integer userCount) { this.userCount = userCount; }
}
