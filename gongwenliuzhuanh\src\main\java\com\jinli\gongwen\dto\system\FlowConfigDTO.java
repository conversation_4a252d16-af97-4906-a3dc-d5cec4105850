package com.jinli.gongwen.dto.system;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 流程配置数据传输对象
 */
@Data
@ApiModel("流程配置数据传输对象")
public class FlowConfigDTO {

    @ApiModelProperty("配置ID")
    private String configId;

    @ApiModelProperty(value = "流程名称", required = true)
    @NotBlank(message = "流程名称不能为空")
    private String flowName;

    @ApiModelProperty("流程描述")
    private String flowDescription;

    @ApiModelProperty("流程步骤")
    private String flowSteps;

    @ApiModelProperty("是否启用")
    private Boolean isEnabled = true;

    @ApiModelProperty("备注")
    private String remark;
}
