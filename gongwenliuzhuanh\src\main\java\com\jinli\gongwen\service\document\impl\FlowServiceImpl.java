package com.jinli.gongwen.service.document.impl;

import com.jinli.gongwen.dto.document.DocumentFlowDTO;
import com.jinli.gongwen.service.document.FlowService;
import com.jinli.gongwen.vo.document.DocumentVO;
import com.jinli.gongwen.vo.document.FlowRecordVO;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 流转服务实现类
 */
@Service
public class FlowServiceImpl implements FlowService {

    @Override
    public void processFlow(DocumentFlowDTO flowDTO) {
        // TODO: 实现具体逻辑
    }

    @Override
    public List<FlowRecordVO> getFlowRecords(String docId) {
        // TODO: 实现具体逻辑
        return new ArrayList<>();
    }

    @Override
    public List<DocumentVO> getFlowStatusOverview() {
        // TODO: 实现具体逻辑
        return new ArrayList<>();
    }

    @Override
    public void returnDocument(String docId, String reason) {
        // TODO: 实现具体逻辑
    }
}
