// 全局样式文件

// 重置样式
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', <PERSON>l, sans-serif;
}

#app {
  height: 100%;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

// 通用类
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.clearfix::after {
  content: '';
  display: table;
  clear: both;
}

.pull-left {
  float: left;
}

.pull-right {
  float: right;
}

// 间距类
.mb-10 {
  margin-bottom: 10px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-10 {
  margin-top: 10px;
}

.mt-20 {
  margin-top: 20px;
}

.ml-10 {
  margin-left: 10px;
}

.mr-10 {
  margin-right: 10px;
}

// 布局相关
.full-height {
  height: 100%;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// 卡片样式
.card {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

// 状态标签样式
.status-tag {
  &.draft {
    color: #909399;
    background-color: #f4f4f5;
    border-color: #d3d4d6;
  }
  
  &.processing {
    color: #e6a23c;
    background-color: #fdf6ec;
    border-color: #f5dab1;
  }
  
  &.approved {
    color: #67c23a;
    background-color: #f0f9ff;
    border-color: #c2e7b0;
  }
  
  &.rejected {
    color: #f56c6c;
    background-color: #fef0f0;
    border-color: #fbc4c4;
  }
  
  &.completed {
    color: #409eff;
    background-color: #ecf5ff;
    border-color: #b3d8ff;
  }
}

// 优先级样式
.priority-tag {
  &.normal {
    color: #909399;
  }
  
  &.urgent {
    color: #e6a23c;
  }
  
  &.very-urgent {
    color: #f56c6c;
  }
}

// 表格样式增强
.el-table {
  .el-table__header {
    th {
      background-color: #fafafa;
      color: #606266;
      font-weight: 500;
    }
  }
  
  .el-table__row {
    &:hover {
      background-color: #f5f7fa;
    }
  }
}

// 表单样式增强
.form-container {
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
}

// 按钮组样式
.button-group {
  .el-button + .el-button {
    margin-left: 10px;
  }
}

// 页面头部样式
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e4e7ed;
  
  .page-title {
    font-size: 20px;
    font-weight: 500;
    color: #303133;
  }
}

// 搜索表单样式
.search-form {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  
  .el-form-item {
    margin-bottom: 18px;
  }
  
  .search-buttons {
    text-align: right;
    margin-top: 10px;
  }
}

// 数据表格容器
.table-container {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  
  .table-header {
    padding: 20px 20px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  .el-table {
    margin: 20px;
    width: calc(100% - 40px);
  }
  
  .pagination-container {
    padding: 20px;
    text-align: right;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .search-form {
    .el-form-item {
      margin-bottom: 15px;
    }
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    
    .page-title {
      margin-bottom: 10px;
    }
  }
  
  .table-container {
    .table-header {
      flex-direction: column;
      align-items: flex-start;
      
      .button-group {
        margin-top: 10px;
      }
    }
  }
}
