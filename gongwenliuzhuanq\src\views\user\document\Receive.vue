<template>
  <div class="document-receive">
    <el-card class="content-card">
      <template #header>
        <div class="card-header">
          <span>签收公文</span>
          <div class="header-actions">
            <el-button @click="refreshList" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-form :model="filterForm" inline>
          <el-form-item label="公文标题">
            <el-input 
              v-model="filterForm.docTitle" 
              placeholder="请输入公文标题"
              style="width: 200px"
              clearable
            />
          </el-form-item>
          <el-form-item label="公文类型">
            <el-select v-model="filterForm.docType" placeholder="请选择" clearable style="width: 150px">
              <el-option label="通知" value="NOTICE" />
              <el-option label="通报" value="BULLETIN" />
              <el-option label="报告" value="REPORT" />
              <el-option label="请示" value="REQUEST" />
              <el-option label="批复" value="REPLY" />
            </el-select>
          </el-form-item>
          <el-form-item label="紧急程度">
            <el-select v-model="filterForm.docLevel" placeholder="请选择" clearable style="width: 120px">
              <el-option label="特急" value="特急" />
              <el-option label="紧急" value="紧急" />
              <el-option label="普通" value="普通" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchDocuments">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetFilter">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 待处理公文列表 -->
      <el-table :data="pendingDocs" style="width: 100%" v-loading="loading">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="docTitle" label="公文标题" min-width="200" show-overflow-tooltip />
        <el-table-column prop="docType" label="类型" width="100" />
        <el-table-column prop="docLevel" label="紧急程度" width="100">
          <template #default="{ row }">
            <el-tag :type="getLevelType(row.docLevel)" size="small">
              {{ row.docLevel }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createUserName" label="发起人" width="120" />
        <el-table-column prop="createDeptName" label="发起部门" width="120" />
        <el-table-column prop="currentStep" label="当前环节" width="120">
          <template #default="{ row }">
            <el-tag type="warning" size="small">
              {{ getStepText(row.currentStep) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="deadline" label="办理期限" width="150">
          <template #default="{ row }">
            <span :class="{ 'text-danger': isOverdue(row.deadline) }">
              {{ row.deadline }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="150" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="viewDocument(row)">
              查看
            </el-button>
            <el-button type="text" size="small" @click="processDocument(row)">
              处理
            </el-button>
            <el-button type="text" size="small" @click="viewHistory(row)">
              流转记录
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 处理公文对话框 -->
    <el-dialog
      v-model="processDialogVisible"
      :title="`处理公文 - ${currentDoc?.docTitle}`"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="process-content">
        <!-- 公文信息 -->
        <el-descriptions :column="2" border>
          <el-descriptions-item label="公文标题">{{ currentDoc?.docTitle }}</el-descriptions-item>
          <el-descriptions-item label="公文类型">{{ currentDoc?.docType }}</el-descriptions-item>
          <el-descriptions-item label="紧急程度">
            <el-tag :type="getLevelType(currentDoc?.docLevel)" size="small">
              {{ currentDoc?.docLevel }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="发起人">{{ currentDoc?.createUserName }}</el-descriptions-item>
          <el-descriptions-item label="发起部门">{{ currentDoc?.createDeptName }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ currentDoc?.createTime }}</el-descriptions-item>
        </el-descriptions>

        <!-- 公文内容 -->
        <div class="doc-content-section">
          <h4>公文内容</h4>
          <div class="doc-content">
            {{ currentDoc?.docContent }}
          </div>
        </div>

        <!-- 处理表单 -->
        <el-form :model="processForm" :rules="processRules" ref="processFormRef" label-width="100px">
          <el-form-item label="处理意见" prop="opinion">
            <el-input
              v-model="processForm.opinion"
              type="textarea"
              :rows="4"
              placeholder="请输入处理意见..."
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="处理结果" prop="action">
            <el-radio-group v-model="processForm.action">
              <el-radio label="APPROVE">同意</el-radio>
              <el-radio label="REJECT">退回</el-radio>
              <el-radio label="TRANSFER">转发</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="processForm.action === 'TRANSFER'" label="转发给" prop="transferTo">
            <el-select v-model="processForm.transferTo" placeholder="请选择转发对象" style="width: 100%">
              <el-option label="生产副厂长" value="USER011" />
              <el-option label="技术副厂长" value="USER012" />
              <el-option label="安全副厂长" value="USER013" />
              <el-option label="厂长" value="USER010" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="processDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitProcess" :loading="processing">
            确定处理
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 流转记录对话框 -->
    <el-dialog
      v-model="historyDialogVisible"
      title="流转记录"
      width="700px"
    >
      <el-timeline>
        <el-timeline-item
          v-for="record in flowHistory"
          :key="record.recordId"
          :timestamp="record.processTime"
          :type="getTimelineType(record.action)"
        >
          <div class="timeline-content">
            <h4>{{ getActionText(record.action) }}</h4>
            <p><strong>处理人：</strong>{{ record.fromUserName || '系统' }}</p>
            <p><strong>处理环节：</strong>{{ getStepText(record.flowStep) }}</p>
            <p v-if="record.opinion"><strong>处理意见：</strong>{{ record.opinion }}</p>
          </div>
        </el-timeline-item>
      </el-timeline>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Search } from '@element-plus/icons-vue'
import {
  getPendingDocuments,
  processDocument as processDocumentAPI,
  getDocumentFlowHistory
} from '@/api/user/workspace'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const processing = ref(false)
const processDialogVisible = ref(false)
const historyDialogVisible = ref(false)
const processFormRef = ref()

const filterForm = reactive({
  docTitle: '',
  docType: '',
  docLevel: ''
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

const processForm = reactive({
  opinion: '',
  action: 'APPROVE',
  transferTo: ''
})

const processRules = {
  opinion: [
    { required: true, message: '请输入处理意见', trigger: 'blur' },
    { min: 5, message: '处理意见至少5个字符', trigger: 'blur' }
  ],
  action: [
    { required: true, message: '请选择处理结果', trigger: 'change' }
  ],
  transferTo: [
    { required: true, message: '请选择转发对象', trigger: 'change' }
  ]
}

const pendingDocs = ref([])
const currentDoc = ref(null)
const flowHistory = ref([])

// 方法
const loadPendingDocuments = async () => {
  loading.value = true
  try {
    // 调用API获取待处理公文
    const response = await getPendingDocuments({
      currentPage: pagination.currentPage,
      pageSize: pagination.pageSize,
      ...filterForm
    })

    console.log('待处理公文响应:', response)

    if (response.code === 200) {
      pendingDocs.value = response.data.records || []
      pagination.total = response.data.total || 0
    } else {
      ElMessage.error(response.message || '获取待处理公文失败')
    }
  } catch (error) {
    console.error('获取待处理公文失败:', error)
    ElMessage.error('获取待处理公文失败')
  } finally {
    loading.value = false
  }
}

const searchDocuments = () => {
  pagination.currentPage = 1
  loadPendingDocuments()
}

const resetFilter = () => {
  Object.assign(filterForm, {
    docTitle: '',
    docType: '',
    docLevel: ''
  })
  searchDocuments()
}

const refreshList = () => {
  loadPendingDocuments()
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  loadPendingDocuments()
}

const handleCurrentChange = (page) => {
  pagination.currentPage = page
  loadPendingDocuments()
}

const viewDocument = (doc) => {
  router.push(`/user/document/detail/${doc.docId}`)
}

const processDocument = (doc) => {
  currentDoc.value = doc
  processForm.opinion = ''
  processForm.action = 'APPROVE'
  processForm.transferTo = ''
  processDialogVisible.value = true
}

const submitProcess = async () => {
  if (!processFormRef.value) return
  
  await processFormRef.value.validate(async (valid) => {
    if (valid) {
      processing.value = true
      try {
        // 调用API处理公文
        await processDocumentAPI(currentDoc.value.docId, processForm)
        
        ElMessage.success('公文处理成功')
        processDialogVisible.value = false
        loadPendingDocuments()
      } catch (error) {
        ElMessage.error('处理失败')
      } finally {
        processing.value = false
      }
    }
  })
}

const viewHistory = async (doc) => {
  try {
    // 这里调用API获取流转记录
    // const response = await getDocumentFlowHistory(doc.docId)
    // flowHistory.value = response.data
    
    // 模拟数据
    flowHistory.value = [
      {
        recordId: '1',
        flowStep: 'DRAFT',
        action: 'SUBMIT',
        fromUserName: '张三',
        opinion: '创建公文',
        processTime: '2025-01-30 10:00'
      },
      {
        recordId: '2',
        flowStep: 'OFFICE_EDIT',
        action: 'EDIT',
        fromUserName: '办公室',
        opinion: '格式规范，内容完善',
        processTime: '2025-01-30 11:00'
      }
    ]
    
    historyDialogVisible.value = true
  } catch (error) {
    ElMessage.error('获取流转记录失败')
  }
}

const getLevelType = (level) => {
  const types = {
    '特急': 'danger',
    '紧急': 'warning',
    '普通': 'info'
  }
  return types[level] || 'info'
}

const getStepText = (step) => {
  const texts = {
    'DRAFT': '草稿',
    'OFFICE_EDIT': '办公室修改',
    'VICE_REVIEW': '副厂长审核',
    'DIRECTOR_APPROVE': '厂长审签',
    'OFFICE_SEND': '办公室发送',
    'DEPT_RECEIVE': '部门签收'
  }
  return texts[step] || step
}

const getActionText = (action) => {
  const texts = {
    'SUBMIT': '提交',
    'APPROVE': '同意',
    'REJECT': '退回',
    'EDIT': '修改',
    'SEND': '发送',
    'RECEIVE': '签收',
    'TRANSFER': '转发'
  }
  return texts[action] || action
}

const getTimelineType = (action) => {
  const types = {
    'SUBMIT': 'primary',
    'APPROVE': 'success',
    'REJECT': 'danger',
    'EDIT': 'warning',
    'SEND': 'info',
    'RECEIVE': 'success',
    'TRANSFER': 'warning'
  }
  return types[action] || 'primary'
}

const isOverdue = (deadline) => {
  if (!deadline) return false
  return new Date(deadline) < new Date()
}

// 生命周期
onMounted(() => {
  loadPendingDocuments()
})
</script>

<style scoped>
.document-receive {
  padding: 20px;
}

.content-card {
  background: white;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.text-danger {
  color: #f56c6c;
}

.process-content {
  max-height: 600px;
  overflow-y: auto;
}

.doc-content-section {
  margin: 20px 0;
}

.doc-content-section h4 {
  margin-bottom: 10px;
  color: #303133;
}

.doc-content {
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  white-space: pre-wrap;
  max-height: 200px;
  overflow-y: auto;
}

.timeline-content h4 {
  margin: 0 0 8px 0;
  color: #303133;
}

.timeline-content p {
  margin: 4px 0;
  color: #606266;
}
</style>
