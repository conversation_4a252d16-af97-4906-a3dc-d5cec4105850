package com.jinli.gongwen.util;

import cn.hutool.crypto.digest.BCrypt;

/**
 * 密码验证测试工具
 */
public class PasswordTestUtil {
    
    public static void main(String[] args) {
        // 数据库中的密码哈希
        String dbPasswordHash = "$2a$10$7JB720yubVSOfvVWbfXCOOlWpZXGlDYDdTOWkTRDDHQGpVw62VsMO";

        // 用户输入的明文密码
        String inputPassword = "123456";

        System.out.println("=== BCrypt密码验证测试 ===");
        System.out.println("数据库密码哈希: " + dbPasswordHash);
        System.out.println("输入的明文密码: " + inputPassword);

        // 测试密码验证
        boolean isValid = BCrypt.checkpw(inputPassword, dbPasswordHash);
        System.out.println("密码验证结果: " + (isValid ? "✓ 成功" : "✗ 失败"));

        System.out.println("\n=== 生成正确的123456密码哈希 ===");
        String correctHash = BCrypt.hashpw("123456", BCrypt.gensalt());
        System.out.println("正确的123456哈希: " + correctHash);

        boolean correctHashValid = BCrypt.checkpw("123456", correctHash);
        System.out.println("正确哈希验证结果: " + (correctHashValid ? "✓ 成功" : "✗ 失败"));

        System.out.println("\n=== 生成SQL更新语句 ===");
        System.out.println("UPDATE SYS_USER SET PASSWORD = '" + correctHash + "' WHERE USERNAME = 'admin';");
        System.out.println("-- 或者更新所有用户密码为123456:");
        System.out.println("UPDATE SYS_USER SET PASSWORD = '" + correctHash + "';");
    }
}
