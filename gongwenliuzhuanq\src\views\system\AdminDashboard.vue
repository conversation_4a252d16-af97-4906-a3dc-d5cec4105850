<template>
  <div class="admin-dashboard">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon user-icon">
              <i class="el-icon-user"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ systemStats.userCount }}</div>
              <div class="stats-label">用户总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon role-icon">
              <i class="el-icon-s-custom"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ systemStats.roleCount }}</div>
              <div class="stats-label">角色总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon dept-icon">
              <i class="el-icon-office-building"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ systemStats.deptCount }}</div>
              <div class="stats-label">部门总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon online-icon">
              <i class="el-icon-connection"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ systemStats.onlineCount }}</div>
              <div class="stats-label">在线用户</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="chart-card">
          <div slot="header" class="card-header">
            <span>用户状态分布</span>
          </div>
          <div id="userStatusChart" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="chart-card">
          <div slot="header" class="card-header">
            <span>部门用户分布</span>
          </div>
          <div id="deptUserChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快捷操作 -->
    <el-row :gutter="20" class="actions-row">
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="action-card">
          <div slot="header" class="card-header">
            <span>快捷操作</span>
          </div>
          <div class="action-buttons">
            <el-button type="primary" @click="goToUserManage">
              <i class="el-icon-user"></i> 用户管理
            </el-button>
            <el-button type="success" @click="goToRoleManage">
              <i class="el-icon-s-custom"></i> 角色管理
            </el-button>
            <el-button type="warning" @click="goToFlowManage">
              <i class="el-icon-s-operation"></i> 流程管理
            </el-button>
            <el-button type="info" @click="goToSystemConfig">
              <i class="el-icon-setting"></i> 系统配置
            </el-button>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="action-card">
          <div slot="header" class="card-header">
            <span>系统信息</span>
          </div>
          <div class="system-info">
            <div class="info-item">
              <span class="info-label">系统版本：</span>
              <span class="info-value">{{ systemInfo.version }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">数据库：</span>
              <span class="info-value">{{ systemInfo.database }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">服务器时间：</span>
              <span class="info-value">{{ systemInfo.serverTime }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">运行时间：</span>
              <span class="info-value">{{ systemInfo.uptime }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最近操作日志 -->
    <el-row class="logs-row">
      <el-col :span="24">
        <el-card class="logs-card">
          <div slot="header" class="card-header">
            <span>最近操作日志</span>
            <el-button type="text" @click="goToLogManage">查看更多</el-button>
          </div>
          <el-table :data="recentLogs" style="width: 100%">
            <el-table-column prop="username" label="操作用户" width="120" />
            <el-table-column prop="operation" label="操作类型" width="120" />
            <el-table-column prop="module" label="操作模块" width="120" />
            <el-table-column prop="description" label="操作描述" min-width="200" />
            <el-table-column prop="ip" label="IP地址" width="130" />
            <el-table-column prop="createTime" label="操作时间" width="160" />
            <el-table-column prop="status" label="状态" width="80">
              <template slot-scope="scope">
                <el-tag :type="scope.row.status === 'success' ? 'success' : 'danger'">
                  {{ scope.row.status === 'success' ? '成功' : '失败' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getSystemStats, getSystemInfo, getRecentLogs } from '@/api/system/dashboard'

export default {
  name: 'AdminDashboard',
  data() {
    return {
      systemStats: {
        userCount: 0,
        roleCount: 0,
        deptCount: 0,
        onlineCount: 0
      },
      systemInfo: {
        version: '1.0.0',
        database: '达梦8',
        serverTime: '',
        uptime: ''
      },
      recentLogs: [],
      userStatusChart: null,
      deptUserChart: null
    }
  },
  mounted() {
    this.initData()
    this.initCharts()
    // 定时更新数据
    this.timer = setInterval(() => {
      this.updateSystemInfo()
    }, 60000) // 每分钟更新一次
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
    if (this.userStatusChart) {
      this.userStatusChart.dispose()
    }
    if (this.deptUserChart) {
      this.deptUserChart.dispose()
    }
  },
  methods: {
    // 初始化数据
    async initData() {
      await this.getSystemStats()
      await this.getSystemInfo()
      await this.getRecentLogs()
    },

    // 获取系统统计
    async getSystemStats() {
      try {
        const response = await getSystemStats()
        this.systemStats = response.data
        this.updateCharts()
      } catch (error) {
        console.error('获取系统统计失败', error)
      }
    },

    // 获取系统信息
    async getSystemInfo() {
      try {
        const response = await getSystemInfo()
        this.systemInfo = response.data
      } catch (error) {
        console.error('获取系统信息失败', error)
      }
    },

    // 获取最近日志
    async getRecentLogs() {
      try {
        const response = await getRecentLogs()
        this.recentLogs = response.data
      } catch (error) {
        console.error('获取最近日志失败', error)
      }
    },

    // 更新系统信息
    updateSystemInfo() {
      this.systemInfo.serverTime = new Date().toLocaleString()
    },

    // 初始化图表
    initCharts() {
      this.initUserStatusChart()
      this.initDeptUserChart()
    },

    // 初始化用户状态图表
    initUserStatusChart() {
      const chartDom = document.getElementById('userStatusChart')
      this.userStatusChart = echarts.init(chartDom)
    },

    // 初始化部门用户图表
    initDeptUserChart() {
      const chartDom = document.getElementById('deptUserChart')
      this.deptUserChart = echarts.init(chartDom)
    },

    // 更新图表
    updateCharts() {
      this.updateUserStatusChart()
      this.updateDeptUserChart()
    },

    // 更新用户状态图表
    updateUserStatusChart() {
      if (!this.userStatusChart) return
      
      const option = {
        title: {
          text: '用户状态分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        series: [
          {
            name: '用户状态',
            type: 'pie',
            radius: '50%',
            data: [
              { value: this.systemStats.activeUsers || 10, name: '正常用户' },
              { value: this.systemStats.inactiveUsers || 2, name: '停用用户' }
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      this.userStatusChart.setOption(option)
    },

    // 更新部门用户图表
    updateDeptUserChart() {
      if (!this.deptUserChart) return
      
      const option = {
        title: {
          text: '部门用户分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        xAxis: {
          type: 'category',
          data: ['办公室', '生产部', '销售部', '财务部']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '用户数量',
            type: 'bar',
            data: [2, 3, 3, 3],
            itemStyle: {
              color: '#409EFF'
            }
          }
        ]
      }
      this.deptUserChart.setOption(option)
    },

    // 导航到用户管理
    goToUserManage() {
      this.$router.push('/system/user')
    },

    // 导航到角色管理
    goToRoleManage() {
      this.$router.push('/system/role')
    },

    // 导航到流程管理
    goToFlowManage() {
      this.$router.push('/system/flow')
    },

    // 导航到系统配置
    goToSystemConfig() {
      this.$router.push('/system/config')
    },

    // 导航到日志管理
    goToLogManage() {
      this.$router.push('/system/log')
    }
  }
}
</script>

<style scoped>
.admin-dashboard {
  padding: 20px;
}

.stats-row,
.charts-row,
.actions-row,
.logs-row {
  margin-bottom: 20px;
}

.stats-card {
  height: 120px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: white;
}

.user-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.role-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.dept-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.online-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.chart-card {
  height: 400px;
}

.chart-container {
  height: 320px;
}

.action-card {
  height: 200px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.action-buttons .el-button {
  flex: 1;
  min-width: 120px;
}

.system-info {
  padding: 10px 0;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding: 5px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-label {
  color: #909399;
  font-weight: 500;
}

.info-value {
  color: #303133;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .admin-dashboard {
    padding: 10px;
  }
  
  .stats-card {
    margin-bottom: 10px;
  }
  
  .chart-card {
    height: 350px;
    margin-bottom: 10px;
  }
  
  .chart-container {
    height: 270px;
  }
}

@media (max-width: 800px) {
  .stats-content {
    flex-direction: column;
    text-align: center;
  }
  
  .stats-icon {
    margin-right: 0;
    margin-bottom: 10px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-buttons .el-button {
    width: 100%;
  }
}
</style>
