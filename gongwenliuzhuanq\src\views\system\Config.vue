<template>
  <div class="system-config">
    <el-card class="content-card">
      <template #header>
        <div class="card-header">
          <span>系统配置管理</span>
          <el-button type="primary" @click="saveConfig">
            <el-icon><Check /></el-icon>
            保存配置
          </el-button>
        </div>
      </template>

      <el-tabs v-model="activeTab" type="border-card">
        <!-- 基础配置 -->
        <el-tab-pane label="基础配置" name="basic">
          <el-form :model="basicConfig" label-width="120px" class="config-form">
            <el-form-item label="系统名称">
              <el-input v-model="basicConfig.systemName" placeholder="请输入系统名称" />
            </el-form-item>
            
            <el-form-item label="系统版本">
              <el-input v-model="basicConfig.systemVersion" placeholder="请输入系统版本" />
            </el-form-item>
            
            <el-form-item label="公司名称">
              <el-input v-model="basicConfig.companyName" placeholder="请输入公司名称" />
            </el-form-item>
            
            <el-form-item label="系统Logo">
              <el-upload
                class="logo-uploader"
                action="#"
                :show-file-list="false"
                :before-upload="beforeLogoUpload"
              >
                <img v-if="basicConfig.logoUrl" :src="basicConfig.logoUrl" class="logo" />
                <el-icon v-else class="logo-uploader-icon"><Plus /></el-icon>
              </el-upload>
            </el-form-item>
            
            <el-form-item label="系统描述">
              <el-input
                v-model="basicConfig.systemDesc"
                type="textarea"
                :rows="3"
                placeholder="请输入系统描述"
              />
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 安全配置 -->
        <el-tab-pane label="安全配置" name="security">
          <el-form :model="securityConfig" label-width="120px" class="config-form">
            <el-form-item label="密码策略">
              <el-checkbox-group v-model="securityConfig.passwordPolicy">
                <el-checkbox label="requireUppercase">包含大写字母</el-checkbox>
                <el-checkbox label="requireLowercase">包含小写字母</el-checkbox>
                <el-checkbox label="requireNumbers">包含数字</el-checkbox>
                <el-checkbox label="requireSpecialChars">包含特殊字符</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            
            <el-form-item label="密码最小长度">
              <el-input-number v-model="securityConfig.minPasswordLength" :min="6" :max="20" />
            </el-form-item>
            
            <el-form-item label="登录失败锁定">
              <el-switch v-model="securityConfig.enableLoginLock" />
            </el-form-item>
            
            <el-form-item label="最大失败次数" v-if="securityConfig.enableLoginLock">
              <el-input-number v-model="securityConfig.maxLoginAttempts" :min="3" :max="10" />
            </el-form-item>
            
            <el-form-item label="锁定时间(分钟)" v-if="securityConfig.enableLoginLock">
              <el-input-number v-model="securityConfig.lockDuration" :min="5" :max="60" />
            </el-form-item>
            
            <el-form-item label="会话超时(分钟)">
              <el-input-number v-model="securityConfig.sessionTimeout" :min="30" :max="480" />
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 流程配置 -->
        <el-tab-pane label="流程配置" name="workflow">
          <el-form :model="workflowConfig" label-width="140px" class="config-form">
            <el-form-item label="公文编号规则">
              <el-input v-model="workflowConfig.documentNumberRule" placeholder="如：{YEAR}{MONTH}{DAY}-{SEQUENCE}" />
              <div class="form-tip">支持变量：{YEAR}年份、{MONTH}月份、{DAY}日期、{SEQUENCE}序号</div>
            </el-form-item>

            <el-form-item label="自动编号">
              <el-switch v-model="workflowConfig.autoNumbering" />
              <div class="form-tip">开启后系统将自动为新建公文分配编号</div>
            </el-form-item>

            <el-form-item label="流转超时提醒">
              <el-switch v-model="workflowConfig.enableTimeoutAlert" />
              <div class="form-tip">开启后将在公文超时时发送提醒通知</div>
            </el-form-item>

            <el-form-item label="超时时间(小时)" v-if="workflowConfig.enableTimeoutAlert">
              <el-input-number v-model="workflowConfig.timeoutHours" :min="1" :max="168" />
              <div class="form-tip">公文在当前环节停留超过此时间将触发超时提醒</div>
            </el-form-item>

            <el-form-item label="允许撤回">
              <el-switch v-model="workflowConfig.allowWithdraw" />
              <div class="form-tip">允许发起人在特定条件下撤回已提交的公文</div>
            </el-form-item>

            <el-form-item label="允许转发">
              <el-switch v-model="workflowConfig.allowForward" />
              <div class="form-tip">允许处理人将公文转发给其他人员处理</div>
            </el-form-item>

            <el-divider content-position="left">流程步骤配置</el-divider>

            <el-form-item label="默认审批流程">
              <el-select v-model="workflowConfig.defaultWorkflow" placeholder="请选择默认流程">
                <el-option label="简单审批流程" value="simple" />
                <el-option label="部门审批流程" value="department" />
                <el-option label="多级审批流程" value="multilevel" />
              </el-select>
              <div class="form-tip">新建公文时的默认流程模板</div>
            </el-form-item>

            <el-form-item label="流程步骤">
              <div class="workflow-steps">
                <div v-for="(step, index) in workflowConfig.steps" :key="index" class="step-item">
                  <el-card shadow="hover">
                    <div class="step-header">
                      <span class="step-number">{{ index + 1 }}</span>
                      <span class="step-name">{{ step.name }}</span>
                      <el-button type="danger" size="small" text @click="removeStep(index)">删除</el-button>
                    </div>
                    <div class="step-content">
                      <el-form-item label="步骤名称" label-width="80px">
                        <el-input v-model="step.name" placeholder="请输入步骤名称" />
                      </el-form-item>
                      <el-form-item label="处理角色" label-width="80px">
                        <el-select v-model="step.roleId" placeholder="请选择处理角色">
                          <el-option label="部门主管" value="dept_manager" />
                          <el-option label="分管领导" value="leader" />
                          <el-option label="总经理" value="general_manager" />
                          <el-option label="董事长" value="chairman" />
                        </el-select>
                      </el-form-item>
                      <el-form-item label="是否必须" label-width="80px">
                        <el-switch v-model="step.required" />
                      </el-form-item>
                    </div>
                  </el-card>
                </div>
                <el-button type="primary" @click="addStep" class="add-step-btn">
                  <el-icon><Plus /></el-icon>
                  添加步骤
                </el-button>
              </div>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const activeTab = ref('basic')

// 基础配置
const basicConfig = reactive({
  systemName: '金利科技公文流转系统',
  systemVersion: 'v1.0.0',
  companyName: '金利科技有限公司',
  logoUrl: '/logo.svg',
  systemDesc: '企业级公文流转管理系统，支持多级审批流程'
})

// 安全配置
const securityConfig = reactive({
  passwordPolicy: ['requireNumbers'],
  minPasswordLength: 6,
  enableLoginLock: true,
  maxLoginAttempts: 5,
  lockDuration: 30,
  sessionTimeout: 120
})

// 流程配置
const workflowConfig = reactive({
  documentNumberRule: '{YEAR}{MONTH}{DAY}-{SEQUENCE}',
  autoNumbering: true,
  enableTimeoutAlert: true,
  timeoutHours: 24,
  allowWithdraw: true,
  allowForward: true,
  defaultWorkflow: 'department',
  steps: [
    {
      name: '部门审核',
      roleId: 'dept_manager',
      required: true
    },
    {
      name: '分管领导审批',
      roleId: 'leader',
      required: true
    },
    {
      name: '总经理审批',
      roleId: 'general_manager',
      required: false
    }
  ]
})

// 方法
const loadConfig = async () => {
  try {
    // 这里应该调用API获取配置
    // const response = await getSystemConfig()
    // Object.assign(basicConfig, response.data.basic)
    // Object.assign(securityConfig, response.data.security)
    // Object.assign(workflowConfig, response.data.workflow)

    console.log('配置加载完成')
  } catch (error) {
    ElMessage.error('配置加载失败')
  }
}

const saveConfig = async () => {
  try {
    await ElMessageBox.confirm('确定要保存系统配置吗？', '确认操作', {
      type: 'warning'
    })

    const configData = {
      basic: basicConfig,
      security: securityConfig,
      workflow: workflowConfig
    }

    // 这里应该调用API保存配置
    // await saveSystemConfig(configData)

    ElMessage.success('配置保存成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('配置保存失败')
    }
  }
}

const beforeLogoUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('Logo只能是 JPG/PNG 格式!')
  }
  if (!isLt2M) {
    ElMessage.error('Logo大小不能超过 2MB!')
  }
  return isJPG && isLt2M
}

// 流程步骤管理方法
const addStep = () => {
  workflowConfig.steps.push({
    name: '新步骤',
    roleId: '',
    required: true
  })
}

const removeStep = (index) => {
  if (workflowConfig.steps.length > 1) {
    workflowConfig.steps.splice(index, 1)
  } else {
    ElMessage.warning('至少需要保留一个流程步骤')
  }
}

// 生命周期
onMounted(() => {
  loadConfig()
})
</script>

<style scoped>
.system-config {
  padding: 20px;
}

.content-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.config-form {
  max-width: 800px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.logo-uploader .logo {
  width: 100px;
  height: 100px;
  display: block;
}

.logo-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: 0.2s;
}

.logo-uploader .el-upload:hover {
  border-color: #409eff;
}

.logo-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  text-align: center;
  line-height: 100px;
}

/* 流程配置样式 */
.workflow-steps {
  width: 100%;
}

.step-item {
  margin-bottom: 16px;
}

.step-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.step-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: #409eff;
  color: white;
  border-radius: 50%;
  font-size: 12px;
  font-weight: bold;
  margin-right: 8px;
}

.step-name {
  font-weight: 500;
  flex: 1;
}

.step-content {
  padding-left: 32px;
}

.add-step-btn {
  width: 100%;
  margin-top: 16px;
  border: 2px dashed #d9d9d9;
  background: #fafafa;
  color: #666;
}

.add-step-btn:hover {
  border-color: #409eff;
  color: #409eff;
}
</style>
