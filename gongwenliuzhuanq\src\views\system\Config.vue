<template>
  <div class="system-config">
    <el-card class="content-card">
      <template #header>
        <div class="card-header">
          <span>系统配置管理</span>
          <el-button type="primary" @click="saveConfig">
            <el-icon><Check /></el-icon>
            保存配置
          </el-button>
        </div>
      </template>

      <el-tabs v-model="activeTab" type="border-card">
        <!-- 基础配置 -->
        <el-tab-pane label="基础配置" name="basic">
          <el-form :model="basicConfig" label-width="120px" class="config-form">
            <el-form-item label="系统名称">
              <el-input v-model="basicConfig.systemName" placeholder="请输入系统名称" />
            </el-form-item>
            
            <el-form-item label="系统版本">
              <el-input v-model="basicConfig.systemVersion" placeholder="请输入系统版本" />
            </el-form-item>
            
            <el-form-item label="公司名称">
              <el-input v-model="basicConfig.companyName" placeholder="请输入公司名称" />
            </el-form-item>
            
            <el-form-item label="系统Logo">
              <el-upload
                class="logo-uploader"
                action="#"
                :show-file-list="false"
                :before-upload="beforeLogoUpload"
              >
                <img v-if="basicConfig.logoUrl" :src="basicConfig.logoUrl" class="logo" />
                <el-icon v-else class="logo-uploader-icon"><Plus /></el-icon>
              </el-upload>
            </el-form-item>
            
            <el-form-item label="系统描述">
              <el-input
                v-model="basicConfig.systemDesc"
                type="textarea"
                :rows="3"
                placeholder="请输入系统描述"
              />
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 安全配置 -->
        <el-tab-pane label="安全配置" name="security">
          <el-form :model="securityConfig" label-width="120px" class="config-form">
            <el-form-item label="密码策略">
              <el-checkbox-group v-model="securityConfig.passwordPolicy">
                <el-checkbox label="requireUppercase">包含大写字母</el-checkbox>
                <el-checkbox label="requireLowercase">包含小写字母</el-checkbox>
                <el-checkbox label="requireNumbers">包含数字</el-checkbox>
                <el-checkbox label="requireSpecialChars">包含特殊字符</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            
            <el-form-item label="密码最小长度">
              <el-input-number v-model="securityConfig.minPasswordLength" :min="6" :max="20" />
            </el-form-item>
            
            <el-form-item label="登录失败锁定">
              <el-switch v-model="securityConfig.enableLoginLock" />
            </el-form-item>
            
            <el-form-item label="最大失败次数" v-if="securityConfig.enableLoginLock">
              <el-input-number v-model="securityConfig.maxLoginAttempts" :min="3" :max="10" />
            </el-form-item>
            
            <el-form-item label="锁定时间(分钟)" v-if="securityConfig.enableLoginLock">
              <el-input-number v-model="securityConfig.lockDuration" :min="5" :max="60" />
            </el-form-item>
            
            <el-form-item label="会话超时(分钟)">
              <el-input-number v-model="securityConfig.sessionTimeout" :min="30" :max="480" />
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 流程配置 -->
        <el-tab-pane label="流程配置" name="workflow">
          <el-form :model="workflowConfig" label-width="120px" class="config-form">
            <el-form-item label="公文编号规则">
              <el-input v-model="workflowConfig.documentNumberRule" placeholder="如：{YEAR}{MONTH}{DAY}-{SEQUENCE}" />
            </el-form-item>
            
            <el-form-item label="自动编号">
              <el-switch v-model="workflowConfig.autoNumbering" />
            </el-form-item>
            
            <el-form-item label="流转超时提醒">
              <el-switch v-model="workflowConfig.enableTimeoutAlert" />
            </el-form-item>
            
            <el-form-item label="超时时间(小时)" v-if="workflowConfig.enableTimeoutAlert">
              <el-input-number v-model="workflowConfig.timeoutHours" :min="1" :max="168" />
            </el-form-item>
            
            <el-form-item label="允许撤回">
              <el-switch v-model="workflowConfig.allowWithdraw" />
            </el-form-item>
            
            <el-form-item label="允许转发">
              <el-switch v-model="workflowConfig.allowForward" />
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 邮件配置 -->
        <el-tab-pane label="邮件配置" name="email">
          <el-form :model="emailConfig" label-width="120px" class="config-form">
            <el-form-item label="SMTP服务器">
              <el-input v-model="emailConfig.smtpHost" placeholder="请输入SMTP服务器地址" />
            </el-form-item>
            
            <el-form-item label="SMTP端口">
              <el-input-number v-model="emailConfig.smtpPort" :min="1" :max="65535" />
            </el-form-item>
            
            <el-form-item label="发送邮箱">
              <el-input v-model="emailConfig.senderEmail" placeholder="请输入发送邮箱" />
            </el-form-item>
            
            <el-form-item label="邮箱密码">
              <el-input v-model="emailConfig.senderPassword" type="password" placeholder="请输入邮箱密码" />
            </el-form-item>
            
            <el-form-item label="启用SSL">
              <el-switch v-model="emailConfig.enableSSL" />
            </el-form-item>
            
            <el-form-item label="测试邮件">
              <el-input v-model="testEmail" placeholder="请输入测试邮箱">
                <template #append>
                  <el-button @click="sendTestEmail">发送测试</el-button>
                </template>
              </el-input>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const activeTab = ref('basic')
const testEmail = ref('')

// 基础配置
const basicConfig = reactive({
  systemName: '金利科技公文流转系统',
  systemVersion: 'v1.0.0',
  companyName: '金利科技有限公司',
  logoUrl: '/logo.svg',
  systemDesc: '企业级公文流转管理系统，支持多级审批流程'
})

// 安全配置
const securityConfig = reactive({
  passwordPolicy: ['requireNumbers'],
  minPasswordLength: 6,
  enableLoginLock: true,
  maxLoginAttempts: 5,
  lockDuration: 30,
  sessionTimeout: 120
})

// 流程配置
const workflowConfig = reactive({
  documentNumberRule: '{YEAR}{MONTH}{DAY}-{SEQUENCE}',
  autoNumbering: true,
  enableTimeoutAlert: true,
  timeoutHours: 24,
  allowWithdraw: true,
  allowForward: true
})

// 邮件配置
const emailConfig = reactive({
  smtpHost: 'smtp.qq.com',
  smtpPort: 587,
  senderEmail: '',
  senderPassword: '',
  enableSSL: true
})

// 方法
const loadConfig = async () => {
  try {
    // 这里应该调用API获取配置
    // const response = await getSystemConfig()
    // Object.assign(basicConfig, response.data.basic)
    // Object.assign(securityConfig, response.data.security)
    // Object.assign(workflowConfig, response.data.workflow)
    // Object.assign(emailConfig, response.data.email)
    
    console.log('配置加载完成')
  } catch (error) {
    ElMessage.error('配置加载失败')
  }
}

const saveConfig = async () => {
  try {
    await ElMessageBox.confirm('确定要保存系统配置吗？', '确认操作', {
      type: 'warning'
    })
    
    const configData = {
      basic: basicConfig,
      security: securityConfig,
      workflow: workflowConfig,
      email: emailConfig
    }
    
    // 这里应该调用API保存配置
    // await saveSystemConfig(configData)
    
    ElMessage.success('配置保存成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('配置保存失败')
    }
  }
}

const beforeLogoUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('Logo只能是 JPG/PNG 格式!')
  }
  if (!isLt2M) {
    ElMessage.error('Logo大小不能超过 2MB!')
  }
  return isJPG && isLt2M
}

const sendTestEmail = async () => {
  if (!testEmail.value) {
    ElMessage.warning('请输入测试邮箱')
    return
  }
  
  try {
    // 这里应该调用API发送测试邮件
    // await sendTestEmail({ email: testEmail.value, config: emailConfig })
    
    ElMessage.success('测试邮件发送成功')
  } catch (error) {
    ElMessage.error('测试邮件发送失败')
  }
}

// 生命周期
onMounted(() => {
  loadConfig()
})
</script>

<style scoped>
.system-config {
  padding: 20px;
}

.content-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.config-form {
  max-width: 600px;
}

.logo-uploader .logo {
  width: 100px;
  height: 100px;
  display: block;
}

.logo-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: 0.2s;
}

.logo-uploader .el-upload:hover {
  border-color: #409eff;
}

.logo-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  text-align: center;
  line-height: 100px;
}
</style>
