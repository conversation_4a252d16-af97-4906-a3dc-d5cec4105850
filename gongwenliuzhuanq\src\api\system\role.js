import request from '@/utils/request'

// 获取角色列表
export function getRoleList(params) {
  return request({
    url: '/system/role/list',
    method: 'get',
    params
  })
}

// 获取所有角色（不分页）
export function getAllRoles() {
  return request({
    url: '/system/role/all',
    method: 'get'
  })
}

// 获取角色详情
export function getRoleDetail(roleId) {
  return request({
    url: `/system/role/${roleId}`,
    method: 'get'
  })
}

// 根据ID获取角色（别名）
export function getRoleById(roleId) {
  return getRoleDetail(roleId)
}

// 新增角色
export function createRole(data) {
  return request({
    url: '/system/role',
    method: 'post',
    data
  })
}

// 修改角色
export function updateRole(roleId, data) {
  return request({
    url: `/system/role/${roleId}`,
    method: 'put',
    data
  })
}

// 删除角色
export function deleteRole(roleId) {
  return request({
    url: `/system/role/${roleId}`,
    method: 'delete'
  })
}

// 启用/停用角色
export function changeRoleStatus(roleId, status) {
  return request({
    url: `/system/role/${roleId}/status`,
    method: 'put',
    params: { status }
  })
}

// 获取角色权限菜单
export function getRoleMenus(roleId) {
  return request({
    url: `/system/role/${roleId}/menus`,
    method: 'get'
  })
}

// 分配角色权限
export function assignRoleMenus(roleId, menuIds) {
  return request({
    url: `/system/role/${roleId}/menus`,
    method: 'put',
    data: menuIds
  })
}
