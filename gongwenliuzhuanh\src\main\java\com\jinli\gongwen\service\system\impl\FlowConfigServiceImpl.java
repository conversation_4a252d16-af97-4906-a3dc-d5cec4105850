package com.jinli.gongwen.service.system.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinli.gongwen.dto.system.FlowConfigDTO;
import com.jinli.gongwen.dto.system.FlowQueryDTO;
import com.jinli.gongwen.entity.system.FlowConfig;
import com.jinli.gongwen.service.system.FlowConfigService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 流程配置服务实现类
 */
@Service
public class FlowConfigServiceImpl implements FlowConfigService {

    @Override
    public IPage<FlowConfig> getFlowConfigList(Page<FlowConfig> page, FlowQueryDTO queryDTO) {
        // TODO: 实现具体逻辑
        return new Page<>();
    }

    @Override
    public FlowConfig getFlowConfigById(String configId) {
        // TODO: 实现具体逻辑
        return new FlowConfig();
    }

    @Override
    public String createFlowConfig(FlowConfigDTO configDTO) {
        // TODO: 实现具体逻辑
        return "CONFIG_ID";
    }

    @Override
    public void updateFlowConfig(String configId, FlowConfigDTO configDTO) {
        // TODO: 实现具体逻辑
    }

    @Override
    public void deleteFlowConfig(String configId) {
        // TODO: 实现具体逻辑
    }

    @Override
    public List<FlowConfig> getAllEnabledFlowConfigs() {
        // TODO: 实现具体逻辑
        return new ArrayList<>();
    }
}
