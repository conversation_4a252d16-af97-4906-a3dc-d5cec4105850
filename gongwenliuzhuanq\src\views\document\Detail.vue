<template>
  <div class="document-detail">
    <div class="page-header">
      <h1 class="page-title">公文详情</h1>
      <div class="page-actions">
        <el-button @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <el-button type="primary" @click="handlePrint">
          <el-icon><Printer /></el-icon>
          打印
        </el-button>
      </div>
    </div>

    <el-card class="detail-card" shadow="never" v-loading="loading">
      <!-- 公文基本信息 -->
      <div class="document-info">
        <h2 class="doc-title">{{ documentInfo.docTitle }}</h2>
        
        <div class="doc-meta">
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="meta-item">
                <span class="meta-label">公文编号：</span>
                <span class="meta-value">{{ documentInfo.docId }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="meta-item">
                <span class="meta-label">公文类型：</span>
                <el-tag :type="getDocTypeColor(documentInfo.docType)" size="small">
                  {{ getDocTypeName(documentInfo.docType) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="meta-item">
                <span class="meta-label">紧急程度：</span>
                <el-tag :type="documentInfo.docLevel === '紧急' ? 'danger' : 'info'" size="small">
                  {{ documentInfo.docLevel }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="meta-item">
                <span class="meta-label">当前状态：</span>
                <el-tag :type="getStatusColor(documentInfo.docStatus)" size="small">
                  {{ getStatusName(documentInfo.docStatus) }}
                </el-tag>
              </div>
            </el-col>
          </el-row>
          
          <el-row :gutter="20" style="margin-top: 16px;">
            <el-col :span="6">
              <div class="meta-item">
                <span class="meta-label">发起人：</span>
                <span class="meta-value">{{ documentInfo.createUserName }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="meta-item">
                <span class="meta-label">创建时间：</span>
                <span class="meta-value">{{ formatTime(documentInfo.createTime) }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="meta-item">
                <span class="meta-label">更新时间：</span>
                <span class="meta-value">{{ formatTime(documentInfo.updateTime) }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="meta-item">
                <span class="meta-label">截止时间：</span>
                <span class="meta-value">{{ formatTime(documentInfo.deadline) }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 公文内容 -->
      <el-divider />
      <div class="document-content">
        <h3>公文内容</h3>
        <div class="content-body" v-html="documentInfo.content"></div>
      </div>

      <!-- 附件列表 -->
      <el-divider />
      <div class="document-attachments" v-if="documentInfo.attachments && documentInfo.attachments.length > 0">
        <h3>附件列表</h3>
        <el-table :data="documentInfo.attachments" style="width: 100%">
          <el-table-column prop="fileName" label="文件名" />
          <el-table-column prop="fileSize" label="文件大小" width="120" />
          <el-table-column prop="uploadTime" label="上传时间" width="160">
            <template #default="{ row }">
              {{ formatTime(row.uploadTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="downloadFile(row)">
                下载
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 流转记录 -->
      <el-divider />
      <div class="flow-history">
        <h3>流转记录</h3>
        <el-timeline>
          <el-timeline-item
            v-for="(item, index) in flowHistory"
            :key="index"
            :timestamp="formatTime(item.operateTime)"
            :type="getTimelineType(item.operateType)"
          >
            <div class="timeline-content">
              <div class="timeline-title">{{ item.operateType }}</div>
              <div class="timeline-user">操作人：{{ item.operateUser }}</div>
              <div class="timeline-comment" v-if="item.comment">
                意见：{{ item.comment }}
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getDocumentById } from '@/api/document'
import dayjs from 'dayjs'
import { ArrowLeft, Printer } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

const loading = ref(false)
const documentInfo = ref({})
const flowHistory = ref([])

const formatTime = (timestamp) => {
  return timestamp ? dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss') : '-'
}

const getDocTypeName = (type) => {
  const typeMap = {
    'NOTICE': '通知',
    'REQUEST': '请示',
    'REPORT': '报告',
    'OPINION': '意见'
  }
  return typeMap[type] || type
}

const getDocTypeColor = (type) => {
  const colorMap = {
    'NOTICE': 'primary',
    'REQUEST': 'warning',
    'REPORT': 'success',
    'OPINION': 'info'
  }
  return colorMap[type] || 'info'
}

const getStatusName = (status) => {
  const statusMap = {
    'DRAFT': '草稿',
    'PROCESSING': '处理中',
    'COMPLETED': '已完成',
    'ARCHIVED': '已归档'
  }
  return statusMap[status] || status
}

const getStatusColor = (status) => {
  const colorMap = {
    'DRAFT': 'info',
    'PROCESSING': 'warning',
    'COMPLETED': 'success',
    'ARCHIVED': 'primary'
  }
  return colorMap[status] || 'info'
}

const getTimelineType = (operateType) => {
  const typeMap = {
    '创建': 'primary',
    '提交': 'success',
    '审核': 'warning',
    '退回': 'danger',
    '完成': 'success'
  }
  return typeMap[operateType] || 'primary'
}

const loadDocumentDetail = async () => {
  loading.value = true
  try {
    const docId = route.params.id
    console.log('Loading document detail for ID:', docId)

    // 调用后端API获取公文详情
    const response = await getDocumentById(docId)

    if (response.code === 200) {
      documentInfo.value = response.data

      // 模拟流转记录（根据不同公文生成不同的流转记录）
      if (docId === 'DOC001') {
        flowHistory.value = [
          {
            operateType: '创建',
            operateUser: '李主任',
            operateTime: documentInfo.value.createTime,
            comment: '创建公文'
          },
          {
            operateType: '提交',
            operateUser: '李主任',
            operateTime: documentInfo.value.createTime + 30 * 60 * 1000,
            comment: '提交审核'
          },
          {
            operateType: '审核',
            operateUser: '张部长',
            operateTime: documentInfo.value.updateTime,
            comment: '内容详实，同意发布'
          }
        ]
      } else if (docId === 'DOC002') {
        flowHistory.value = [
          {
            operateType: '创建',
            operateUser: '王部长',
            operateTime: documentInfo.value.createTime,
            comment: '创建采购申请'
          },
          {
            operateType: '提交',
            operateUser: '王部长',
            operateTime: documentInfo.value.createTime + 60 * 60 * 1000,
            comment: '提交财务审核'
          },
          {
            operateType: '审核中',
            operateUser: '财务部',
            operateTime: documentInfo.value.updateTime,
            comment: '正在审核预算'
          }
        ]
      } else if (docId === 'DOC003') {
        flowHistory.value = [
          {
            operateType: '创建',
            operateUser: '李主任',
            operateTime: documentInfo.value.createTime,
            comment: '创建会议通知'
          },
          {
            operateType: '审核',
            operateUser: '张部长',
            operateTime: documentInfo.value.createTime + 2 * 60 * 60 * 1000,
            comment: '同意召开会议'
          },
          {
            operateType: '发布',
            operateUser: '办公室',
            operateTime: documentInfo.value.updateTime,
            comment: '已发布通知'
          }
        ]
      } else if (docId === 'REC001') {
        flowHistory.value = [
          {
            operateType: '创建',
            operateUser: '张部长',
            operateTime: documentInfo.value.createTime,
            comment: '创建生产计划报告'
          },
          {
            operateType: '审核',
            operateUser: '李主任',
            operateTime: documentInfo.value.createTime + 4 * 60 * 60 * 1000,
            comment: '数据详实，同意上报'
          },
          {
            operateType: '完成',
            operateUser: '总经理',
            operateTime: documentInfo.value.updateTime,
            comment: '报告已阅，继续保持'
          }
        ]
      } else if (docId === 'REC002') {
        flowHistory.value = [
          {
            operateType: '创建',
            operateUser: '李主任',
            operateTime: documentInfo.value.createTime,
            comment: '创建财务意见'
          },
          {
            operateType: '提交',
            operateUser: '李主任',
            operateTime: documentInfo.value.createTime + 2 * 60 * 60 * 1000,
            comment: '提交财务部处理'
          },
          {
            operateType: '处理中',
            operateUser: '财务部',
            operateTime: documentInfo.value.updateTime,
            comment: '正在制定改进方案'
          }
        ]
      } else if (docId === 'REC003') {
        flowHistory.value = [
          {
            operateType: '创建',
            operateUser: '张部长',
            operateTime: documentInfo.value.createTime,
            comment: '创建技术改造通知'
          },
          {
            operateType: '审核',
            operateUser: '总工程师',
            operateTime: documentInfo.value.createTime + 3 * 60 * 60 * 1000,
            comment: '技术方案可行，同意实施'
          },
          {
            operateType: '批准',
            operateUser: '总经理',
            operateTime: documentInfo.value.updateTime,
            comment: '批准项目启动'
          }
        ]
      } else {
        // 默认流转记录
        flowHistory.value = [
          {
            operateType: '创建',
            operateUser: documentInfo.value.createUserName,
            operateTime: documentInfo.value.createTime,
            comment: '创建公文'
          }
        ]
      }

      // 模拟附件（根据公文类型添加不同附件）
      if (docId === 'DOC001') {
        documentInfo.value.attachments = [
          {
            fileName: '安全生产管理制度.pdf',
            fileSize: '2.5MB',
            uploadTime: documentInfo.value.createTime
          }
        ]
      } else if (docId === 'DOC002') {
        documentInfo.value.attachments = [
          {
            fileName: '设备采购清单.xlsx',
            fileSize: '1.2MB',
            uploadTime: documentInfo.value.createTime
          },
          {
            fileName: '设备技术参数.pdf',
            fileSize: '3.8MB',
            uploadTime: documentInfo.value.createTime
          }
        ]
      } else if (docId === 'DOC003') {
        documentInfo.value.attachments = [
          {
            fileName: '会议议程.docx',
            fileSize: '0.8MB',
            uploadTime: documentInfo.value.createTime
          }
        ]
      } else if (docId === 'REC001') {
        documentInfo.value.attachments = [
          {
            fileName: '生产计划执行报表.xlsx',
            fileSize: '2.1MB',
            uploadTime: documentInfo.value.createTime
          },
          {
            fileName: '质量分析图表.pdf',
            fileSize: '1.5MB',
            uploadTime: documentInfo.value.createTime
          }
        ]
      } else if (docId === 'REC002') {
        documentInfo.value.attachments = [
          {
            fileName: '预算执行明细表.xlsx',
            fileSize: '3.2MB',
            uploadTime: documentInfo.value.createTime
          }
        ]
      } else if (docId === 'REC003') {
        documentInfo.value.attachments = [
          {
            fileName: '技术改造方案.pdf',
            fileSize: '4.5MB',
            uploadTime: documentInfo.value.createTime
          },
          {
            fileName: '设备清单.xlsx',
            fileSize: '1.8MB',
            uploadTime: documentInfo.value.createTime
          },
          {
            fileName: '项目进度计划.mpp',
            fileSize: '2.3MB',
            uploadTime: documentInfo.value.createTime
          }
        ]
      } else {
        documentInfo.value.attachments = []
      }
    } else {
      ElMessage.error(response.message || '获取公文详情失败')
    }

  } catch (error) {
    console.error('加载公文详情失败:', error)
    ElMessage.error('加载公文详情失败')
  } finally {
    loading.value = false
  }
}

const goBack = () => {
  router.go(-1)
}

const handlePrint = () => {
  window.print()
}

const downloadFile = (file) => {
  ElMessage.success(`下载文件: ${file.fileName}`)
}

onMounted(() => {
  loadDocumentDetail()
})
</script>

<style lang="scss" scoped>
.document-detail {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .page-title {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .detail-card {
    .document-info {
      .doc-title {
        text-align: center;
        font-size: 28px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 24px;
      }
      
      .doc-meta {
        .meta-item {
          display: flex;
          align-items: center;
          margin-bottom: 8px;
          
          .meta-label {
            font-weight: 500;
            color: #606266;
            margin-right: 8px;
          }
          
          .meta-value {
            color: #303133;
          }
        }
      }
    }
    
    .document-content {
      h3 {
        margin-bottom: 16px;
        color: #303133;
      }
      
      .content-body {
        line-height: 1.8;
        color: #606266;
        background: #fafafa;
        padding: 20px;
        border-radius: 4px;
      }
    }
    
    .document-attachments {
      h3 {
        margin-bottom: 16px;
        color: #303133;
      }
    }
    
    .flow-history {
      h3 {
        margin-bottom: 16px;
        color: #303133;
      }
      
      .timeline-content {
        .timeline-title {
          font-weight: 600;
          color: #303133;
          margin-bottom: 4px;
        }
        
        .timeline-user {
          color: #606266;
          font-size: 14px;
          margin-bottom: 4px;
        }
        
        .timeline-comment {
          color: #909399;
          font-size: 14px;
        }
      }
    }
  }
}
</style>
