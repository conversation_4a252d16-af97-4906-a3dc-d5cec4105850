package com.jinli.gongwen.service.document;

import com.jinli.gongwen.dto.document.DocumentFlowDTO;
import com.jinli.gongwen.vo.document.DocumentVO;
import com.jinli.gongwen.vo.document.FlowRecordVO;

import java.util.List;

/**
 * 流转服务接口
 */
public interface FlowService {

    /**
     * 处理流转
     */
    void processFlow(DocumentFlowDTO flowDTO);

    /**
     * 获取流转记录
     */
    List<FlowRecordVO> getFlowRecords(String docId);

    /**
     * 获取流转状态概览
     */
    List<DocumentVO> getFlowStatusOverview();

    /**
     * 退回公文
     */
    void returnDocument(String docId, String reason);
}
