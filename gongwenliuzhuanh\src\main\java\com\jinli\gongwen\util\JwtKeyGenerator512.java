package com.jinli.gongwen.util;

import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import java.util.Base64;
import javax.crypto.SecretKey;

/**
 * JWT 512-bit Key Generator for HS512 Algorithm
 */
public class JwtKeyGenerator512 {
    
    public static void main(String[] args) {
        generateHS512Key();
    }
    
    public static void generateHS512Key() {
        System.out.println("=== JWT HS512 Key Generator ===");
        
        // Generate proper 512-bit key for HS512 algorithm
        SecretKey secretKey = Keys.secretKeyFor(SignatureAlgorithm.HS512);
        String base64Key = Base64.getEncoder().encodeToString(secretKey.getEncoded());
        
        System.out.println("Generated 512-bit key for HS512:");
        System.out.println("Key: " + base64Key);
        System.out.println("Key length: " + base64Key.length() + " characters");
        System.out.println("Key bits: " + (secretKey.getEncoded().length * 8) + " bits");
        
        System.out.println("\n=== Update application.properties ===");
        System.out.println("Replace the jwt.secret with:");
        System.out.println("jwt.secret=" + base64Key);
        
        System.out.println("\n=== Verification ===");
        System.out.println("✓ Key meets HS512 requirements (512+ bits)");
        System.out.println("✓ Generated using io.jsonwebtoken.security.Keys.secretKeyFor()");
        System.out.println("✓ RFC 7518 compliant");
    }
}
