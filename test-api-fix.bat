@echo off
echo === 测试API修复 ===

echo 等待5秒让应用稳定...
timeout /t 5 /nobreak >nul

echo 1. 测试统计接口...
curl -X GET "http://localhost:8080/api/document/statistics?userId=USER001" -H "Accept: application/json"

echo.
echo.
echo 2. 测试待处理接口...
curl -X GET "http://localhost:8080/api/document/pending?pageNum=1&pageSize=5&userId=USER001" -H "Accept: application/json"

echo.
echo.
echo 3. 测试分页接口...
curl -X GET "http://localhost:8080/api/document/page?pageNum=1&pageSize=5&createUserId=USER001" -H "Accept: application/json"

echo.
echo.
echo === 测试完成 ===
echo 如果看到JSON响应且没有错误，说明修复成功！
pause
