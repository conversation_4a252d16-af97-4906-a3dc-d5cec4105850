package com.jinli.gongwen.vo.vice;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 副厂长仪表板视图对象
 */
@Data
@ApiModel("副厂长仪表板视图对象")
public class ViceDashboardVO {

    @ApiModelProperty("待审核公文数量")
    private Integer pendingReviewCount;

    @ApiModelProperty("今日审核数量")
    private Integer todayReviewedCount;

    @ApiModelProperty("本月审核数量")
    private Integer monthReviewedCount;

    @ApiModelProperty("分管部门数量")
    private Integer managedDeptCount;

    @ApiModelProperty("审核通过率")
    private Double approvalRate;

    @ApiModelProperty("平均审核时间（小时）")
    private Double avgReviewTime;

    @ApiModelProperty("紧急公文数量")
    private Integer urgentDocumentCount;

    @ApiModelProperty("分管部门公文统计")
    private Map<String, Integer> deptDocumentStats;

    @ApiModelProperty("公文类型审核统计")
    private Map<String, Integer> docTypeReviewStats;

    @ApiModelProperty("审核结果统计")
    private Map<String, Integer> reviewResultStats;

    @ApiModelProperty("最近审核的公文列表")
    private List<RecentReviewVO> recentReviews;

    @ApiModelProperty("待审核公文列表")
    private List<PendingReviewVO> pendingReviews;

    @ApiModelProperty("本周审核趋势")
    private List<DailyReviewVO> weeklyReviewTrend;

    @ApiModelProperty("分管部门效率排名")
    private List<DeptEfficiencyVO> deptEfficiencyRanking;

    /**
     * 最近审核视图对象
     */
    @Data
    @ApiModel("最近审核视图对象")
    public static class RecentReviewVO {
        @ApiModelProperty("公文ID")
        private String docId;

        @ApiModelProperty("公文标题")
        private String docTitle;

        @ApiModelProperty("公文类型")
        private String docType;

        @ApiModelProperty("创建部门")
        private String createDeptName;

        @ApiModelProperty("审核结果")
        private Boolean isApproved;

        @ApiModelProperty("审核时间")
        private String reviewTime;

        @ApiModelProperty("审核意见")
        private String reviewOpinion;
    }

    /**
     * 待审核公文视图对象
     */
    @Data
    @ApiModel("待审核公文视图对象")
    public static class PendingReviewVO {
        @ApiModelProperty("公文ID")
        private String docId;

        @ApiModelProperty("公文标题")
        private String docTitle;

        @ApiModelProperty("公文类型")
        private String docType;

        @ApiModelProperty("紧急程度")
        private String docLevel;

        @ApiModelProperty("创建部门")
        private String createDeptName;

        @ApiModelProperty("创建时间")
        private String createTime;

        @ApiModelProperty("等待时间（小时）")
        private Integer waitingHours;
    }

    /**
     * 每日审核统计视图对象
     */
    @Data
    @ApiModel("每日审核统计视图对象")
    public static class DailyReviewVO {
        @ApiModelProperty("日期")
        private String date;

        @ApiModelProperty("审核数量")
        private Integer reviewCount;

        @ApiModelProperty("通过数量")
        private Integer approvedCount;

        @ApiModelProperty("退回数量")
        private Integer rejectedCount;
    }

    /**
     * 部门效率视图对象
     */
    @Data
    @ApiModel("部门效率视图对象")
    public static class DeptEfficiencyVO {
        @ApiModelProperty("部门ID")
        private String deptId;

        @ApiModelProperty("部门名称")
        private String deptName;

        @ApiModelProperty("公文数量")
        private Integer documentCount;

        @ApiModelProperty("平均处理时间")
        private Double avgProcessTime;

        @ApiModelProperty("及时完成率")
        private Double timelyRate;

        @ApiModelProperty("排名")
        private Integer ranking;
    }
}
