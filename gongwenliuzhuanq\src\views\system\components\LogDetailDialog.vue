<template>
  <el-dialog
    title="日志详情"
    v-model="dialogVisible"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-loading="loading" class="log-detail">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="日志ID" :span="2">
          <el-tag type="info">{{ logDetail.logId }}</el-tag>
        </el-descriptions-item>
        
        <el-descriptions-item label="操作用户">
          {{ logDetail.username }}
        </el-descriptions-item>
        
        <el-descriptions-item label="用户姓名">
          {{ logDetail.realName }}
        </el-descriptions-item>
        
        <el-descriptions-item label="操作模块">
          <el-tag :type="getModuleColor(logDetail.module)" size="small">
            {{ getModuleName(logDetail.module) }}
          </el-tag>
        </el-descriptions-item>
        
        <el-descriptions-item label="操作类型">
          <el-tag :type="getOperationTypeColor(logDetail.operationType)" size="small">
            {{ getOperationTypeName(logDetail.operationType) }}
          </el-tag>
        </el-descriptions-item>
        
        <el-descriptions-item label="操作状态">
          <el-tag :type="logDetail.status === 'SUCCESS' ? 'success' : 'danger'" size="small">
            {{ logDetail.status === 'SUCCESS' ? '成功' : '失败' }}
          </el-tag>
        </el-descriptions-item>
        
        <el-descriptions-item label="IP地址">
          {{ logDetail.ip }}
        </el-descriptions-item>
        
        <el-descriptions-item label="用户代理" :span="2">
          <div class="user-agent">{{ logDetail.userAgent || '-' }}</div>
        </el-descriptions-item>
        
        <el-descriptions-item label="操作描述" :span="2">
          <div class="description">{{ logDetail.description }}</div>
        </el-descriptions-item>
        
        <el-descriptions-item label="请求URL" :span="2">
          <el-tag type="info" class="url-tag">{{ logDetail.requestUrl || '-' }}</el-tag>
        </el-descriptions-item>
        
        <el-descriptions-item label="请求方法">
          <el-tag :type="getMethodColor(logDetail.requestMethod)" size="small">
            {{ logDetail.requestMethod || '-' }}
          </el-tag>
        </el-descriptions-item>
        
        <el-descriptions-item label="响应时间">
          {{ logDetail.responseTime ? logDetail.responseTime + 'ms' : '-' }}
        </el-descriptions-item>
        
        <el-descriptions-item label="操作时间" :span="2">
          {{ formatTime(logDetail.createTime) }}
        </el-descriptions-item>
      </el-descriptions>
      
      <!-- 请求参数 -->
      <div v-if="logDetail.requestParams" class="detail-section">
        <h4>请求参数</h4>
        <el-input
          v-model="logDetail.requestParams"
          type="textarea"
          :rows="6"
          readonly
          class="json-textarea"
        />
      </div>
      
      <!-- 响应结果 -->
      <div v-if="logDetail.responseResult" class="detail-section">
        <h4>响应结果</h4>
        <el-input
          v-model="logDetail.responseResult"
          type="textarea"
          :rows="6"
          readonly
          class="json-textarea"
        />
      </div>
      
      <!-- 异常信息 -->
      <div v-if="logDetail.errorMsg" class="detail-section">
        <h4>异常信息</h4>
        <el-alert
          :title="logDetail.errorMsg"
          type="error"
          :closable="false"
          show-icon
        />
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="danger" @click="handleDelete" :loading="deleteLoading">
          删除此日志
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getLogDetail, deleteLog } from '@/api/system/log'
import dayjs from 'dayjs'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  logId: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:visible', 'deleted'])

// 响应式数据
const loading = ref(false)
const deleteLoading = ref(false)
const dialogVisible = ref(false)
const logDetail = ref({})

// 监听visible变化
watch(() => props.visible, (val) => {
  dialogVisible.value = val
  if (val && props.logId) {
    loadLogDetail()
  }
})

watch(dialogVisible, (val) => {
  emit('update:visible', val)
})

// 加载日志详情
const loadLogDetail = async () => {
  loading.value = true
  try {
    const response = await getLogDetail(props.logId)
    if (response.code === 200) {
      logDetail.value = response.data || {}
      // 格式化JSON字符串
      if (logDetail.value.requestParams) {
        try {
          logDetail.value.requestParams = JSON.stringify(
            JSON.parse(logDetail.value.requestParams), 
            null, 
            2
          )
        } catch (e) {
          // 如果不是JSON格式，保持原样
        }
      }
      if (logDetail.value.responseResult) {
        try {
          logDetail.value.responseResult = JSON.stringify(
            JSON.parse(logDetail.value.responseResult), 
            null, 
            2
          )
        } catch (e) {
          // 如果不是JSON格式，保持原样
        }
      }
    } else {
      ElMessage.error(response.message || '获取日志详情失败')
    }
  } catch (error) {
    console.error('加载日志详情失败:', error)
    ElMessage.error('加载日志详情失败')
  } finally {
    loading.value = false
  }
}

// 删除日志
const handleDelete = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条日志吗？删除后不可恢复！',
      '删除日志',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    deleteLoading.value = true
    const response = await deleteLog(props.logId)
    if (response.code === 200) {
      ElMessage.success('删除成功')
      emit('deleted')
      handleClose()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  } finally {
    deleteLoading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  logDetail.value = {}
}

// 工具方法
const formatTime = (timestamp) => {
  return timestamp ? dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss') : '-'
}

const getModuleName = (module) => {
  const moduleMap = {
    USER: '用户管理',
    ROLE: '角色管理',
    DEPT: '部门管理',
    DOCUMENT: '公文管理',
    SYSTEM: '系统设置'
  }
  return moduleMap[module] || module
}

const getModuleColor = (module) => {
  const colorMap = {
    USER: 'primary',
    ROLE: 'success',
    DEPT: 'warning',
    DOCUMENT: 'info',
    SYSTEM: 'danger'
  }
  return colorMap[module] || 'primary'
}

const getOperationTypeName = (type) => {
  const typeMap = {
    LOGIN: '登录',
    LOGOUT: '登出',
    CREATE: '新增',
    UPDATE: '修改',
    DELETE: '删除',
    SELECT: '查询'
  }
  return typeMap[type] || type
}

const getOperationTypeColor = (type) => {
  const colorMap = {
    LOGIN: 'success',
    LOGOUT: 'info',
    CREATE: 'primary',
    UPDATE: 'warning',
    DELETE: 'danger',
    SELECT: 'info'
  }
  return colorMap[type] || 'info'
}

const getMethodColor = (method) => {
  const colorMap = {
    GET: 'success',
    POST: 'primary',
    PUT: 'warning',
    DELETE: 'danger'
  }
  return colorMap[method] || 'info'
}
</script>

<style scoped>
.log-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-section {
  margin-top: 20px;
}

.detail-section h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.json-textarea {
  font-family: 'Courier New', monospace;
}

.user-agent {
  word-break: break-all;
  font-size: 12px;
  color: #606266;
}

.description {
  color: #303133;
}

.url-tag {
  word-break: break-all;
}

.dialog-footer {
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto !important;
  }
  
  .log-detail {
    max-height: 60vh;
  }
}
</style>
