package com.jinli.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinli.entity.SysLog;
import com.jinli.mapper.SysLogMapper;
import com.jinli.service.ISysLogService;
// import org.apache.poi.ss.usermodel.*;
// import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

// import java.io.ByteArrayOutputStream;
// import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 系统日志服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
@Service
public class SysLogServiceImpl extends ServiceImpl<SysLogMapper, SysLog> implements ISysLogService {

    private static final Logger log = LoggerFactory.getLogger(SysLogServiceImpl.class);

    @Override
    public Map<String, Object> selectLogPage(Integer pageNum, Integer pageSize, String username, 
                                           String module, String operationType, String status, 
                                           LocalDateTime startTime, LocalDateTime endTime) {
        log.info("分页查询系统日志，页码：{}，每页大小：{}，用户名：{}，模块：{}，操作类型：{}，状态：{}，开始时间：{}，结束时间：{}", 
                pageNum, pageSize, username, module, operationType, status, startTime, endTime);

        Page<SysLog> page = new Page<>(pageNum, pageSize);
        QueryWrapper<SysLog> queryWrapper = new QueryWrapper<>();

        // 构建查询条件
        if (StringUtils.hasText(username)) {
            queryWrapper.like("username", username);
        }
        if (StringUtils.hasText(module)) {
            queryWrapper.eq("module", module);
        }
        if (StringUtils.hasText(operationType)) {
            queryWrapper.eq("operation_type", operationType);
        }
        if (StringUtils.hasText(status)) {
            queryWrapper.eq("status", status);
        }
        if (startTime != null) {
            queryWrapper.ge("create_time", startTime);
        }
        if (endTime != null) {
            queryWrapper.le("create_time", endTime);
        }

        // 按创建时间倒序排列
        queryWrapper.orderByDesc("create_time");

        IPage<SysLog> result = this.page(page, queryWrapper);

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("records", result.getRecords());
        resultMap.put("total", result.getTotal());
        resultMap.put("current", result.getCurrent());
        resultMap.put("size", result.getSize());
        resultMap.put("pages", result.getPages());

        return resultMap;
    }

    @Override
    public void saveLog(String username, String realName, String module, String operationType, 
                       String description, String requestUrl, String requestMethod, 
                       String requestParams, String responseResult, String ip, String userAgent, 
                       String status, String errorMsg, Long responseTime) {
        try {
            SysLog sysLog = new SysLog();
            sysLog.setUsername(username);
            sysLog.setRealName(realName);
            sysLog.setModule(module);
            sysLog.setOperationType(operationType);
            sysLog.setDescription(description);
            sysLog.setRequestUrl(requestUrl);
            sysLog.setRequestMethod(requestMethod);
            sysLog.setRequestParams(requestParams);
            sysLog.setResponseResult(responseResult);
            sysLog.setIp(ip);
            sysLog.setUserAgent(userAgent);
            sysLog.setStatus(status);
            sysLog.setErrorMsg(errorMsg);
            sysLog.setResponseTime(responseTime);
            sysLog.setCreateTime(LocalDateTime.now());

            this.save(sysLog);
        } catch (Exception e) {
            log.error("保存系统日志失败", e);
        }
    }

    @Override
    public boolean clearAllLogs() {
        try {
            QueryWrapper<SysLog> queryWrapper = new QueryWrapper<>();
            return this.remove(queryWrapper);
        } catch (Exception e) {
            log.error("清空系统日志失败", e);
            return false;
        }
    }

    @Override
    public byte[] exportLogs(String username, String module, String operationType, String status,
                           LocalDateTime startTime, LocalDateTime endTime) {
        try {
            // 查询数据
            QueryWrapper<SysLog> queryWrapper = new QueryWrapper<>();
            if (StringUtils.hasText(username)) {
                queryWrapper.like("username", username);
            }
            if (StringUtils.hasText(module)) {
                queryWrapper.eq("module", module);
            }
            if (StringUtils.hasText(operationType)) {
                queryWrapper.eq("operation_type", operationType);
            }
            if (StringUtils.hasText(status)) {
                queryWrapper.eq("status", status);
            }
            if (startTime != null) {
                queryWrapper.ge("create_time", startTime);
            }
            if (endTime != null) {
                queryWrapper.le("create_time", endTime);
            }
            queryWrapper.orderByDesc("create_time");

            List<SysLog> logs = this.list(queryWrapper);

            // 创建CSV格式的数据
            StringBuilder csvContent = new StringBuilder();

            // 添加BOM以支持中文
            csvContent.append("\uFEFF");

            // 添加标题行
            csvContent.append("日志ID,操作用户,用户姓名,操作模块,操作类型,操作描述,IP地址,操作状态,操作时间\n");

            // 填充数据
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            for (SysLog sysLog : logs) {
                csvContent.append(escapeCSV(sysLog.getLogId())).append(",");
                csvContent.append(escapeCSV(sysLog.getUsername())).append(",");
                csvContent.append(escapeCSV(sysLog.getRealName())).append(",");
                csvContent.append(escapeCSV(getModuleName(sysLog.getModule()))).append(",");
                csvContent.append(escapeCSV(getOperationTypeName(sysLog.getOperationType()))).append(",");
                csvContent.append(escapeCSV(sysLog.getDescription())).append(",");
                csvContent.append(escapeCSV(sysLog.getIp())).append(",");
                csvContent.append(escapeCSV("SUCCESS".equals(sysLog.getStatus()) ? "成功" : "失败")).append(",");
                csvContent.append(escapeCSV(sysLog.getCreateTime() != null ?
                    sysLog.getCreateTime().format(formatter) : "")).append("\n");
            }

            return csvContent.toString().getBytes("UTF-8");
        } catch (Exception e) {
            log.error("导出系统日志失败", e);
            throw new RuntimeException("导出系统日志失败", e);
        }
    }

    /**
     * CSV字段转义
     */
    private String escapeCSV(String field) {
        if (field == null) {
            return "";
        }
        // 如果包含逗号、引号或换行符，需要用引号包围并转义内部引号
        if (field.contains(",") || field.contains("\"") || field.contains("\n")) {
            return "\"" + field.replace("\"", "\"\"") + "\"";
        }
        return field;
    }

    @Override
    public Map<String, Object> getLogStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        try {
            // 总日志数
            long totalCount = this.count();
            statistics.put("totalCount", totalCount);

            // 今日日志数
            LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
            LocalDateTime todayEnd = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59).withNano(999999999);
            QueryWrapper<SysLog> todayWrapper = new QueryWrapper<>();
            todayWrapper.between("create_time", todayStart, todayEnd);
            long todayCount = this.count(todayWrapper);
            statistics.put("todayCount", todayCount);

            // 成功操作数
            QueryWrapper<SysLog> successWrapper = new QueryWrapper<>();
            successWrapper.eq("status", "SUCCESS");
            long successCount = this.count(successWrapper);
            statistics.put("successCount", successCount);

            // 失败操作数
            QueryWrapper<SysLog> failWrapper = new QueryWrapper<>();
            failWrapper.eq("status", "FAIL");
            long failCount = this.count(failWrapper);
            statistics.put("failCount", failCount);

        } catch (Exception e) {
            log.error("获取日志统计信息失败", e);
        }

        return statistics;
    }

    @Override
    public List<Map<String, String>> getLogModules() {
        List<Map<String, String>> modules = new ArrayList<>();
        
        String[][] moduleArray = {
            {"USER", "用户管理"},
            {"ROLE", "角色管理"},
            {"DEPT", "部门管理"},
            {"DOCUMENT", "公文管理"},
            {"SYSTEM", "系统设置"}
        };

        for (String[] module : moduleArray) {
            Map<String, String> moduleMap = new HashMap<>();
            moduleMap.put("value", module[0]);
            moduleMap.put("label", module[1]);
            modules.add(moduleMap);
        }

        return modules;
    }

    @Override
    public List<Map<String, String>> getLogOperationTypes() {
        List<Map<String, String>> operationTypes = new ArrayList<>();
        
        String[][] typeArray = {
            {"LOGIN", "登录"},
            {"LOGOUT", "登出"},
            {"CREATE", "新增"},
            {"UPDATE", "修改"},
            {"DELETE", "删除"},
            {"SELECT", "查询"}
        };

        for (String[] type : typeArray) {
            Map<String, String> typeMap = new HashMap<>();
            typeMap.put("value", type[0]);
            typeMap.put("label", type[1]);
            operationTypes.add(typeMap);
        }

        return operationTypes;
    }

    @Override
    public void saveLoginLog(String username, String realName, String ip, String userAgent, 
                           String status, String errorMsg) {
        String operationType = "LOGIN".equals(status) ? "LOGIN" : "LOGOUT";
        String description = "LOGIN".equals(status) ? "用户登录系统" : "用户登出系统";
        
        saveLog(username, realName, "USER", operationType, description, 
               null, null, null, null, ip, userAgent, status, errorMsg, null);
    }

    @Override
    public void saveOperationLog(String username, String realName, String module, String operationType, 
                               String description, String ip, String status) {
        saveLog(username, realName, module, operationType, description, 
               null, null, null, null, ip, null, status, null, null);
    }

    /**
     * 获取模块名称
     */
    private String getModuleName(String module) {
        Map<String, String> moduleMap = new HashMap<>();
        moduleMap.put("USER", "用户管理");
        moduleMap.put("ROLE", "角色管理");
        moduleMap.put("DEPT", "部门管理");
        moduleMap.put("DOCUMENT", "公文管理");
        moduleMap.put("SYSTEM", "系统设置");
        return moduleMap.getOrDefault(module, module);
    }

    /**
     * 获取操作类型名称
     */
    private String getOperationTypeName(String operationType) {
        Map<String, String> typeMap = new HashMap<>();
        typeMap.put("LOGIN", "登录");
        typeMap.put("LOGOUT", "登出");
        typeMap.put("CREATE", "新增");
        typeMap.put("UPDATE", "修改");
        typeMap.put("DELETE", "删除");
        typeMap.put("SELECT", "查询");
        return typeMap.getOrDefault(operationType, operationType);
    }
}
