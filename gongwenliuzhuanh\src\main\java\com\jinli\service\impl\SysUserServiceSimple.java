package com.jinli.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinli.dto.UserQueryDTO;
import com.jinli.entity.SysUser;
import com.jinli.gongwen.mapper.SysUserMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.ZoneId;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;

/**
 * 用户服务简化实现类
 * 
 * <AUTHOR>
 * @since 2025-01-30
 */
@Service("sysUserService")
public class SysUserServiceSimple {

    private static final Logger log = LoggerFactory.getLogger(SysUserServiceSimple.class);

    @Autowired
    private SysUserMapper userMapper;

    /**
     * 分页查询用户列表
     */
    public Map<String, Object> selectUserPage(UserQueryDTO queryDTO) {
        log.info("分页查询用户列表，查询条件：{}", queryDTO);
        
        try {
            // 验证分页参数
            if (queryDTO.getPageNum() == null || queryDTO.getPageNum() < 1) {
                queryDTO.setPageNum(1);
            }
            if (queryDTO.getPageSize() == null || queryDTO.getPageSize() < 1) {
                queryDTO.setPageSize(10);
            }
            
            // 创建分页对象
            Page<com.jinli.gongwen.entity.SysUser> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());

            // 使用现有的分页查询方法
            IPage<com.jinli.gongwen.entity.SysUser> pageResult = userMapper.selectUserPage(
                page,
                queryDTO.getUsername(),
                queryDTO.getRealName(),
                queryDTO.getDepartmentId(),
                queryDTO.getStatus()
            );

            // 转换为新的实体类
            List<SysUser> users = new ArrayList<>();
            for (com.jinli.gongwen.entity.SysUser gongwenUser : pageResult.getRecords()) {
                SysUser user = convertToNewUser(gongwenUser);
                users.add(user);
            }

            // 获取总数
            Long total = pageResult.getTotal();
            
            Map<String, Object> result = new HashMap<>();
            result.put("list", users);
            result.put("total", total != null ? total : 0);
            result.put("pageNum", queryDTO.getPageNum());
            result.put("pageSize", queryDTO.getPageSize());
            result.put("pages", (total + queryDTO.getPageSize() - 1) / queryDTO.getPageSize());
            
            return result;
            
        } catch (Exception e) {
            log.error("查询用户列表失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("list", new ArrayList<>());
            result.put("total", 0);
            result.put("pageNum", queryDTO.getPageNum());
            result.put("pageSize", queryDTO.getPageSize());
            result.put("pages", 0);
            return result;
        }
    }

    /**
     * 转换用户实体
     */
    private SysUser convertToNewUser(com.jinli.gongwen.entity.SysUser gongwenUser) {
        SysUser user = new SysUser();
        user.setUserId(gongwenUser.getUserId());
        user.setUsername(gongwenUser.getUsername());
        user.setRealName(gongwenUser.getRealName());
        user.setEmail(gongwenUser.getEmail());
        user.setPhone(gongwenUser.getPhone());
        user.setDepartmentId(gongwenUser.getDepartmentId());
        user.setStatus(gongwenUser.getStatus());
        user.setLoginIp(gongwenUser.getLoginIp());

        // 转换Date到LocalDateTime
        if (gongwenUser.getLoginDate() != null) {
            user.setLoginDate(gongwenUser.getLoginDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
        }
        if (gongwenUser.getCreateTime() != null) {
            user.setCreateTime(gongwenUser.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
        }
        if (gongwenUser.getUpdateTime() != null) {
            user.setUpdateTime(gongwenUser.getUpdateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
        }

        user.setRemark(gongwenUser.getRemark());

        // 设置扩展字段
        user.setDeptName(gongwenUser.getDepartmentName());
        user.setRoleName(gongwenUser.getRoleName());
        user.setRoleKey(gongwenUser.getRoleKey());

        // 清除密码
        user.clearSensitiveInfo();

        return user;
    }

    /**
     * 根据用户ID查询用户详情
     */
    public SysUser selectUserById(String userId) {
        log.info("根据ID查询用户详情，用户ID：{}", userId);
        
        try {
            com.jinli.gongwen.entity.SysUser gongwenUser = userMapper.selectUserDetailById(userId);
            if (gongwenUser != null) {
                return convertToNewUser(gongwenUser);
            }
            return null;
        } catch (Exception e) {
            log.error("查询用户详情失败", e);
            return null;
        }
    }

    /**
     * 新增用户
     */
    public boolean insertUser(SysUser user) {
        log.info("新增用户，用户信息：{}", user);

        try {
            // 转换为原有实体
            com.jinli.gongwen.entity.SysUser gongwenUser = convertToGongwenUser(user);
            int result = userMapper.insert(gongwenUser);
            return result > 0;
        } catch (Exception e) {
            log.error("新增用户失败", e);
            return false;
        }
    }

    /**
     * 更新用户
     */
    public boolean updateUser(SysUser user) {
        log.info("更新用户，用户信息：{}", user);

        try {
            // 转换为原有实体
            com.jinli.gongwen.entity.SysUser gongwenUser = convertToGongwenUser(user);
            int result = userMapper.updateById(gongwenUser);
            return result > 0;
        } catch (Exception e) {
            log.error("更新用户失败", e);
            return false;
        }
    }

    /**
     * 转换为原有用户实体
     */
    private com.jinli.gongwen.entity.SysUser convertToGongwenUser(SysUser user) {
        com.jinli.gongwen.entity.SysUser gongwenUser = new com.jinli.gongwen.entity.SysUser();
        gongwenUser.setUserId(user.getUserId());
        gongwenUser.setUsername(user.getUsername());
        gongwenUser.setPassword(user.getPassword());
        gongwenUser.setRealName(user.getRealName());
        gongwenUser.setEmail(user.getEmail());
        gongwenUser.setPhone(user.getPhone());
        gongwenUser.setDepartmentId(user.getDepartmentId());
        gongwenUser.setStatus(user.getStatus());
        gongwenUser.setLoginIp(user.getLoginIp());
        gongwenUser.setRemark(user.getRemark());

        // 转换LocalDateTime到Date
        if (user.getLoginDate() != null) {
            gongwenUser.setLoginDate(java.util.Date.from(user.getLoginDate().atZone(ZoneId.systemDefault()).toInstant()));
        }
        if (user.getCreateTime() != null) {
            gongwenUser.setCreateTime(java.util.Date.from(user.getCreateTime().atZone(ZoneId.systemDefault()).toInstant()));
        }
        if (user.getUpdateTime() != null) {
            gongwenUser.setUpdateTime(java.util.Date.from(user.getUpdateTime().atZone(ZoneId.systemDefault()).toInstant()));
        }

        return gongwenUser;
    }

    /**
     * 更新用户状态
     */
    public boolean updateUserStatus(String userId, String status) {
        log.info("更新用户状态，用户ID：{}，状态：{}", userId, status);

        try {
            // 先查询用户
            com.jinli.gongwen.entity.SysUser gongwenUser = userMapper.selectById(userId);
            if (gongwenUser != null) {
                gongwenUser.setStatus(status);
                int result = userMapper.updateById(gongwenUser);
                return result > 0;
            }
            return false;
        } catch (Exception e) {
            log.error("更新用户状态失败", e);
            return false;
        }
    }

    /**
     * 删除用户
     */
    public boolean deleteUser(String userId) {
        log.info("删除用户，用户ID：{}", userId);

        try {
            int result = userMapper.deleteById(userId);
            return result > 0;
        } catch (Exception e) {
            log.error("删除用户失败", e);
            return false;
        }
    }
}
