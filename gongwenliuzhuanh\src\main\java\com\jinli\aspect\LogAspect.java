package com.jinli.aspect;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jinli.service.ISysLogService;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 系统日志切面
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
@Aspect
@Component
public class LogAspect {

    private static final Logger log = LoggerFactory.getLogger(LogAspect.class);

    @Autowired
    private ISysLogService logService;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 系统日志注解
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    public @interface SystemLog {
        /**
         * 操作模块
         */
        String module() default "";

        /**
         * 操作类型
         */
        String operationType() default "";

        /**
         * 操作描述
         */
        String description() default "";

        /**
         * 是否记录请求参数
         */
        boolean recordParams() default true;

        /**
         * 是否记录响应结果
         */
        boolean recordResult() default false;
    }

    /**
     * 定义切点
     */
    @Pointcut("@annotation(systemLog)")
    public void logPointcut(SystemLog systemLog) {
    }

    /**
     * 环绕通知
     */
    @Around("logPointcut(systemLog)")
    public Object around(ProceedingJoinPoint joinPoint, SystemLog systemLog) throws Throwable {
        long startTime = System.currentTimeMillis();
        
        // 获取请求信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes != null ? attributes.getRequest() : null;
        
        String requestUrl = request != null ? request.getRequestURL().toString() : "";
        String requestMethod = request != null ? request.getMethod() : "";
        String ip = getClientIpAddress(request);
        String userAgent = request != null ? request.getHeader("User-Agent") : "";
        
        // 获取请求参数
        String requestParams = "";
        if (systemLog.recordParams()) {
            try {
                Object[] args = joinPoint.getArgs();
                if (args != null && args.length > 0) {
                    requestParams = objectMapper.writeValueAsString(args);
                }
            } catch (Exception e) {
                log.warn("序列化请求参数失败", e);
                requestParams = "参数序列化失败";
            }
        }

        Object result = null;
        String status = "SUCCESS";
        String errorMsg = null;
        String responseResult = "";

        try {
            // 执行目标方法
            result = joinPoint.proceed();
            
            // 记录响应结果
            if (systemLog.recordResult() && result != null) {
                try {
                    responseResult = objectMapper.writeValueAsString(result);
                } catch (Exception e) {
                    log.warn("序列化响应结果失败", e);
                    responseResult = "结果序列化失败";
                }
            }
        } catch (Exception e) {
            status = "FAIL";
            errorMsg = e.getMessage();
            throw e;
        } finally {
            long endTime = System.currentTimeMillis();
            long responseTime = endTime - startTime;

            // 异步记录日志
            try {
                // 这里应该从当前登录用户获取用户信息，暂时使用默认值
                String username = getCurrentUsername();
                String realName = getCurrentUserRealName();
                
                logService.saveLog(
                    username,
                    realName,
                    systemLog.module(),
                    systemLog.operationType(),
                    systemLog.description(),
                    requestUrl,
                    requestMethod,
                    requestParams,
                    responseResult,
                    ip,
                    userAgent,
                    status,
                    errorMsg,
                    responseTime
                );
            } catch (Exception e) {
                log.error("记录系统日志失败", e);
            }
        }

        return result;
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        if (request == null) {
            return "unknown";
        }

        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }

        // 如果是多级代理，取第一个IP
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }

        return ip;
    }

    /**
     * 获取当前登录用户名
     * TODO: 实现从Security Context或JWT Token中获取用户信息
     */
    private String getCurrentUsername() {
        // 这里应该从Spring Security或JWT Token中获取当前用户信息
        // 暂时返回默认值
        return "admin";
    }

    /**
     * 获取当前登录用户真实姓名
     * TODO: 实现从Security Context或JWT Token中获取用户信息
     */
    private String getCurrentUserRealName() {
        // 这里应该从Spring Security或JWT Token中获取当前用户信息
        // 暂时返回默认值
        return "系统管理员";
    }
}

/**
 * 系统日志注解
 * 使用示例：
 * 
 * @SystemLog(module = "USER", operationType = "CREATE", description = "新增用户")
 * public Result<Void> createUser(@RequestBody User user) {
 *     // 业务逻辑
 *     return Result.success();
 * }
 */
