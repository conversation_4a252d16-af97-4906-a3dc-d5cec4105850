-- =====================================================
-- 河北金力集团公文流转系统完整测试数据
-- 数据库：达梦8 (DM8)
-- 创建时间：2025-07-29
-- 说明：严格按照需求文档精简用户，包含完整测试数据
-- =====================================================

-- ========================================
-- 1. 数据清理（按外键依赖关系顺序，忽略不存在的表）
-- ========================================

-- 清理公文相关数据（如果表存在）
BEGIN
    EXECUTE IMMEDIATE 'DELETE FROM DOC_RECEIVE_RECORD';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DELETE FROM DOC_ATTACHMENT';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DELETE FROM DOC_FLOW_RECORD';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DELETE FROM DOC_DOCUMENT';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

-- 清理用户相关数据
BEGIN
    EXECUTE IMMEDIATE 'DELETE FROM SYS_USER_ROLE';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DELETE FROM SYS_USER';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

-- 清理角色和部门数据
BEGIN
    EXECUTE IMMEDIATE 'DELETE FROM SYS_ROLE';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DELETE FROM SYS_DEPARTMENT';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

-- 清理字典和配置数据
BEGIN
    EXECUTE IMMEDIATE 'DELETE FROM SYS_DOC_TYPE';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DELETE FROM SYS_FLOW_CONFIG';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DELETE FROM SYS_CONFIG';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

-- 清理系统日志（如果表存在）
BEGIN
    EXECUTE IMMEDIATE 'DELETE FROM SYS_LOG';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

-- 提交清理操作
COMMIT;

-- ========================================
-- 2. 部门数据（严格按需求文档）
-- ========================================
INSERT INTO SYS_DEPARTMENT (DEPT_ID, DEPT_NAME, DEPT_CODE, PARENT_ID, ORDER_NUM, LEADER, PHONE, EMAIL, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) VALUES
('DEPT001', '河北金力集团', 'JINLI_GROUP', NULL, 1, '张厂长', '0311-88888888', '<EMAIL>', '1', SYSDATE, SYSDATE, '集团总部'),
('DEPT002', '办公室', 'OFFICE', 'DEPT001', 2, '李主任', '0311-88888801', '<EMAIL>', '1', SYSDATE, SYSDATE, '负责公文流转管理'),
('DEPT003', '生产部', 'PRODUCTION', 'DEPT001', 3, '王经理', '0311-88888802', '<EMAIL>', '1', SYSDATE, SYSDATE, '负责主厂区、一分厂、二分厂、三分厂生产管理'),
('DEPT004', '销售部', 'SALES', 'DEPT001', 4, '赵经理', '0311-88888803', '<EMAIL>', '1', SYSDATE, SYSDATE, '负责销售管理'),
('DEPT005', '财务部', 'FINANCE', 'DEPT001', 5, '钱经理', '0311-88888804', '<EMAIL>', '1', SYSDATE, SYSDATE, '负责财务管理');

-- ========================================
-- 3. 角色数据
-- ========================================
INSERT INTO SYS_ROLE (ROLE_ID, ROLE_NAME, ROLE_KEY, ROLE_SORT, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) VALUES
('ROLE001', '系统管理员', 'ADMIN', 1, '1', SYSDATE, SYSDATE, '用户管理、角色管理、公文流转流程管理'),
('ROLE002', '厂长', 'DIRECTOR', 2, '1', SYSDATE, SYSDATE, '审签公文、浏览已发所有公文、查询公文'),
('ROLE003', '生产副厂长', 'VICE_DIRECTOR_PROD', 3, '1', SYSDATE, SYSDATE, '审核公文、浏览已发所有公文、查询公文'),
('ROLE004', '销售副厂长', 'VICE_DIRECTOR_SALES', 4, '1', SYSDATE, SYSDATE, '审核公文、浏览已发所有公文、查询公文'),
('ROLE005', '财务副厂长', 'VICE_DIRECTOR_FINANCE', 5, '1', SYSDATE, SYSDATE, '审核公文、浏览已发所有公文、查询公文'),
('ROLE006', '办公室主任', 'OFFICE_DIRECTOR', 6, '1', SYSDATE, SYSDATE, '修改公文、公文流转、删除公文、公文发送'),
('ROLE007', '部门经理', 'DEPT_MANAGER', 7, '1', SYSDATE, SYSDATE, '公文拟制、签收公文、浏览公文'),
('ROLE008', '普通员工', 'EMPLOYEE', 8, '1', SYSDATE, SYSDATE, '公文拟制、签收公文、浏览公文');

-- ========================================
-- 4. 用户数据（精简至12个用户）
-- ========================================
INSERT INTO SYS_USER (USER_ID, USERNAME, PASSWORD, REAL_NAME, EMAIL, PHONE, DEPARTMENT_ID, STATUS, LOGIN_IP, LOGIN_DATE, CREATE_TIME, UPDATE_TIME, REMARK) VALUES
-- 管理层（5个）
('USER001', 'admin', '$2a$10$7JB720yubVSOfvVWbfXCOOlWpZXGlDYDdTOWkTRDDHQGpVw62VsMO', '系统管理员', '<EMAIL>', '13800000001', 'DEPT002', '1', '127.0.0.1', SYSDATE, SYSDATE, SYSDATE, '系统管理员账号，密码：123456'),
('USER002', 'director', '$2a$10$7JB720yubVSOfvVWbfXCOOlWpZXGlDYDdTOWkTRDDHQGpVw62VsMO', '张厂长', '<EMAIL>', '13800000002', 'DEPT001', '1', NULL, NULL, SYSDATE, SYSDATE, '厂长，密码：123456'),
('USER003', 'vice_prod', '$2a$10$7JB720yubVSOfvVWbfXCOOlWpZXGlDYDdTOWkTRDDHQGpVw62VsMO', '王副厂长', '<EMAIL>', '13800000003', 'DEPT003', '1', NULL, NULL, SYSDATE, SYSDATE, '生产副厂长，密码：123456'),
('USER004', 'vice_sales', '$2a$10$7JB720yubVSOfvVWbfXCOOlWpZXGlDYDdTOWkTRDDHQGpVw62VsMO', '赵副厂长', '<EMAIL>', '13800000004', 'DEPT004', '1', NULL, NULL, SYSDATE, SYSDATE, '销售副厂长，密码：123456'),
('USER005', 'vice_finance', '$2a$10$7JB720yubVSOfvVWbfXCOOlWpZXGlDYDdTOWkTRDDHQGpVw62VsMO', '钱副厂长', '<EMAIL>', '13800000005', 'DEPT005', '1', NULL, NULL, SYSDATE, SYSDATE, '财务副厂长，密码：123456'),
-- 办公室（1个）
('USER006', 'office', '$2a$10$7JB720yubVSOfvVWbfXCOOlWpZXGlDYDdTOWkTRDDHQGpVw62VsMO', '李主任', '<EMAIL>', '13800000006', 'DEPT002', '1', NULL, NULL, SYSDATE, SYSDATE, '办公室主任，密码：123456'),
-- 部门经理（3个）
('USER007', 'prod_manager', '$2a$10$7JB720yubVSOfvVWbfXCOOlWpZXGlDYDdTOWkTRDDHQGpVw62VsMO', '王经理', '<EMAIL>', '13800000007', 'DEPT003', '1', NULL, NULL, SYSDATE, SYSDATE, '生产部经理，密码：123456'),
('USER008', 'sales_manager', '$2a$10$7JB720yubVSOfvVWbfXCOOlWpZXGlDYDdTOWkTRDDHQGpVw62VsMO', '赵经理', '<EMAIL>', '13800000008', 'DEPT004', '1', NULL, NULL, SYSDATE, SYSDATE, '销售部经理，密码：123456'),
('USER009', 'finance_manager', '$2a$10$7JB720yubVSOfvVWbfXCOOlWpZXGlDYDdTOWkTRDDHQGpVw62VsMO', '钱经理', '<EMAIL>', '13800000009', 'DEPT005', '1', NULL, NULL, SYSDATE, SYSDATE, '财务部经理，密码：123456'),
-- 普通员工（3个）
('USER010', 'prod_employee', '$2a$10$7JB720yubVSOfvVWbfXCOOlWpZXGlDYDdTOWkTRDDHQGpVw62VsMO', '张三', '<EMAIL>', '13800000010', 'DEPT003', '1', NULL, NULL, SYSDATE, SYSDATE, '生产部员工，密码：123456'),
('USER011', 'sales_employee', '$2a$10$7JB720yubVSOfvVWbfXCOOlWpZXGlDYDdTOWkTRDDHQGpVw62VsMO', '李四', '<EMAIL>', '13800000011', 'DEPT004', '1', NULL, NULL, SYSDATE, SYSDATE, '销售部员工，密码：123456'),
('USER012', 'finance_employee', '$2a$10$7JB720yubVSOfvVWbfXCOOlWpZXGlDYDdTOWkTRDDHQGpVw62VsMO', '王五', '<EMAIL>', '13800000012', 'DEPT005', '1', NULL, NULL, SYSDATE, SYSDATE, '财务部员工，密码：123456');

-- ========================================
-- 5. 用户角色关联
-- ========================================
INSERT INTO SYS_USER_ROLE (USER_ID, ROLE_ID) VALUES
('USER001', 'ROLE001'),  -- admin -> 系统管理员
('USER002', 'ROLE002'),  -- director -> 厂长
('USER003', 'ROLE003'),  -- vice_prod -> 生产副厂长
('USER004', 'ROLE004'),  -- vice_sales -> 销售副厂长
('USER005', 'ROLE005'),  -- vice_finance -> 财务副厂长
('USER006', 'ROLE006'),  -- office -> 办公室主任
('USER007', 'ROLE007'),  -- prod_manager -> 部门经理
('USER008', 'ROLE007'),  -- sales_manager -> 部门经理
('USER009', 'ROLE007'),  -- finance_manager -> 部门经理
('USER010', 'ROLE008'),  -- prod_employee -> 普通员工
('USER011', 'ROLE008'),  -- sales_employee -> 普通员工
('USER012', 'ROLE008');  -- finance_employee -> 普通员工

-- ========================================
-- 6. 公文类型字典
-- ========================================
INSERT INTO SYS_DOC_TYPE (TYPE_ID, TYPE_NAME, TYPE_CODE, PARENT_ID, ORDER_NUM, STATUS, CREATE_TIME, REMARK) VALUES
('TYPE001', '通知', 'NOTICE', NULL, 1, '1', SYSDATE, '通知类公文'),
('TYPE002', '报告', 'REPORT', NULL, 2, '1', SYSDATE, '报告类公文'),
('TYPE003', '请示', 'REQUEST', NULL, 3, '1', SYSDATE, '请示类公文'),
('TYPE004', '决定', 'DECISION', NULL, 4, '1', SYSDATE, '决定类公文'),
('TYPE005', '意见', 'OPINION', NULL, 5, '1', SYSDATE, '意见类公文'),
('TYPE006', '函', 'LETTER', NULL, 6, '1', SYSDATE, '函件类公文'),
('TYPE007', '会议纪要', 'MINUTES', NULL, 7, '1', SYSDATE, '会议纪要类公文');

-- ========================================
-- 7. 系统配置
-- ========================================
INSERT INTO SYS_CONFIG (CONFIG_ID, CONFIG_NAME, CONFIG_KEY, CONFIG_VALUE, CONFIG_TYPE, CREATE_TIME, UPDATE_TIME, REMARK) VALUES
('CFG001', '系统名称', 'sys.name', '河北金力集团公文流转系统', 'Y', SYSDATE, SYSDATE, '系统名称配置'),
('CFG002', '系统版本', 'sys.version', '1.0.0', 'Y', SYSDATE, SYSDATE, '系统版本号'),
('CFG003', '文件上传路径', 'file.upload.path', '/uploads/', 'N', SYSDATE, SYSDATE, '文件上传路径'),
('CFG004', '文件最大大小', 'file.max.size', '10485760', 'N', SYSDATE, SYSDATE, '文件最大大小(字节)'),
('CFG005', '公文编号前缀', 'doc.number.prefix', 'JINLI', 'N', SYSDATE, SYSDATE, '公文编号前缀');

-- ========================================
-- 8. 流程配置
-- ========================================
INSERT INTO SYS_FLOW_CONFIG (CONFIG_ID, FLOW_NAME, FLOW_KEY, FLOW_STEPS, STATUS, CREATE_TIME, UPDATE_TIME) VALUES
('FLOW001', '标准公文流转流程', 'STANDARD_FLOW',
'[{"step":"DRAFT","name":"拟稿","handler":"DEPT_USER"},{"step":"OFFICE_EDIT","name":"办公室修改","handler":"OFFICE"},{"step":"VICE_REVIEW","name":"副厂长审核","handler":"VICE_DIRECTOR"},{"step":"DIRECTOR_APPROVE","name":"厂长审签","handler":"DIRECTOR"},{"step":"OFFICE_SEND","name":"办公室发送","handler":"OFFICE"},{"step":"DEPT_RECEIVE","name":"部门签收","handler":"DEPT_USER"}]',
'1', SYSDATE, SYSDATE);

-- ========================================
-- 9. 公文数据（支持完整业务流程测试）
-- ========================================
INSERT INTO DOC_DOCUMENT (DOC_ID, DOC_TITLE, DOC_NUMBER, DOC_TYPE, DOC_LEVEL, DOC_CONTENT, DOC_STATUS, CREATE_USER_ID, CREATE_DEPT_ID, CREATE_TIME, UPDATE_TIME, CURRENT_HANDLER, CURRENT_STEP, DEADLINE, REMARK) VALUES
-- 已完成流转的公文
('DOC001', '关于加强安全生产管理的通知', 'JINLI-2025-001', 'NOTICE', '紧急',
'各部门：

为进一步加强我集团安全生产管理工作，确保员工生命安全和企业财产安全，现就有关事项通知如下：

一、提高安全意识
各部门要高度重视安全生产工作，定期组织安全培训，提高员工安全意识。

二、完善安全制度
建立健全安全生产责任制，明确各级人员安全职责。

三、加强安全检查
定期开展安全隐患排查，及时整改发现的问题。

特此通知。

河北金力集团
2025年7月20日',
'COMPLETED', 'USER007', 'DEPT003', SYSDATE - 10, SYSDATE - 1, NULL, 'DEPT_RECEIVE', SYSDATE + 30, '已完成流转'),

('DOC002', '2025年第一季度生产计划报告', 'JINLI-2025-002', 'REPORT', '普通',
'集团领导：

现将2025年第一季度生产计划情况报告如下：

一、生产目标
第一季度计划生产产品A 1000台，产品B 800台，产品C 600台。

二、资源配置
需要原材料采购预算500万元，人员配置120人。

三、质量要求
严格按照ISO9001标准执行，确保产品合格率达到99%以上。

四、时间安排
1月份完成30%，2月份完成30%，3月份完成40%。

请领导审批。

生产部
2025年7月21日',
'APPROVED', 'USER007', 'DEPT003', SYSDATE - 8, SYSDATE - 2, NULL, 'DIRECTOR_APPROVE', SYSDATE + 15, '已通过厂长审签');

-- 流转中的公文
('DOC003', '关于调整销售策略的请示', 'JINLI-2025-003', 'REQUEST', '紧急',
'集团领导：

根据市场变化情况，现申请调整销售策略如下：

一、目标市场调整
重点开拓华北、华东市场，适度收缩西南市场。

二、产品结构优化
加大高端产品推广力度，逐步淘汰低端产品。

三、销售渠道拓展
积极发展线上销售渠道，建立电商平台。

四、预期效果
预计可提高销售收入20%，降低销售成本15%。

请领导批准。

销售部
2025年7月25日',
'PROCESSING', 'USER008', 'DEPT004', SYSDATE - 5, SYSDATE - 1, 'USER004', 'VICE_REVIEW', SYSDATE + 7, '销售副厂长审核中'),

('DOC004', '财务预算执行情况报告', 'JINLI-2025-004', 'REPORT', '普通',
'集团领导：

现将2024年财务预算执行情况报告如下：

一、收入情况
全年实现营业收入2.8亿元，完成预算的112%。

二、支出情况
全年支出2.5亿元，控制在预算范围内。

三、利润情况
实现净利润3000万元，超额完成预算目标。

四、资金状况
年末现金余额5000万元，资金充裕。

五、存在问题
应收账款增长较快，需加强回收管理。

请领导审阅。

财务部
2025年7月22日',
'PROCESSING', 'USER009', 'DEPT005', SYSDATE - 6, SYSDATE, 'USER002', 'DIRECTOR_APPROVE', SYSDATE + 10, '厂长审签中'),

-- 草稿状态的公文
('DOC005', '关于召开年度工作会议的通知', 'JINLI-2025-005', 'NOTICE', '普通',
'各部门：

定于2025年8月15日召开集团年度工作会议，现将有关事项通知如下：

一、会议时间
2025年8月15日上午9:00-12:00

二、会议地点
集团总部三楼会议室

三、参会人员
各部门负责人及以上领导

四、会议议程
1. 2024年工作总结
2. 2025年工作计划
3. 表彰先进集体和个人
4. 领导讲话

请准时参加。

办公室
2025年7月28日',
'DRAFT', 'USER006', 'DEPT002', SYSDATE - 1, SYSDATE - 1, NULL, 'DRAFT', SYSDATE + 20, '草稿状态'),

('DOC006', '设备采购申请', 'JINLI-2025-006', 'REQUEST', '紧急',
'集团领导：

为提高生产效率，现申请采购以下设备：

一、设备清单
1. 数控机床 2台，单价80万元
2. 检测设备 1套，价格50万元
3. 包装设备 1套，价格30万元

二、采购理由
现有设备老化严重，维修成本高，影响生产效率。

三、资金来源
设备采购预算160万元，从设备更新基金中支出。

四、预期效果
预计可提高生产效率30%，降低不良品率至1%以下。

请领导批准。

生产部
2025年7月29日',
'PROCESSING', 'USER010', 'DEPT003', SYSDATE, SYSDATE, 'USER006', 'OFFICE_EDIT', SYSDATE + 5, '办公室修改中');

-- ========================================
-- 10. 公文流转记录
-- ========================================
INSERT INTO DOC_FLOW_RECORD (RECORD_ID, DOC_ID, FROM_USER_ID, TO_USER_ID, FROM_DEPT_ID, TO_DEPT_ID, FLOW_STEP, ACTION, OPINION, PROCESS_TIME, REMARK) VALUES
-- DOC001 (安全生产通知) 的完整流转记录
('REC001', 'DOC001', NULL, 'USER007', NULL, 'DEPT003', 'DRAFT', 'SUBMIT', '创建安全生产管理通知', SYSDATE - 10, '生产部创建'),
('REC002', 'DOC001', 'USER007', 'USER006', 'DEPT003', 'DEPT002', 'OFFICE_EDIT', 'EDIT', '格式规范，内容完善', SYSDATE - 9, '办公室修改'),
('REC003', 'DOC001', 'USER006', 'USER003', 'DEPT002', 'DEPT003', 'VICE_REVIEW', 'APPROVE', '同意发布，请各部门认真执行', SYSDATE - 8, '生产副厂长审批'),
('REC004', 'DOC001', 'USER003', 'USER002', 'DEPT003', 'DEPT001', 'DIRECTOR_APPROVE', 'APPROVE', '批准发布，加强安全管理', SYSDATE - 7, '厂长最终审签'),
('REC005', 'DOC001', 'USER002', 'USER006', 'DEPT001', 'DEPT002', 'OFFICE_SEND', 'SEND', '已发送至各部门', SYSDATE - 6, '办公室发送'),
('REC006', 'DOC001', 'USER006', 'USER007', 'DEPT002', 'DEPT003', 'DEPT_RECEIVE', 'RECEIVE', '已收到，将认真执行', SYSDATE - 5, '生产部签收'),
('REC007', 'DOC001', 'USER006', 'USER008', 'DEPT002', 'DEPT004', 'DEPT_RECEIVE', 'RECEIVE', '已收到并传达', SYSDATE - 4, '销售部签收'),
('REC008', 'DOC001', 'USER006', 'USER009', 'DEPT002', 'DEPT005', 'DEPT_RECEIVE', 'RECEIVE', '已收到，将配合执行', SYSDATE - 3, '财务部签收');

-- DOC002 (生产计划报告) 的流转记录
('REC009', 'DOC002', NULL, 'USER007', NULL, 'DEPT003', 'DRAFT', 'SUBMIT', '提交生产计划报告', SYSDATE - 8, '生产部创建'),
('REC010', 'DOC002', 'USER007', 'USER006', 'DEPT003', 'DEPT002', 'OFFICE_EDIT', 'EDIT', '完善报告格式', SYSDATE - 7, '办公室修改'),
('REC011', 'DOC002', 'USER006', 'USER003', 'DEPT002', 'DEPT003', 'VICE_REVIEW', 'APPROVE', '计划合理，同意执行', SYSDATE - 6, '生产副厂长审批'),
('REC012', 'DOC002', 'USER003', 'USER002', 'DEPT003', 'DEPT001', 'DIRECTOR_APPROVE', 'APPROVE', '批准执行生产计划', SYSDATE - 5, '厂长审签'),

-- DOC003 (销售策略请示) 的流转记录
('REC013', 'DOC003', NULL, 'USER008', NULL, 'DEPT004', 'DRAFT', 'SUBMIT', '申请调整销售策略', SYSDATE - 5, '销售部创建'),
('REC014', 'DOC003', 'USER008', 'USER006', 'DEPT004', 'DEPT002', 'OFFICE_EDIT', 'EDIT', '完善策略内容', SYSDATE - 4, '办公室修改'),
('REC015', 'DOC003', 'USER006', 'USER004', 'DEPT002', 'DEPT004', 'VICE_REVIEW', 'TRANSFER', '正在审核销售策略', SYSDATE - 1, '销售副厂长审核中'),

-- DOC004 (财务报告) 的流转记录
('REC016', 'DOC004', NULL, 'USER009', NULL, 'DEPT005', 'DRAFT', 'SUBMIT', '提交财务预算报告', SYSDATE - 6, '财务部创建'),
('REC017', 'DOC004', 'USER009', 'USER006', 'DEPT005', 'DEPT002', 'OFFICE_EDIT', 'EDIT', '规范报告格式', SYSDATE - 5, '办公室修改'),
('REC018', 'DOC004', 'USER006', 'USER005', 'DEPT002', 'DEPT005', 'VICE_REVIEW', 'APPROVE', '财务数据准确，同意上报', SYSDATE - 4, '财务副厂长审批'),
('REC019', 'DOC004', 'USER005', 'USER002', 'DEPT005', 'DEPT001', 'DIRECTOR_APPROVE', 'TRANSFER', '正在审核财务报告', SYSDATE, '厂长审核中'),

-- DOC006 (设备采购申请) 的流转记录
('REC020', 'DOC006', NULL, 'USER010', NULL, 'DEPT003', 'DRAFT', 'SUBMIT', '申请设备采购', SYSDATE, '生产部员工创建'),
('REC021', 'DOC006', 'USER010', 'USER006', 'DEPT003', 'DEPT002', 'OFFICE_EDIT', 'TRANSFER', '正在修改申请格式', SYSDATE, '办公室修改中');

-- ========================================
-- 11. 公文附件数据
-- ========================================
INSERT INTO DOC_ATTACHMENT (ATTACHMENT_ID, DOC_ID, FILE_NAME, FILE_PATH, FILE_SIZE, FILE_TYPE, UPLOAD_USER_ID, UPLOAD_TIME) VALUES
('ATT001', 'DOC001', '安全生产管理制度.pdf', '/uploads/2025/07/safety_management.pdf', 2048576, 'pdf', 'USER007', SYSDATE - 10),
('ATT002', 'DOC001', '安全检查表.xlsx', '/uploads/2025/07/safety_checklist.xlsx', 1024000, 'xlsx', 'USER007', SYSDATE - 10),
('ATT003', 'DOC002', '生产计划详表.xlsx', '/uploads/2025/07/production_plan.xlsx', 1536000, 'xlsx', 'USER007', SYSDATE - 8),
('ATT004', 'DOC002', '资源需求分析.docx', '/uploads/2025/07/resource_analysis.docx', 512000, 'docx', 'USER007', SYSDATE - 8),
('ATT005', 'DOC003', '市场分析报告.pdf', '/uploads/2025/07/market_analysis.pdf', 3072000, 'pdf', 'USER008', SYSDATE - 5),
('ATT006', 'DOC003', '销售数据统计.xlsx', '/uploads/2025/07/sales_data.xlsx', 1024000, 'xlsx', 'USER008', SYSDATE - 5),
('ATT007', 'DOC004', '财务报表.xlsx', '/uploads/2025/07/financial_report.xlsx', 2048000, 'xlsx', 'USER009', SYSDATE - 6),
('ATT008', 'DOC004', '预算对比分析.pdf', '/uploads/2025/07/budget_analysis.pdf', 1536000, 'pdf', 'USER009', SYSDATE - 6),
('ATT009', 'DOC005', '会议议程.docx', '/uploads/2025/07/meeting_agenda.docx', 256000, 'docx', 'USER006', SYSDATE - 1),
('ATT010', 'DOC006', '设备技术参数.pdf', '/uploads/2025/07/equipment_specs.pdf', 4096000, 'pdf', 'USER010', SYSDATE),
('ATT011', 'DOC006', '设备报价单.pdf', '/uploads/2025/07/equipment_quote.pdf', 1024000, 'pdf', 'USER010', SYSDATE);

-- ========================================
-- 12. 公文签收记录
-- ========================================
INSERT INTO DOC_RECEIVE_RECORD (RECEIVE_ID, DOC_ID, RECEIVE_USER_ID, RECEIVE_DEPT_ID, RECEIVE_TIME, READ_TIME, STATUS, REMARK) VALUES
-- DOC001 的签收记录
('RCV001', 'DOC001', 'USER007', 'DEPT003', SYSDATE - 5, SYSDATE - 4, '1', '生产部已签收'),
('RCV002', 'DOC001', 'USER008', 'DEPT004', SYSDATE - 4, SYSDATE - 3, '1', '销售部已签收'),
('RCV003', 'DOC001', 'USER009', 'DEPT005', SYSDATE - 3, SYSDATE - 2, '1', '财务部已签收'),
('RCV004', 'DOC001', 'USER010', 'DEPT003', SYSDATE - 5, SYSDATE - 4, '1', '生产部员工已签收'),
('RCV005', 'DOC001', 'USER011', 'DEPT004', SYSDATE - 4, SYSDATE - 3, '1', '销售部员工已签收'),
('RCV006', 'DOC001', 'USER012', 'DEPT005', SYSDATE - 3, SYSDATE - 2, '1', '财务部员工已签收');

-- ========================================
-- 13. 数据验证查询
-- ========================================

-- 统计各表数据量
SELECT '=== 数据统计 ===' AS 统计类型, '' AS 表名, '' AS 记录数;
SELECT '部门数据' AS 统计类型, 'SYS_DEPARTMENT' AS 表名, CAST(COUNT(*) AS VARCHAR(10)) AS 记录数 FROM SYS_DEPARTMENT
UNION ALL
SELECT '角色数据', 'SYS_ROLE', CAST(COUNT(*) AS VARCHAR(10)) FROM SYS_ROLE
UNION ALL
SELECT '用户数据', 'SYS_USER', CAST(COUNT(*) AS VARCHAR(10)) FROM SYS_USER
UNION ALL
SELECT '用户角色关联', 'SYS_USER_ROLE', CAST(COUNT(*) AS VARCHAR(10)) FROM SYS_USER_ROLE
UNION ALL
SELECT '公文类型', 'SYS_DOC_TYPE', CAST(COUNT(*) AS VARCHAR(10)) FROM SYS_DOC_TYPE
UNION ALL
SELECT '系统配置', 'SYS_CONFIG', CAST(COUNT(*) AS VARCHAR(10)) FROM SYS_CONFIG
UNION ALL
SELECT '流程配置', 'SYS_FLOW_CONFIG', CAST(COUNT(*) AS VARCHAR(10)) FROM SYS_FLOW_CONFIG
UNION ALL
SELECT '公文数据', 'DOC_DOCUMENT', CAST(COUNT(*) AS VARCHAR(10)) FROM DOC_DOCUMENT
UNION ALL
SELECT '流转记录', 'DOC_FLOW_RECORD', CAST(COUNT(*) AS VARCHAR(10)) FROM DOC_FLOW_RECORD
UNION ALL
SELECT '附件数据', 'DOC_ATTACHMENT', CAST(COUNT(*) AS VARCHAR(10)) FROM DOC_ATTACHMENT
UNION ALL
SELECT '签收记录', 'DOC_RECEIVE_RECORD', CAST(COUNT(*) AS VARCHAR(10)) FROM DOC_RECEIVE_RECORD;

-- 显示部门用户分布
SELECT '=== 部门用户分布 ===' AS 信息类型, '' AS 部门名称, '' AS 用户数量, '' AS 用户列表;
SELECT
    '部门分布' AS 信息类型,
    d.DEPT_NAME AS 部门名称,
    CAST(COUNT(u.USER_ID) AS VARCHAR(10)) AS 用户数量,
    LISTAGG(u.REAL_NAME, ', ') WITHIN GROUP (ORDER BY u.REAL_NAME) AS 用户列表
FROM SYS_DEPARTMENT d
LEFT JOIN SYS_USER u ON d.DEPT_ID = u.DEPARTMENT_ID AND u.STATUS = '1'
WHERE d.STATUS = '1'
GROUP BY d.DEPT_ID, d.DEPT_NAME
ORDER BY d.ORDER_NUM;

-- 显示测试账号信息
SELECT '=== 测试账号信息 ===' AS 提示, '' AS 用户名, '' AS 密码, '' AS 角色, '' AS 部门, '' AS 功能权限;
SELECT
    '测试账号' AS 提示,
    u.USERNAME AS 用户名,
    '123456' AS 密码,
    r.ROLE_NAME AS 角色,
    d.DEPT_NAME AS 部门,
    CASE
        WHEN r.ROLE_KEY = 'ADMIN' THEN '用户管理、角色管理、流程管理'
        WHEN r.ROLE_KEY = 'DIRECTOR' THEN '审签公文、浏览所有公文、查询公文'
        WHEN r.ROLE_KEY LIKE 'VICE_DIRECTOR%' THEN '审核公文、浏览所有公文、查询公文'
        WHEN r.ROLE_KEY = 'OFFICE_DIRECTOR' THEN '修改公文、公文流转、删除公文、发送公文'
        WHEN r.ROLE_KEY IN ('DEPT_MANAGER', 'EMPLOYEE') THEN '公文拟制、签收公文、浏览公文'
        ELSE '未定义权限'
    END AS 功能权限
FROM SYS_USER u
JOIN SYS_USER_ROLE ur ON u.USER_ID = ur.USER_ID
JOIN SYS_ROLE r ON ur.ROLE_ID = r.ROLE_ID
JOIN SYS_DEPARTMENT d ON u.DEPARTMENT_ID = d.DEPT_ID
WHERE u.STATUS = '1'
ORDER BY r.ROLE_SORT, u.USERNAME;

-- 显示公文流转状态
SELECT '=== 公文流转状态 ===' AS 信息, '' AS 公文标题, '' AS 状态, '' AS 当前处理人, '' AS 当前步骤;
SELECT
    '公文状态' AS 信息,
    d.DOC_TITLE AS 公文标题,
    CASE d.DOC_STATUS
        WHEN 'DRAFT' THEN '草稿'
        WHEN 'PROCESSING' THEN '流转中'
        WHEN 'APPROVED' THEN '已批准'
        WHEN 'REJECTED' THEN '已退回'
        WHEN 'COMPLETED' THEN '已完成'
        ELSE d.DOC_STATUS
    END AS 状态,
    COALESCE(u.REAL_NAME, '无') AS 当前处理人,
    CASE d.CURRENT_STEP
        WHEN 'DRAFT' THEN '草稿'
        WHEN 'OFFICE_EDIT' THEN '办公室修改'
        WHEN 'VICE_REVIEW' THEN '副厂长审核'
        WHEN 'DIRECTOR_APPROVE' THEN '厂长审签'
        WHEN 'OFFICE_SEND' THEN '办公室发送'
        WHEN 'DEPT_RECEIVE' THEN '部门签收'
        ELSE COALESCE(d.CURRENT_STEP, '无')
    END AS 当前步骤
FROM DOC_DOCUMENT d
LEFT JOIN SYS_USER u ON d.CURRENT_HANDLER = u.USER_ID
ORDER BY d.CREATE_TIME DESC;

-- 完成提示
SELECT '========================================' AS 完成信息;
SELECT '河北金力集团公文流转系统测试数据生成完成！' AS 完成信息;
SELECT '========================================' AS 完成信息;
SELECT '✓ 严格按照需求文档精简用户配置' AS 完成信息;
SELECT '✓ 共5个部门，12个用户，符合要求' AS 完成信息;
SELECT '✓ 包含完整的公文流转测试数据' AS 完成信息;
SELECT '✓ 支持达梦8数据库语法' AS 完成信息;
SELECT '✓ 所有测试账号密码均为：123456' AS 完成信息;
SELECT '========================================' AS 完成信息;

COMMIT;
