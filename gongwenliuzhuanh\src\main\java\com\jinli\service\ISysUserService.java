package com.jinli.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jinli.dto.UserQueryDTO;
import com.jinli.entity.SysUser;

import java.util.List;
import java.util.Map;

/**
 * 用户服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-30
 */
public interface ISysUserService extends IService<SysUser> {

    /**
     * 查询用户列表（分页）
     * 
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    Map<String, Object> selectUserPage(UserQueryDTO queryDTO);

    /**
     * 查询用户列表
     * 
     * @param queryDTO 查询条件
     * @return 用户列表
     */
    List<SysUser> selectUserList(UserQueryDTO queryDTO);

    /**
     * 根据用户ID查询用户详情
     * 
     * @param userId 用户ID
     * @return 用户信息
     */
    SysUser selectUserById(String userId);

    /**
     * 根据用户名查询用户
     * 
     * @param username 用户名
     * @return 用户信息
     */
    SysUser selectUserByUsername(String username);

    /**
     * 新增用户
     * 
     * @param user 用户信息
     * @return 操作结果
     */
    boolean insertUser(SysUser user);

    /**
     * 更新用户
     * 
     * @param user 用户信息
     * @return 操作结果
     */
    boolean updateUser(SysUser user);

    /**
     * 删除用户
     * 
     * @param userId 用户ID
     * @return 操作结果
     */
    boolean deleteUser(String userId);

    /**
     * 批量删除用户
     * 
     * @param userIds 用户ID列表
     * @return 操作结果
     */
    boolean batchDeleteUsers(List<String> userIds);

    /**
     * 更新用户状态
     * 
     * @param userId 用户ID
     * @param status 状态
     * @return 操作结果
     */
    boolean updateUserStatus(String userId, String status);

    /**
     * 重置用户密码
     * 
     * @param userId 用户ID
     * @param password 新密码
     * @return 操作结果
     */
    boolean resetUserPassword(String userId, String password);

    /**
     * 检查用户名是否存在
     * 
     * @param username 用户名
     * @param excludeUserId 排除的用户ID
     * @return 是否存在
     */
    boolean checkUsernameExists(String username, String excludeUserId);

    /**
     * 检查邮箱是否存在
     * 
     * @param email 邮箱
     * @param excludeUserId 排除的用户ID
     * @return 是否存在
     */
    boolean checkEmailExists(String email, String excludeUserId);

    /**
     * 检查手机号是否存在
     * 
     * @param phone 手机号
     * @param excludeUserId 排除的用户ID
     * @return 是否存在
     */
    boolean checkPhoneExists(String phone, String excludeUserId);

    /**
     * 更新用户登录信息
     * 
     * @param userId 用户ID
     * @param loginIp 登录IP
     * @return 操作结果
     */
    boolean updateUserLoginInfo(String userId, String loginIp);

    /**
     * 查询部门下的用户列表
     * 
     * @param departmentId 部门ID
     * @return 用户列表
     */
    List<SysUser> selectUsersByDepartmentId(String departmentId);

    /**
     * 查询角色下的用户列表
     * 
     * @param roleId 角色ID
     * @return 用户列表
     */
    List<SysUser> selectUsersByRoleId(String roleId);

    /**
     * 分配用户角色
     * 
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 操作结果
     */
    boolean assignUserRoles(String userId, List<String> roleIds);

    /**
     * 查询用户的角色列表
     * 
     * @param userId 用户ID
     * @return 角色ID列表
     */
    List<String> selectUserRoleIds(String userId);

    /**
     * 查询用户的权限列表
     * 
     * @param userId 用户ID
     * @return 权限列表
     */
    List<String> selectUserPermissions(String userId);

    /**
     * 查询启用的用户列表
     * 
     * @return 启用的用户列表
     */
    List<SysUser> selectEnabledUsers();

    /**
     * 查询用户统计信息
     * 
     * @return 统计信息
     */
    List<SysUser> selectUserStatistics();

    /**
     * 验证用户数据
     * 
     * @param user 用户信息
     * @return 验证结果
     */
    String validateUser(SysUser user);

    /**
     * 检查是否可以删除用户
     * 
     * @param userId 用户ID
     * @return 检查结果
     */
    String checkCanDeleteUser(String userId);

    /**
     * 获取用户的完整信息（包含角色和权限）
     * 
     * @param userId 用户ID
     * @return 用户完整信息
     */
    SysUser getUserFullInfo(String userId);
}
