<template>
  <div class="employee-dashboard">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon pending-icon">
              <i class="el-icon-message"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ dashboardData.pendingReceiveCount || 0 }}</div>
              <div class="stats-label">待签收</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon received-icon">
              <i class="el-icon-circle-check"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ dashboardData.receivedCount || 0 }}</div>
              <div class="stats-label">已签收</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon assist-icon">
              <i class="el-icon-edit-outline"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ dashboardData.assistedCount || 0 }}</div>
              <div class="stats-label">协助创建</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon notification-icon">
              <i class="el-icon-bell"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ dashboardData.unreadNotificationCount || 0 }}</div>
              <div class="stats-label">未读通知</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快捷操作和月度统计 -->
    <el-row :gutter="20" class="actions-row">
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="action-card">
          <div slot="header" class="card-header">
            <span>快捷操作</span>
          </div>
          <div class="action-buttons">
            <el-button type="primary" @click="goToPendingReceive">
              <i class="el-icon-message"></i> 待签收公文
            </el-button>
            <el-button type="success" @click="goToAssistDraft">
              <i class="el-icon-edit"></i> 协助拟制
            </el-button>
            <el-button type="warning" @click="goToNotifications">
              <i class="el-icon-bell"></i> 查看通知
            </el-button>
            <el-button type="info" @click="goToReceivedDocuments">
              <i class="el-icon-folder"></i> 已签收公文
            </el-button>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="monthly-card">
          <div slot="header" class="card-header">
            <span>本月工作统计</span>
          </div>
          <div class="monthly-content" v-if="dashboardData.monthlyWork">
            <div class="monthly-item">
              <div class="monthly-label">签收数量</div>
              <div class="monthly-value">
                {{ dashboardData.monthlyWork.receiveCount || 0 }} 份
              </div>
            </div>
            <div class="monthly-item">
              <div class="monthly-label">协助数量</div>
              <div class="monthly-value">
                {{ dashboardData.monthlyWork.assistCount || 0 }} 份
              </div>
            </div>
            <div class="monthly-item">
              <div class="monthly-label">完成率</div>
              <div class="monthly-value">
                {{ ((dashboardData.monthlyWork.completionRate || 0) * 100).toFixed(1) }}%
              </div>
            </div>
            <div class="monthly-item">
              <div class="monthly-label">及时率</div>
              <div class="monthly-value">
                {{ ((dashboardData.monthlyWork.timelyRate || 0) * 100).toFixed(1) }}%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 待办事项和工作效率 -->
    <el-row :gutter="20" class="work-row">
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="todo-card">
          <div slot="header" class="card-header">
            <span>待办事项</span>
            <el-badge :value="dashboardData.todoList ? dashboardData.todoList.length : 0" class="item">
              <el-button type="text" @click="goToTodoList">查看全部</el-button>
            </el-badge>
          </div>
          <div class="todo-list">
            <div
              v-for="todo in dashboardData.todoList"
              :key="todo.itemId"
              class="todo-item"
              @click="handleTodoItem(todo)"
            >
              <div class="todo-header">
                <div class="todo-title">{{ todo.itemTitle }}</div>
                <el-tag :type="getPriorityType(todo.priority)" size="mini">
                  {{ getPriorityLabel(todo.priority) }}
                </el-tag>
              </div>
              <div class="todo-description">{{ todo.itemDescription }}</div>
              <div class="todo-meta">
                <span class="todo-type">{{ getItemTypeLabel(todo.itemType) }}</span>
                <span class="todo-time">{{ todo.createTime }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="efficiency-card">
          <div slot="header" class="card-header">
            <span>个人工作效率</span>
          </div>
          <div class="efficiency-content" v-if="dashboardData.workEfficiency">
            <div class="efficiency-score">
              <div class="score-label">效率评分</div>
              <div class="score-value">
                <el-rate
                  :value="dashboardData.workEfficiency.efficiencyScore / 20"
                  disabled
                  show-score
                  text-color="#ff9900"
                />
              </div>
            </div>
            <div class="efficiency-stats">
              <div class="stat-item">
                <span class="stat-label">平均处理时间</span>
                <span class="stat-value">{{ dashboardData.workEfficiency.avgProcessTime || 0 }}小时</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">本月排名</span>
                <span class="stat-value">第{{ dashboardData.workEfficiency.monthlyRanking || 0 }}名</span>
              </div>
            </div>
            <div class="efficiency-improvements" v-if="dashboardData.workEfficiency.improvements">
              <div class="improvements-title">改进建议</div>
              <ul class="improvements-list">
                <li v-for="(improvement, index) in dashboardData.workEfficiency.improvements" :key="index">
                  {{ improvement }}
                </li>
              </ul>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最近通知和最近签收 -->
    <el-row :gutter="20" class="recent-row">
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="recent-card">
          <div slot="header" class="card-header">
            <span>最近通知</span>
            <el-button type="text" @click="goToNotifications">查看更多</el-button>
          </div>
          <div class="notification-list">
            <div
              v-for="notification in dashboardData.recentNotifications"
              :key="notification.notificationId"
              class="notification-item"
              :class="{ 'unread': !notification.isRead }"
              @click="handleNotification(notification)"
            >
              <div class="notification-header">
                <div class="notification-title">{{ notification.notificationTitle }}</div>
                <div class="notification-time">{{ notification.sendTime }}</div>
              </div>
              <div class="notification-content">{{ notification.notificationContent }}</div>
              <div class="notification-type">
                <el-tag size="mini">{{ getNotificationTypeLabel(notification.notificationType) }}</el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="recent-card">
          <div slot="header" class="card-header">
            <span>最近签收</span>
            <el-button type="text" @click="goToReceivedDocuments">查看更多</el-button>
          </div>
          <div class="document-list">
            <div
              v-for="doc in dashboardData.recentReceived"
              :key="doc.docId"
              class="document-item"
              @click="viewDocument(doc)"
            >
              <div class="document-header">
                <div class="document-title">{{ doc.docTitle }}</div>
                <el-tag size="mini">{{ getDocTypeLabel(doc.docType) }}</el-tag>
              </div>
              <div class="document-meta">
                <span class="document-dept">{{ doc.sendDeptName }}</span>
                <span class="document-time">{{ doc.receiveTime }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getEmployeeDashboard } from '@/api/employee/dashboard'

export default {
  name: 'EmployeeDashboard',
  data() {
    return {
      dashboardData: {
        pendingReceiveCount: 0,
        receivedCount: 0,
        assistedCount: 0,
        unreadNotificationCount: 0,
        monthlyWork: null,
        todoList: [],
        recentNotifications: [],
        recentReceived: [],
        workEfficiency: null
      }
    }
  },
  mounted() {
    this.initData()
    // 定时刷新数据
    this.timer = setInterval(() => {
      this.refreshData()
    }, 60000) // 每分钟刷新一次
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  methods: {
    // 初始化数据
    async initData() {
      await this.getDashboardData()
    },

    // 获取仪表板数据
    async getDashboardData() {
      try {
        const response = await getEmployeeDashboard()
        this.dashboardData = response.data
      } catch (error) {
        this.$message.error('获取工作台数据失败')
      }
    },

    // 刷新数据
    refreshData() {
      this.getDashboardData()
    },

    // 获取优先级类型
    getPriorityType(priority) {
      const typeMap = {
        'HIGH': 'danger',
        'NORMAL': 'warning',
        'LOW': 'info'
      }
      return typeMap[priority] || 'info'
    },

    // 获取优先级标签
    getPriorityLabel(priority) {
      const labelMap = {
        'HIGH': '高',
        'NORMAL': '中',
        'LOW': '低'
      }
      return labelMap[priority] || '中'
    },

    // 获取事项类型标签
    getItemTypeLabel(type) {
      const typeMap = {
        'RECEIVE': '签收',
        'ASSIST': '协助',
        'NOTIFICATION': '通知'
      }
      return typeMap[type] || type
    },

    // 获取通知类型标签
    getNotificationTypeLabel(type) {
      const typeMap = {
        'DOCUMENT': '公文通知',
        'SYSTEM': '系统通知',
        'REMINDER': '提醒通知'
      }
      return typeMap[type] || type
    },

    // 获取公文类型标签
    getDocTypeLabel(type) {
      const typeMap = {
        'NOTICE': '通知',
        'REPORT': '报告',
        'REQUEST': '请示',
        'DECISION': '决定',
        'OPINION': '意见',
        'LETTER': '函',
        'MINUTES': '会议纪要'
      }
      return typeMap[type] || type
    },

    // 导航到待签收公文
    goToPendingReceive() {
      this.$router.push('/employee/pending-receive')
    },

    // 导航到协助拟制
    goToAssistDraft() {
      this.$router.push('/employee/assist-draft')
    },

    // 导航到通知
    goToNotifications() {
      this.$router.push('/employee/notifications')
    },

    // 导航到已签收公文
    goToReceivedDocuments() {
      this.$router.push('/employee/received-documents')
    },

    // 导航到待办事项
    goToTodoList() {
      this.$router.push('/employee/todo-list')
    },

    // 处理待办事项
    handleTodoItem(todo) {
      if (todo.itemType === 'RECEIVE') {
        this.$router.push(`/employee/receive/${todo.itemId}`)
      } else if (todo.itemType === 'ASSIST') {
        this.$router.push(`/employee/assist/${todo.itemId}`)
      } else {
        this.handleNotification(todo)
      }
    },

    // 处理通知
    handleNotification(notification) {
      if (notification.relatedDocId) {
        this.$router.push(`/employee/document/${notification.relatedDocId}`)
      }
      // 标记为已读
      if (!notification.isRead) {
        this.markNotificationAsRead(notification.notificationId)
      }
    },

    // 标记通知为已读
    async markNotificationAsRead(notificationId) {
      try {
        await this.$api.employee.markNotificationAsRead(notificationId)
        this.refreshData()
      } catch (error) {
        console.error('标记通知已读失败', error)
      }
    },

    // 查看公文
    viewDocument(doc) {
      this.$router.push(`/employee/document/${doc.docId}`)
    }
  }
}
</script>

<style scoped>
.employee-dashboard {
  padding: 20px;
}

.stats-row,
.actions-row,
.work-row,
.recent-row {
  margin-bottom: 20px;
}

.stats-card {
  height: 120px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: white;
}

.pending-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.received-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.assist-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.notification-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.action-card,
.monthly-card {
  height: 250px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.action-buttons .el-button {
  flex: 1;
  min-width: 120px;
}

.monthly-content {
  padding: 10px 0;
}

.monthly-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.monthly-label {
  font-size: 14px;
  color: #606266;
}

.monthly-value {
  font-weight: bold;
  color: #303133;
}

.todo-card,
.efficiency-card {
  height: 350px;
}

.todo-list {
  max-height: 270px;
  overflow-y: auto;
}

.todo-item {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s;
}

.todo-item:hover {
  background-color: #f8f9fa;
}

.todo-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.todo-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  flex: 1;
  margin-right: 10px;
}

.todo-description {
  font-size: 12px;
  color: #606266;
  margin-bottom: 5px;
}

.todo-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
}

.efficiency-content {
  padding: 10px 0;
}

.efficiency-score {
  text-align: center;
  margin-bottom: 20px;
}

.score-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
}

.efficiency-stats {
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.stat-value {
  font-weight: bold;
  color: #303133;
}

.improvements-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 10px;
}

.improvements-list {
  font-size: 12px;
  color: #606266;
  padding-left: 15px;
}

.recent-card {
  height: 350px;
}

.notification-list,
.document-list {
  max-height: 270px;
  overflow-y: auto;
}

.notification-item,
.document-item {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s;
}

.notification-item:hover,
.document-item:hover {
  background-color: #f8f9fa;
}

.notification-item.unread {
  background-color: #f0f9ff;
  border-left: 3px solid #409EFF;
}

.notification-header,
.document-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.notification-title,
.document-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  flex: 1;
  margin-right: 10px;
}

.notification-content {
  font-size: 12px;
  color: #606266;
  margin-bottom: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.notification-time,
.document-time {
  font-size: 12px;
  color: #909399;
}

.document-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .employee-dashboard {
    padding: 10px;
  }
  
  .stats-card {
    margin-bottom: 10px;
  }
  
  .action-card,
  .monthly-card,
  .todo-card,
  .efficiency-card,
  .recent-card {
    margin-bottom: 10px;
  }
}

@media (max-width: 800px) {
  .stats-content {
    flex-direction: column;
    text-align: center;
  }
  
  .stats-icon {
    margin-right: 0;
    margin-bottom: 10px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-buttons .el-button {
    width: 100%;
  }
  
  .monthly-item,
  .stat-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .monthly-value,
  .stat-value {
    margin-top: 5px;
  }
}
</style>
