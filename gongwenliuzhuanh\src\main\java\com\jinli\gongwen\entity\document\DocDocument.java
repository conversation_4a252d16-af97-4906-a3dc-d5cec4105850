package com.jinli.gongwen.entity.document;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 公文实体类
 */
@Data
@TableName("doc_document")
public class DocDocument {

    @TableId(type = IdType.ASSIGN_ID)
    private String docId;

    private String docTitle;

    private String docNumber;

    private String docType;

    private String docLevel;

    private String docContent;

    private String docStatus;

    private String createUserId;

    private String createUserName;

    private String createDeptId;

    private String createDeptName;

    private String currentHandler;

    private String currentHandlerName;

    private String currentStep;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String deadline;

    private String remark;

    private String targetDeptId;

    private String receiveStatus;

    private String reviewResult;

    private String approvalResult;
}
