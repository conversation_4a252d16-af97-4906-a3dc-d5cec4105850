import { defineStore } from 'pinia'
import { ref } from 'vue'
import { login, getUserInfo } from '@/api/auth'
import { getToken, setToken, removeToken } from '@/utils/auth'
import { ElMessage } from 'element-plus'

export const useUserStore = defineStore('user', () => {
  const token = ref(getToken())
  const userInfo = ref(null)
  const permissions = ref([])

  // 登录
  const userLogin = async (loginForm) => {
    try {
      const response = await login(loginForm)
      if (response.code === 200) {
        const { token: userToken, userInfo: info } = response.data
        token.value = userToken
        userInfo.value = info
        setToken(userToken)
        ElMessage.success('登录成功')
        return Promise.resolve(response)
      } else {
        ElMessage.error(response.message || '登录失败')
        return Promise.reject(response)
      }
    } catch (error) {
      ElMessage.error('登录失败，请检查网络连接')
      return Promise.reject(error)
    }
  }

  // 获取用户信息
  const getUserInfoAction = async () => {
    try {
      if (!userInfo.value && token.value) {
        const response = await getUserInfo(userInfo.value?.userId)
        if (response.code === 200) {
          userInfo.value = response.data
          // 根据角色设置权限
          setPermissions(response.data.roleKey)
        }
      }
      return userInfo.value
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    }
  }

  // 设置权限
  const setPermissions = (roleKey) => {
    const rolePermissions = {
      'ADMIN': [
        'system:user:view', 'system:user:add', 'system:user:edit', 'system:user:delete',
        'system:role:view', 'system:role:add', 'system:role:edit', 'system:role:delete',
        'system:dept:view', 'system:dept:add', 'system:dept:edit', 'system:dept:delete',
        'system:log:view',
        'document:create', 'document:edit', 'document:delete', 'document:view',
        'document:submit', 'document:review', 'document:send', 'document:receive'
      ],
      'DIRECTOR': [
        'document:create', 'document:edit', 'document:view',
        'document:submit', 'document:review', 'document:send', 'document:receive'
      ],
      'VICE_DIRECTOR_PROD': [
        'document:create', 'document:edit', 'document:view',
        'document:submit', 'document:review', 'document:send', 'document:receive'
      ],
      'VICE_DIRECTOR_SALES': [
        'document:create', 'document:edit', 'document:view',
        'document:submit', 'document:review', 'document:send', 'document:receive'
      ],
      'VICE_DIRECTOR_FINANCE': [
        'document:create', 'document:edit', 'document:view',
        'document:submit', 'document:review', 'document:send', 'document:receive'
      ],
      'OFFICE': [
        'document:create', 'document:edit', 'document:view',
        'document:submit', 'document:send', 'document:receive'
      ],
      'DEPARTMENT': [
        'document:create', 'document:edit', 'document:view',
        'document:submit', 'document:receive'
      ]
    }
    permissions.value = rolePermissions[roleKey] || []
  }

  // 检查权限
  const hasPermission = (permission) => {
    return permissions.value.includes(permission)
  }

  // 检查角色
  const hasRole = (role) => {
    return userInfo.value?.roleKey === role
  }

  // 登出
  const logout = () => {
    token.value = ''
    userInfo.value = null
    permissions.value = []
    removeToken()
  }

  // 重置状态
  const reset = () => {
    token.value = ''
    userInfo.value = null
    permissions.value = []
  }

  return {
    token,
    userInfo,
    permissions,
    userLogin,
    getUserInfo: getUserInfoAction,
    hasPermission,
    hasRole,
    logout,
    reset
  }
})
