package com.jinli.gongwen.dto.system;

import com.jinli.gongwen.common.BaseQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 流程查询数据传输对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("流程查询数据传输对象")
public class FlowQueryDTO extends BaseQueryDTO {

    @ApiModelProperty("流程名称")
    private String flowName;

    @ApiModelProperty("是否启用")
    private Boolean isEnabled;
}
