package com.jinli.gongwen.controller;

import com.jinli.gongwen.common.Result;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * CORS测试控制器
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
@RestController
@RequestMapping("/cors-test")
public class CorsTestController {

    /**
     * 简单的GET测试
     */
    @GetMapping("/hello")
    public Result<String> hello() {
        return Result.success("Hello from backend!", "CORS test successful");
    }

    /**
     * POST测试
     */
    @PostMapping("/echo")
    public Result<Map<String, Object>> echo(@RequestBody Map<String, Object> request) {
        Map<String, Object> response = new HashMap<>();
        response.put("message", "Echo from backend");
        response.put("received", request);
        response.put("timestamp", System.currentTimeMillis());
        return Result.success("Echo successful", response);
    }

    /**
     * OPTIONS处理
     */
    @RequestMapping(value = "/**", method = RequestMethod.OPTIONS)
    public Result<String> options() {
        return Result.success("OPTIONS OK");
    }
}
