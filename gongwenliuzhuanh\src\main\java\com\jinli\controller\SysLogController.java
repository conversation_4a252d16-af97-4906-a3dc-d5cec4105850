package com.jinli.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinli.common.Result;
import com.jinli.entity.SysLog;
import com.jinli.service.ISysLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 系统日志控制器
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
@Api(tags = "系统日志管理")
@RestController
@RequestMapping("/system/log")
public class SysLogController {

    private static final Logger log = LoggerFactory.getLogger(SysLogController.class);

    @Autowired
    private ISysLogService logService;

    /**
     * 分页查询系统日志
     */
    @ApiOperation("分页查询系统日志")
    @GetMapping("/list")
    public Result<Map<String, Object>> list(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam("用户名") @RequestParam(required = false) String username,
            @ApiParam("操作模块") @RequestParam(required = false) String module,
            @ApiParam("操作类型") @RequestParam(required = false) String operationType,
            @ApiParam("操作状态") @RequestParam(required = false) String status,
            @ApiParam("开始时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @ApiParam("结束时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        
        log.info("分页查询系统日志，页码：{}，每页大小：{}，用户名：{}，模块：{}，操作类型：{}，状态：{}，开始时间：{}，结束时间：{}", 
                pageNum, pageSize, username, module, operationType, status, startTime, endTime);
        
        try {
            Map<String, Object> result = logService.selectLogPage(pageNum, pageSize, username, module, 
                    operationType, status, startTime, endTime);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询系统日志失败", e);
            return Result.error("查询系统日志失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID查询日志详情
     */
    @ApiOperation("查询日志详情")
    @GetMapping("/{logId}")
    public Result<SysLog> getById(@ApiParam("日志ID") @PathVariable String logId) {
        log.info("查询日志详情，日志ID：{}", logId);
        
        try {
            SysLog sysLog = logService.getById(logId);
            if (sysLog != null) {
                return Result.success(sysLog);
            } else {
                return Result.error("日志不存在");
            }
        } catch (Exception e) {
            log.error("查询日志详情失败", e);
            return Result.error("查询日志详情失败：" + e.getMessage());
        }
    }

    /**
     * 删除日志
     */
    @ApiOperation("删除日志")
    @DeleteMapping("/{logId}")
    public Result<Void> delete(@ApiParam("日志ID") @PathVariable String logId) {
        log.info("删除日志，日志ID：{}", logId);
        
        try {
            boolean success = logService.removeById(logId);
            if (success) {
                return Result.success();
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除日志失败", e);
            return Result.error("删除日志失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除日志
     */
    @ApiOperation("批量删除日志")
    @DeleteMapping("/batch")
    public Result<Void> batchDelete(@RequestBody Map<String, List<String>> request) {
        List<String> logIds = request.get("logIds");
        log.info("批量删除日志，日志IDs：{}", logIds);
        
        try {
            boolean success = logService.removeByIds(logIds);
            if (success) {
                return Result.success();
            } else {
                return Result.error("批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除日志失败", e);
            return Result.error("批量删除日志失败：" + e.getMessage());
        }
    }

    /**
     * 清空所有日志
     */
    @ApiOperation("清空所有日志")
    @DeleteMapping("/clear")
    public Result<Void> clearAll() {
        log.info("清空所有系统日志");
        
        try {
            boolean success = logService.clearAllLogs();
            if (success) {
                return Result.success();
            } else {
                return Result.error("清空日志失败");
            }
        } catch (Exception e) {
            log.error("清空日志失败", e);
            return Result.error("清空日志失败：" + e.getMessage());
        }
    }

    /**
     * 导出日志
     */
    @ApiOperation("导出日志")
    @GetMapping("/export")
    public ResponseEntity<byte[]> export(
            @ApiParam("用户名") @RequestParam(required = false) String username,
            @ApiParam("操作模块") @RequestParam(required = false) String module,
            @ApiParam("操作类型") @RequestParam(required = false) String operationType,
            @ApiParam("操作状态") @RequestParam(required = false) String status,
            @ApiParam("开始时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @ApiParam("结束时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            HttpServletResponse response) {
        
        log.info("导出系统日志，用户名：{}，模块：{}，操作类型：{}，状态：{}，开始时间：{}，结束时间：{}", 
                username, module, operationType, status, startTime, endTime);
        
        try {
            byte[] excelData = logService.exportLogs(username, module, operationType, status, startTime, endTime);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", "system_logs.xlsx");
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(excelData);
        } catch (Exception e) {
            log.error("导出日志失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取日志统计信息
     */
    @ApiOperation("获取日志统计信息")
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getStatistics() {
        log.info("获取日志统计信息");
        
        try {
            Map<String, Object> statistics = logService.getLogStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取日志统计信息失败", e);
            return Result.error("获取日志统计信息失败：" + e.getMessage());
        }
    }

    /**
     * 获取操作模块列表
     */
    @ApiOperation("获取操作模块列表")
    @GetMapping("/modules")
    public Result<List<Map<String, String>>> getModules() {
        try {
            List<Map<String, String>> modules = logService.getLogModules();
            return Result.success(modules);
        } catch (Exception e) {
            log.error("获取操作模块列表失败", e);
            return Result.error("获取操作模块列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取操作类型列表
     */
    @ApiOperation("获取操作类型列表")
    @GetMapping("/operation-types")
    public Result<List<Map<String, String>>> getOperationTypes() {
        try {
            List<Map<String, String>> operationTypes = logService.getLogOperationTypes();
            return Result.success(operationTypes);
        } catch (Exception e) {
            log.error("获取操作类型列表失败", e);
            return Result.error("获取操作类型列表失败：" + e.getMessage());
        }
    }
}
