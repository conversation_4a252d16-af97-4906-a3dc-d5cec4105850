<template>
  <div class="document-browse">
    <el-card class="content-card">
      <template #header>
        <div class="card-header">
          <span>浏览公文</span>
          <div class="header-actions">
            <el-button @click="exportDocuments" :loading="exporting">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
            <el-button @click="refreshList" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-form :model="filterForm" inline>
          <el-form-item label="公文标题">
            <el-input 
              v-model="filterForm.docTitle" 
              placeholder="请输入公文标题"
              style="width: 200px"
              clearable
            />
          </el-form-item>
          <el-form-item label="公文类型">
            <el-select v-model="filterForm.docType" placeholder="请选择" clearable style="width: 150px">
              <el-option label="通知" value="NOTICE" />
              <el-option label="通报" value="BULLETIN" />
              <el-option label="报告" value="REPORT" />
              <el-option label="请示" value="REQUEST" />
              <el-option label="批复" value="REPLY" />
            </el-select>
          </el-form-item>
          <el-form-item label="公文状态">
            <el-select v-model="filterForm.docStatus" placeholder="请选择" clearable style="width: 150px">
              <el-option label="已完成" value="COMPLETED" />
              <el-option label="已批准" value="APPROVED" />
              <el-option label="已退回" value="REJECTED" />
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="filterForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 240px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchDocuments">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetFilter">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 统计信息 -->
      <div class="stats-section">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-number">{{ statistics.totalCount }}</div>
              <div class="stat-label">总公文数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-number">{{ statistics.completedCount }}</div>
              <div class="stat-label">已完成</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-number">{{ statistics.approvedCount }}</div>
              <div class="stat-label">已批准</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-number">{{ statistics.rejectedCount }}</div>
              <div class="stat-label">已退回</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 公文列表 -->
      <el-table :data="documentList" style="width: 100%" v-loading="loading">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="docTitle" label="公文标题" min-width="200" show-overflow-tooltip />
        <el-table-column prop="docType" label="类型" width="100" />
        <el-table-column prop="docLevel" label="紧急程度" width="100">
          <template #default="{ row }">
            <el-tag :type="getLevelType(row.docLevel)" size="small">
              {{ row.docLevel }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="docStatus" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.docStatus)" size="small">
              {{ getStatusText(row.docStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createUserName" label="发起人" width="120" />
        <el-table-column prop="createDeptName" label="发起部门" width="120" />
        <el-table-column prop="processTime" label="处理时间" width="150" />
        <el-table-column prop="createTime" label="创建时间" width="150" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="viewDocument(row)">
              查看
            </el-button>
            <el-button type="text" size="small" @click="viewHistory(row)">
              流转记录
            </el-button>
            <el-button type="text" size="small" @click="downloadDocument(row)">
              下载
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 公文详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      :title="currentDoc?.docTitle"
      width="900px"
      :close-on-click-modal="false"
    >
      <div class="document-detail">
        <!-- 公文信息 -->
        <el-descriptions :column="3" border>
          <el-descriptions-item label="公文编号">{{ currentDoc?.docNumber }}</el-descriptions-item>
          <el-descriptions-item label="公文类型">{{ currentDoc?.docType }}</el-descriptions-item>
          <el-descriptions-item label="紧急程度">
            <el-tag :type="getLevelType(currentDoc?.docLevel)" size="small">
              {{ currentDoc?.docLevel }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="发起人">{{ currentDoc?.createUserName }}</el-descriptions-item>
          <el-descriptions-item label="发起部门">{{ currentDoc?.createDeptName }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ currentDoc?.createTime }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(currentDoc?.docStatus)" size="small">
              {{ getStatusText(currentDoc?.docStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="处理时间">{{ currentDoc?.processTime }}</el-descriptions-item>
          <el-descriptions-item label="办理期限">{{ currentDoc?.deadline }}</el-descriptions-item>
        </el-descriptions>

        <!-- 公文内容 -->
        <div class="doc-content-section">
          <h4>公文内容</h4>
          <div class="doc-content">
            {{ currentDoc?.docContent }}
          </div>
        </div>

        <!-- 附件列表 -->
        <div class="attachments-section" v-if="currentDoc?.attachments?.length">
          <h4>附件列表</h4>
          <el-table :data="currentDoc.attachments" size="small">
            <el-table-column prop="fileName" label="文件名" />
            <el-table-column prop="fileSize" label="文件大小" width="100" />
            <el-table-column prop="uploadTime" label="上传时间" width="150" />
            <el-table-column label="操作" width="100">
              <template #default="{ row }">
                <el-button type="text" size="small" @click="downloadAttachment(row)">
                  下载
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 我的处理意见 -->
        <div class="my-opinion-section" v-if="currentDoc?.myOpinion">
          <h4>我的处理意见</h4>
          <div class="opinion-content">
            <p><strong>处理结果：</strong>{{ getActionText(currentDoc.myOpinion.action) }}</p>
            <p><strong>处理时间：</strong>{{ currentDoc.myOpinion.processTime }}</p>
            <p><strong>处理意见：</strong>{{ currentDoc.myOpinion.opinion }}</p>
          </div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="downloadDocument(currentDoc)">
            <el-icon><Download /></el-icon>
            下载公文
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 流转记录对话框 -->
    <el-dialog
      v-model="historyDialogVisible"
      title="流转记录"
      width="700px"
    >
      <el-timeline>
        <el-timeline-item
          v-for="record in flowHistory"
          :key="record.recordId"
          :timestamp="record.processTime"
          :type="getTimelineType(record.action)"
        >
          <div class="timeline-content">
            <h4>{{ getActionText(record.action) }}</h4>
            <p><strong>处理人：</strong>{{ record.fromUserName || '系统' }}</p>
            <p><strong>处理环节：</strong>{{ getStepText(record.flowStep) }}</p>
            <p v-if="record.opinion"><strong>处理意见：</strong>{{ record.opinion }}</p>
          </div>
        </el-timeline-item>
      </el-timeline>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Download, Refresh, Search } from '@element-plus/icons-vue'
import {
  getProcessedDocuments,
  getDocumentStatistics,
  getDocumentFlowHistory,
  downloadDocument as downloadDocumentAPI,
  downloadAttachment as downloadAttachmentAPI,
  exportDocuments as exportDocumentsAPI
} from '@/api/user/workspace'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const exporting = ref(false)
const detailDialogVisible = ref(false)
const historyDialogVisible = ref(false)

const filterForm = reactive({
  docTitle: '',
  docType: '',
  docStatus: '',
  dateRange: null
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

const statistics = reactive({
  totalCount: 0,
  completedCount: 0,
  approvedCount: 0,
  rejectedCount: 0
})

const documentList = ref([])
const currentDoc = ref(null)
const flowHistory = ref([])

// 方法
const loadDocuments = async () => {
  loading.value = true
  try {
    // 这里调用API获取已处理公文
    // const response = await getProcessedDocuments(pagination, filterForm)
    // documentList.value = response.data.records
    // pagination.total = response.data.total
    
    // 模拟数据
    documentList.value = [
      {
        docId: '1',
        docTitle: '关于加强安全生产管理的通知',
        docNumber: 'JINLI-2025-001',
        docType: '通知',
        docLevel: '紧急',
        docStatus: 'COMPLETED',
        createUserName: '张三',
        createDeptName: '生产部',
        processTime: '2025-01-30 15:30',
        createTime: '2025-01-30 10:00',
        docContent: '各部门：\n\n为进一步加强我集团安全生产管理工作...',
        myOpinion: {
          action: 'APPROVE',
          opinion: '同意执行，请各部门认真落实',
          processTime: '2025-01-30 15:30'
        }
      }
    ]
    pagination.total = 1
  } catch (error) {
    ElMessage.error('获取公文列表失败')
  } finally {
    loading.value = false
  }
}

const loadStatistics = async () => {
  try {
    // 这里调用API获取统计数据
    // const response = await getDocumentStatistics()
    // Object.assign(statistics, response.data)
    
    // 模拟数据
    statistics.totalCount = 25
    statistics.completedCount = 20
    statistics.approvedCount = 18
    statistics.rejectedCount = 2
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

const searchDocuments = () => {
  pagination.currentPage = 1
  loadDocuments()
}

const resetFilter = () => {
  Object.assign(filterForm, {
    docTitle: '',
    docType: '',
    docStatus: '',
    dateRange: null
  })
  searchDocuments()
}

const refreshList = () => {
  loadDocuments()
  loadStatistics()
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  loadDocuments()
}

const handleCurrentChange = (page) => {
  pagination.currentPage = page
  loadDocuments()
}

const viewDocument = (doc) => {
  currentDoc.value = doc
  detailDialogVisible.value = true
}

const viewHistory = async (doc) => {
  try {
    // 这里调用API获取流转记录
    // const response = await getDocumentFlowHistory(doc.docId)
    // flowHistory.value = response.data
    
    // 模拟数据
    flowHistory.value = [
      {
        recordId: '1',
        flowStep: 'DRAFT',
        action: 'SUBMIT',
        fromUserName: '张三',
        opinion: '创建公文',
        processTime: '2025-01-30 10:00'
      },
      {
        recordId: '2',
        flowStep: 'VICE_REVIEW',
        action: 'APPROVE',
        fromUserName: '李副厂长',
        opinion: '同意执行，请各部门认真落实',
        processTime: '2025-01-30 15:30'
      }
    ]
    
    historyDialogVisible.value = true
  } catch (error) {
    ElMessage.error('获取流转记录失败')
  }
}

const downloadDocument = async (doc) => {
  try {
    // 调用API下载公文
    await downloadDocumentAPI(doc.docId)

    ElMessage.success('下载开始')
  } catch (error) {
    ElMessage.error('下载失败')
  }
}

const downloadAttachment = async (attachment) => {
  try {
    // 调用API下载附件
    await downloadAttachmentAPI(attachment.attachmentId)

    ElMessage.success('下载开始')
  } catch (error) {
    ElMessage.error('下载失败')
  }
}

const exportDocuments = async () => {
  exporting.value = true
  try {
    // 调用API导出公文列表
    await exportDocumentsAPI(filterForm)
    
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  } finally {
    exporting.value = false
  }
}

const getLevelType = (level) => {
  const types = {
    '特急': 'danger',
    '紧急': 'warning',
    '普通': 'info'
  }
  return types[level] || 'info'
}

const getStatusType = (status) => {
  const types = {
    'DRAFT': 'info',
    'PROCESSING': 'warning',
    'APPROVED': 'success',
    'REJECTED': 'danger',
    'COMPLETED': 'success'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    'DRAFT': '草稿',
    'PROCESSING': '流转中',
    'APPROVED': '已批准',
    'REJECTED': '已退回',
    'COMPLETED': '已完成'
  }
  return texts[status] || status
}

const getStepText = (step) => {
  const texts = {
    'DRAFT': '草稿',
    'OFFICE_EDIT': '办公室修改',
    'VICE_REVIEW': '副厂长审核',
    'DIRECTOR_APPROVE': '厂长审签',
    'OFFICE_SEND': '办公室发送',
    'DEPT_RECEIVE': '部门签收'
  }
  return texts[step] || step
}

const getActionText = (action) => {
  const texts = {
    'SUBMIT': '提交',
    'APPROVE': '同意',
    'REJECT': '退回',
    'EDIT': '修改',
    'SEND': '发送',
    'RECEIVE': '签收',
    'TRANSFER': '转发'
  }
  return texts[action] || action
}

const getTimelineType = (action) => {
  const types = {
    'SUBMIT': 'primary',
    'APPROVE': 'success',
    'REJECT': 'danger',
    'EDIT': 'warning',
    'SEND': 'info',
    'RECEIVE': 'success',
    'TRANSFER': 'warning'
  }
  return types[action] || 'primary'
}

// 生命周期
onMounted(() => {
  loadDocuments()
  loadStatistics()
})
</script>

<style scoped>
.document-browse {
  padding: 20px;
}

.content-card {
  background: white;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.stats-section {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.document-detail {
  max-height: 600px;
  overflow-y: auto;
}

.doc-content-section,
.attachments-section,
.my-opinion-section {
  margin: 20px 0;
}

.doc-content-section h4,
.attachments-section h4,
.my-opinion-section h4 {
  margin-bottom: 10px;
  color: #303133;
}

.doc-content {
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  white-space: pre-wrap;
  max-height: 200px;
  overflow-y: auto;
}

.opinion-content {
  padding: 15px;
  background-color: #f0f9ff;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.opinion-content p {
  margin: 8px 0;
  color: #606266;
}

.timeline-content h4 {
  margin: 0 0 8px 0;
  color: #303133;
}

.timeline-content p {
  margin: 4px 0;
  color: #606266;
}
</style>
