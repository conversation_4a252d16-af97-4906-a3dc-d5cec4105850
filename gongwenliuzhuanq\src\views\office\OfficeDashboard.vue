<template>
  <div class="office-dashboard">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon pending-icon">
              <i class="el-icon-document"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ dashboardData.pendingCount || 0 }}</div>
              <div class="stats-label">待处理公文</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon today-icon">
              <i class="el-icon-date"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ dashboardData.todayProcessed || 0 }}</div>
              <div class="stats-label">今日处理</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon flowing-icon">
              <i class="el-icon-s-promotion"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ dashboardData.flowingCount || 0 }}</div>
              <div class="stats-label">流转中</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon completed-icon">
              <i class="el-icon-circle-check"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ dashboardData.completedCount || 0 }}</div>
              <div class="stats-label">已完成</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="chart-card">
          <div slot="header" class="card-header">
            <span>公文类型分布</span>
          </div>
          <div id="docTypeChart" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="chart-card">
          <div slot="header" class="card-header">
            <span>本周处理趋势</span>
          </div>
          <div id="weeklyTrendChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快捷操作和流转状态 -->
    <el-row :gutter="20" class="actions-row">
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="action-card">
          <div slot="header" class="card-header">
            <span>快捷操作</span>
          </div>
          <div class="action-buttons">
            <el-button type="primary" @click="goToPendingDocuments">
              <i class="el-icon-document"></i> 待处理公文
            </el-button>
            <el-button type="success" @click="goToDocumentEdit">
              <i class="el-icon-edit"></i> 公文编辑
            </el-button>
            <el-button type="warning" @click="goToFlowControl">
              <i class="el-icon-s-promotion"></i> 流转控制
            </el-button>
            <el-button type="info" @click="goToSendManage">
              <i class="el-icon-message"></i> 发送管理
            </el-button>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="status-card">
          <div slot="header" class="card-header">
            <span>流转状态概览</span>
          </div>
          <div class="flow-status">
            <div class="status-item" v-for="(count, status) in dashboardData.flowStatusStats" :key="status">
              <div class="status-label">{{ getStatusLabel(status) }}</div>
              <div class="status-count">{{ count }}</div>
              <div class="status-bar">
                <div class="status-progress" :style="{ width: getStatusPercent(count) + '%' }"></div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最近处理公文 -->
    <el-row class="recent-row">
      <el-col :span="24">
        <el-card class="recent-card">
          <div slot="header" class="card-header">
            <span>最近处理公文</span>
            <el-button type="text" @click="goToDocumentList">查看更多</el-button>
          </div>
          <el-table :data="dashboardData.recentDocuments" style="width: 100%">
            <el-table-column prop="docTitle" label="公文标题" min-width="200" />
            <el-table-column prop="docType" label="类型" width="100">
              <template slot-scope="scope">
                <el-tag size="small">{{ getDocTypeLabel(scope.row.docType) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createDept" label="创建部门" width="120" />
            <el-table-column prop="currentStatus" label="当前状态" width="120">
              <template slot-scope="scope">
                <el-tag :type="getStatusType(scope.row.currentStatus)" size="small">
                  {{ getStatusLabel(scope.row.currentStatus) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="processTime" label="处理时间" width="160" />
            <el-table-column label="操作" width="150">
              <template slot-scope="scope">
                <el-button size="mini" @click="handleDocument(scope.row)">
                  处理
                </el-button>
                <el-button size="mini" type="primary" @click="viewDocument(scope.row)">
                  查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getOfficeDashboard } from '@/api/office/dashboard'

export default {
  name: 'OfficeDashboard',
  data() {
    return {
      dashboardData: {
        pendingCount: 0,
        todayProcessed: 0,
        flowingCount: 0,
        completedCount: 0,
        docTypeStats: {},
        flowStatusStats: {},
        weeklyTrend: [],
        recentDocuments: []
      },
      docTypeChart: null,
      weeklyTrendChart: null
    }
  },
  mounted() {
    this.initData()
    this.initCharts()
    // 定时刷新数据
    this.timer = setInterval(() => {
      this.refreshData()
    }, 30000) // 每30秒刷新一次
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
    if (this.docTypeChart) {
      this.docTypeChart.dispose()
    }
    if (this.weeklyTrendChart) {
      this.weeklyTrendChart.dispose()
    }
  },
  methods: {
    // 初始化数据
    async initData() {
      await this.getDashboardData()
    },

    // 获取仪表板数据
    async getDashboardData() {
      try {
        const response = await getOfficeDashboard()
        this.dashboardData = response.data
        this.updateCharts()
      } catch (error) {
        this.$message.error('获取仪表板数据失败')
      }
    },

    // 刷新数据
    refreshData() {
      this.getDashboardData()
    },

    // 初始化图表
    initCharts() {
      this.initDocTypeChart()
      this.initWeeklyTrendChart()
    },

    // 初始化公文类型图表
    initDocTypeChart() {
      const chartDom = document.getElementById('docTypeChart')
      this.docTypeChart = echarts.init(chartDom)
    },

    // 初始化周趋势图表
    initWeeklyTrendChart() {
      const chartDom = document.getElementById('weeklyTrendChart')
      this.weeklyTrendChart = echarts.init(chartDom)
    },

    // 更新图表
    updateCharts() {
      this.updateDocTypeChart()
      this.updateWeeklyTrendChart()
    },

    // 更新公文类型图表
    updateDocTypeChart() {
      if (!this.docTypeChart) return
      
      const data = Object.entries(this.dashboardData.docTypeStats || {}).map(([key, value]) => ({
        name: this.getDocTypeLabel(key),
        value: value
      }))
      
      const option = {
        tooltip: {
          trigger: 'item'
        },
        series: [
          {
            name: '公文类型',
            type: 'pie',
            radius: '60%',
            data: data,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      this.docTypeChart.setOption(option)
    },

    // 更新周趋势图表
    updateWeeklyTrendChart() {
      if (!this.weeklyTrendChart) return
      
      const weeklyData = this.dashboardData.weeklyTrend || []
      const dates = weeklyData.map(item => item.date)
      const counts = weeklyData.map(item => item.count)
      const completed = weeklyData.map(item => item.completed)
      
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['处理数量', '完成数量']
        },
        xAxis: {
          type: 'category',
          data: dates
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '处理数量',
            type: 'line',
            data: counts,
            itemStyle: { color: '#409EFF' }
          },
          {
            name: '完成数量',
            type: 'line',
            data: completed,
            itemStyle: { color: '#67C23A' }
          }
        ]
      }
      this.weeklyTrendChart.setOption(option)
    },

    // 获取状态标签
    getStatusLabel(status) {
      const statusMap = {
        'DRAFT': '草稿',
        'OFFICE_EDIT': '办公室修改',
        'VICE_REVIEW': '副厂长审核',
        'DIRECTOR_APPROVE': '厂长审签',
        'OFFICE_SEND': '办公室发送',
        'DEPT_RECEIVE': '部门签收',
        'COMPLETED': '已完成',
        'REJECTED': '已退回'
      }
      return statusMap[status] || status
    },

    // 获取状态类型
    getStatusType(status) {
      const typeMap = {
        'DRAFT': 'info',
        'OFFICE_EDIT': 'warning',
        'VICE_REVIEW': 'primary',
        'DIRECTOR_APPROVE': 'primary',
        'OFFICE_SEND': 'warning',
        'DEPT_RECEIVE': 'success',
        'COMPLETED': 'success',
        'REJECTED': 'danger'
      }
      return typeMap[status] || 'info'
    },

    // 获取公文类型标签
    getDocTypeLabel(type) {
      const typeMap = {
        'NOTICE': '通知',
        'REPORT': '报告',
        'REQUEST': '请示',
        'DECISION': '决定',
        'OPINION': '意见',
        'LETTER': '函',
        'MINUTES': '会议纪要'
      }
      return typeMap[type] || type
    },

    // 获取状态百分比
    getStatusPercent(count) {
      const total = Object.values(this.dashboardData.flowStatusStats || {}).reduce((sum, val) => sum + val, 0)
      return total > 0 ? (count / total * 100) : 0
    },

    // 导航到待处理公文
    goToPendingDocuments() {
      this.$router.push('/office/pending')
    },

    // 导航到公文编辑
    goToDocumentEdit() {
      this.$router.push('/office/edit')
    },

    // 导航到流转控制
    goToFlowControl() {
      this.$router.push('/office/flow')
    },

    // 导航到发送管理
    goToSendManage() {
      this.$router.push('/office/send')
    },

    // 导航到公文列表
    goToDocumentList() {
      this.$router.push('/office/documents')
    },

    // 处理公文
    handleDocument(row) {
      this.$router.push(`/office/handle/${row.docId}`)
    },

    // 查看公文
    viewDocument(row) {
      this.$router.push(`/office/view/${row.docId}`)
    }
  }
}
</script>

<style scoped>
.office-dashboard {
  padding: 20px;
}

.stats-row,
.charts-row,
.actions-row,
.recent-row {
  margin-bottom: 20px;
}

.stats-card {
  height: 120px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: white;
}

.pending-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.today-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.flowing-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.completed-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.chart-card {
  height: 400px;
}

.chart-container {
  height: 320px;
}

.action-card,
.status-card {
  height: 300px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.action-buttons .el-button {
  flex: 1;
  min-width: 120px;
}

.flow-status {
  padding: 10px 0;
}

.status-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.status-label {
  width: 80px;
  font-size: 14px;
  color: #606266;
}

.status-count {
  width: 40px;
  font-weight: bold;
  color: #303133;
}

.status-bar {
  flex: 1;
  height: 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  margin-left: 10px;
  overflow: hidden;
}

.status-progress {
  height: 100%;
  background: linear-gradient(90deg, #409EFF, #67C23A);
  transition: width 0.3s ease;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .office-dashboard {
    padding: 10px;
  }
  
  .stats-card {
    margin-bottom: 10px;
  }
  
  .chart-card {
    height: 350px;
    margin-bottom: 10px;
  }
  
  .chart-container {
    height: 270px;
  }
}

@media (max-width: 800px) {
  .stats-content {
    flex-direction: column;
    text-align: center;
  }
  
  .stats-icon {
    margin-right: 0;
    margin-bottom: 10px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-buttons .el-button {
    width: 100%;
  }
}
</style>
