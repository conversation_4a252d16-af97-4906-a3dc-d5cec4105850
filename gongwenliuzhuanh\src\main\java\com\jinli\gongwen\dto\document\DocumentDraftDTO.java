package com.jinli.gongwen.dto.document;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 公文草稿数据传输对象
 */
@Data
@ApiModel("公文草稿数据传输对象")
public class DocumentDraftDTO {

    @ApiModelProperty("公文ID")
    private String docId;

    @ApiModelProperty(value = "公文标题", required = true)
    @NotBlank(message = "公文标题不能为空")
    @Size(max = 200, message = "公文标题长度不能超过200个字符")
    private String docTitle;

    @ApiModelProperty("公文编号")
    @Size(max = 50, message = "公文编号长度不能超过50个字符")
    private String docNumber;

    @ApiModelProperty(value = "公文类型", required = true)
    @NotBlank(message = "公文类型不能为空")
    private String docType;

    @ApiModelProperty("公文级别")
    private String docLevel = "普通";

    @ApiModelProperty(value = "公文内容", required = true)
    @NotBlank(message = "公文内容不能为空")
    private String docContent;

    @ApiModelProperty("截止时间")
    private String deadline;

    @ApiModelProperty("创建用户ID")
    private String createUserId;

    @ApiModelProperty("创建部门ID")
    private String createDeptId;

    @ApiModelProperty("备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    @ApiModelProperty("是否保存为草稿")
    private Boolean isDraft = true;

    @ApiModelProperty("模板ID")
    private String templateId;
}
