package com.jinli.gongwen.service.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinli.gongwen.dto.system.UserDTO;
import com.jinli.gongwen.dto.system.UserQueryDTO;
import com.jinli.gongwen.entity.system.SysUser;
import com.jinli.gongwen.vo.system.UserVO;

import java.util.List;

/**
 * 用户服务接口
 */
public interface UserService {

    /**
     * 获取用户列表
     */
    IPage<UserVO> getUserList(Page<SysUser> page, UserQueryDTO queryDTO);

    /**
     * 根据ID获取用户
     */
    UserVO getUserById(String userId);

    /**
     * 创建用户
     */
    String createUser(UserDTO userDTO);

    /**
     * 更新用户
     */
    void updateUser(String userId, UserDTO userDTO);

    /**
     * 删除用户
     */
    void deleteUser(String userId);

    /**
     * 批量删除用户
     */
    void batchDeleteUsers(List<String> userIds);

    /**
     * 启用/禁用用户
     */
    void toggleUserStatus(String userId, Boolean isEnabled);

    /**
     * 重置密码
     */
    void resetPassword(String userId, String newPassword);

    /**
     * 分配用户角色
     */
    void assignUserRoles(String userId, List<String> roleIds);

    /**
     * 获取用户角色
     */
    List<String> getUserRoles(String userId);

    /**
     * 根据用户名获取用户
     */
    SysUser getUserByUsername(String username);
}
