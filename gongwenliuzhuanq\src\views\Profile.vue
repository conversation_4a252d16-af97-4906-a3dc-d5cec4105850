<template>
  <div class="profile">
    <div class="page-header">
      <h1 class="page-title">个人中心</h1>
    </div>
    
    <el-row :gutter="20">
      <!-- 个人信息卡片 -->
      <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="6">
        <el-card class="profile-card">
          <div class="profile-avatar">
            <el-avatar :size="80">
              {{ userInfo?.realName?.charAt(0) || 'A' }}
            </el-avatar>
            <h3 class="profile-name">{{ userInfo?.realName || '系统管理员' }}</h3>
            <p class="profile-role">{{ userInfo?.roleName || '管理员' }}</p>
            <p class="profile-dept">{{ userInfo?.departmentName || '办公室' }}</p>
          </div>
          
          <div class="profile-stats">
            <div class="stat-item">
              <div class="stat-value">23</div>
              <div class="stat-label">待处理</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">156</div>
              <div class="stat-label">已处理</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">89</div>
              <div class="stat-label">已创建</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 个人信息编辑 -->
      <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="18">
        <el-card class="info-card">
          <template #header>
            <div class="card-header">
              <span>个人信息</span>
              <el-button
                v-if="!editing"
                type="primary"
                size="small"
                @click="startEdit"
              >
                编辑
              </el-button>
              <div v-else>
                <el-button size="small" @click="cancelEdit">取消</el-button>
                <el-button
                  type="primary"
                  size="small"
                  :loading="saving"
                  @click="saveProfile"
                >
                  保存
                </el-button>
              </div>
            </div>
          </template>
          
          <el-form
            ref="profileFormRef"
            :model="profileForm"
            label-width="100px"
            class="profile-form"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="用户名">
                  <el-input
                    v-model="profileForm.username"
                    disabled
                    placeholder="用户名不可修改"
                  />
                </el-form-item>
              </el-col>
              
              <el-col :span="12">
                <el-form-item label="真实姓名">
                  <el-input
                    v-model="profileForm.realName"
                    :disabled="!editing"
                    placeholder="请输入真实姓名"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="邮箱">
                  <el-input
                    v-model="profileForm.email"
                    :disabled="!editing"
                    placeholder="请输入邮箱地址"
                  />
                </el-form-item>
              </el-col>
              
              <el-col :span="12">
                <el-form-item label="手机号">
                  <el-input
                    v-model="profileForm.phone"
                    :disabled="!editing"
                    placeholder="请输入手机号码"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="所属部门">
                  <el-input
                    value="办公室"
                    disabled
                    placeholder="部门信息由管理员设置"
                  />
                </el-form-item>
              </el-col>
              
              <el-col :span="12">
                <el-form-item label="用户角色">
                  <el-input
                    value="管理员"
                    disabled
                    placeholder="角色信息由管理员设置"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-form-item label="备注">
              <el-input
                v-model="profileForm.remark"
                type="textarea"
                :rows="3"
                :disabled="!editing"
                placeholder="个人备注信息"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </el-form>
        </el-card>
        
        <!-- 修改密码 -->
        <el-card class="password-card" style="margin-top: 20px;">
          <template #header>
            <span>修改密码</span>
          </template>
          
          <el-form
            ref="passwordFormRef"
            :model="passwordForm"
            label-width="100px"
            class="password-form"
          >
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="原密码">
                  <el-input
                    v-model="passwordForm.oldPassword"
                    type="password"
                    show-password
                    placeholder="请输入原密码"
                  />
                </el-form-item>
              </el-col>
              
              <el-col :span="8">
                <el-form-item label="新密码">
                  <el-input
                    v-model="passwordForm.newPassword"
                    type="password"
                    show-password
                    placeholder="请输入新密码"
                  />
                </el-form-item>
              </el-col>
              
              <el-col :span="8">
                <el-form-item label="确认密码">
                  <el-input
                    v-model="passwordForm.confirmPassword"
                    type="password"
                    show-password
                    placeholder="请再次输入新密码"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-form-item>
              <el-button
                type="primary"
                :loading="changingPassword"
                @click="handleChangePassword"
              >
                修改密码
              </el-button>
              <el-button @click="resetPasswordForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'

const userStore = useUserStore()

// 响应式数据
const editing = ref(false)
const saving = ref(false)
const changingPassword = ref(false)
const profileFormRef = ref()
const passwordFormRef = ref()

// 个人信息表单
const profileForm = reactive({
  userId: 'USER001',
  username: 'admin',
  realName: '系统管理员',
  email: '<EMAIL>',
  phone: '13800000001',
  remark: ''
})

// 密码修改表单
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 计算属性
const userInfo = computed(() => userStore.userInfo || {
  userId: 'USER001',
  username: 'admin',
  realName: '系统管理员',
  email: '<EMAIL>',
  phone: '13800000001',
  departmentName: '办公室',
  roleName: '管理员'
})

// 方法
const startEdit = () => {
  editing.value = true
}

const cancelEdit = () => {
  editing.value = false
}

const saveProfile = () => {
  saving.value = true
  setTimeout(() => {
    ElMessage.success('个人信息更新成功')
    editing.value = false
    saving.value = false
  }, 1000)
}

const handleChangePassword = () => {
  if (!passwordForm.oldPassword || !passwordForm.newPassword || !passwordForm.confirmPassword) {
    ElMessage.error('请填写完整的密码信息')
    return
  }
  
  if (passwordForm.newPassword !== passwordForm.confirmPassword) {
    ElMessage.error('两次输入的密码不一致')
    return
  }
  
  changingPassword.value = true
  setTimeout(() => {
    ElMessage.success('密码修改成功')
    resetPasswordForm()
    changingPassword.value = false
  }, 1000)
}

const resetPasswordForm = () => {
  passwordForm.oldPassword = ''
  passwordForm.newPassword = ''
  passwordForm.confirmPassword = ''
}
</script>

<style lang="scss" scoped>
.profile {
  .profile-card {
    .profile-avatar {
      text-align: center;
      padding: 20px 0;

      .profile-name {
        margin: 16px 0 8px 0;
        font-size: 20px;
        font-weight: 600;
        color: #303133;
      }

      .profile-role {
        margin: 0 0 4px 0;
        color: #409eff;
        font-size: 14px;
      }

      .profile-dept {
        margin: 0;
        color: #909399;
        font-size: 12px;
      }
    }

    .profile-stats {
      display: flex;
      justify-content: space-around;
      padding: 20px 0;
      border-top: 1px solid #f0f0f0;

      .stat-item {
        text-align: center;

        .stat-value {
          font-size: 24px;
          font-weight: 600;
          color: #409eff;
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .profile-form {
    .el-form-item {
      margin-bottom: 20px;
    }
  }

  .password-form {
    .el-form-item {
      margin-bottom: 20px;
    }
  }
}
</style>
