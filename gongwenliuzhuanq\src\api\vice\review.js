import request from '@/utils/request'

// 审核公文
export function reviewDocument(docId, data) {
  return request({
    url: `/api/vice/review/${docId}`,
    method: 'post',
    data
  })
}

// 批量审核公文
export function batchReviewDocuments(data) {
  return request({
    url: '/api/vice/batchReview',
    method: 'post',
    data
  })
}

// 获取公文详情
export function getDocumentDetail(docId) {
  return request({
    url: `/api/vice/document/${docId}`,
    method: 'get'
  })
}

// 获取审核历史记录
export function getReviewHistory(params) {
  return request({
    url: '/api/vice/reviewHistory',
    method: 'get',
    params
  })
}

// 获取所有已发公文
export function getAllDocuments(params) {
  return request({
    url: '/api/vice/allDocuments',
    method: 'get',
    params
  })
}

// 退回公文
export function returnDocument(docId, reason) {
  return request({
    url: `/api/vice/return/${docId}`,
    method: 'post',
    params: { reason }
  })
}
