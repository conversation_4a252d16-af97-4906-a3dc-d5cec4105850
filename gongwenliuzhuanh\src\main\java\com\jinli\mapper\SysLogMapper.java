package com.jinli.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jinli.entity.SysLog;
import org.apache.ibatis.annotations.Mapper;

/**
 * 系统日志Mapper接口
 *
 * 简化版本，避免复杂的注解SQL导致的XML解析问题
 * 所有查询都通过MyBatis-Plus的QueryWrapper实现
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
@Mapper
public interface SysLogMapper extends BaseMapper<SysLog> {

    // 所有方法都使用MyBatis-Plus的BaseMapper提供的基础方法
    // 复杂查询在Service层使用QueryWrapper实现

}
