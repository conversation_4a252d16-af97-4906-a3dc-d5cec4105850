package com.jinli.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jinli.entity.SysLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 系统日志Mapper接口
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
@Mapper
public interface SysLogMapper extends BaseMapper<SysLog> {

    /**
     * 根据条件查询日志列表
     *
     * @param username 用户名
     * @param module 操作模块
     * @param operationType 操作类型
     * @param status 操作状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 日志列表
     */
    @Select("<script>" +
            "SELECT * FROM sys_log WHERE 1=1 " +
            "<if test='username != null and username != \"\"'>" +
            "AND username LIKE CONCAT('%', #{username}, '%') " +
            "</if>" +
            "<if test='module != null and module != \"\"'>" +
            "AND module = #{module} " +
            "</if>" +
            "<if test='operationType != null and operationType != \"\"'>" +
            "AND operation_type = #{operationType} " +
            "</if>" +
            "<if test='status != null and status != \"\"'>" +
            "AND status = #{status} " +
            "</if>" +
            "<if test='startTime != null'>" +
            "AND create_time >= #{startTime} " +
            "</if>" +
            "<if test='endTime != null'>" +
            "AND create_time <= #{endTime} " +
            "</if>" +
            "ORDER BY create_time DESC" +
            "</script>")
    List<SysLog> selectLogList(@Param("username") String username,
                              @Param("module") String module,
                              @Param("operationType") String operationType,
                              @Param("status") String status,
                              @Param("startTime") LocalDateTime startTime,
                              @Param("endTime") LocalDateTime endTime);

    /**
     * 获取日志统计信息
     *
     * @return 统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as totalCount, " +
            "SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as successCount, " +
            "SUM(CASE WHEN status = 'FAIL' THEN 1 ELSE 0 END) as failCount, " +
            "SUM(CASE WHEN TRUNC(create_time) = TRUNC(SYSDATE) THEN 1 ELSE 0 END) as todayCount " +
            "FROM sys_log")
    Map<String, Object> selectLogStatistics();

    /**
     * 获取模块统计信息
     *
     * @return 模块统计
     */
    @Select("SELECT module, COUNT(*) as count FROM sys_log GROUP BY module ORDER BY count DESC")
    List<Map<String, Object>> selectModuleStatistics();

    /**
     * 获取操作类型统计信息
     *
     * @return 操作类型统计
     */
    @Select("SELECT operation_type, COUNT(*) as count FROM sys_log GROUP BY operation_type ORDER BY count DESC")
    List<Map<String, Object>> selectOperationTypeStatistics();

    /**
     * 获取最近7天的日志统计
     *
     * @return 最近7天统计
     */
    @Select("SELECT TRUNC(create_time) as date, COUNT(*) as count " +
            "FROM sys_log " +
            "WHERE create_time >= SYSDATE - 7 " +
            "GROUP BY TRUNC(create_time) " +
            "ORDER BY date DESC")
    List<Map<String, Object>> selectRecentDaysStatistics();

    /**
     * 清空指定时间之前的日志
     *
     * @param beforeTime 指定时间
     * @return 删除数量
     */
    @Select("DELETE FROM sys_log WHERE create_time < #{beforeTime}")
    int deleteLogsBefore(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 获取用户操作统计
     *
     * @return 用户操作统计
     */
    @Select("SELECT username, real_name, COUNT(*) as count " +
            "FROM sys_log " +
            "GROUP BY username, real_name " +
            "ORDER BY count DESC " +
            "LIMIT 10")
    List<Map<String, Object>> selectUserOperationStatistics();
}
