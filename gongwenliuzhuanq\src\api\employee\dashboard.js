import request from '@/utils/request'

// 获取员工个人工作台数据
export function getEmployeeDashboard() {
  return request({
    url: '/api/employee/dashboard',
    method: 'get'
  })
}

// 获取待签收公文列表
export function getPendingReceiveDocuments(params) {
  return request({
    url: '/api/employee/pendingReceive',
    method: 'get',
    params
  })
}

// 获取已签收公文列表
export function getReceivedDocuments(params) {
  return request({
    url: '/api/employee/receivedDocuments',
    method: 'get',
    params
  })
}

// 获取个人待办事项
export function getTodoList() {
  return request({
    url: '/api/employee/todoList',
    method: 'get'
  })
}

// 获取个人工作统计
export function getWorkStatistics() {
  return request({
    url: '/api/employee/workStatistics',
    method: 'get'
  })
}
