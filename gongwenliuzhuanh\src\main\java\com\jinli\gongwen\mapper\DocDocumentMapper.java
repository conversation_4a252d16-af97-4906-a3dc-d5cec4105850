package com.jinli.gongwen.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinli.gongwen.entity.DocDocument;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 公文信息表 Mapper 接口
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
@Mapper
public interface DocDocumentMapper extends BaseMapper<DocDocument> {

    /**
     * 分页查询公文列表（包含创建人、部门、处理人等信息）
     * 
     * @param page 分页参数
     * @param docTitle 公文标题（模糊查询）
     * @param docType 公文类型
     * @param docStatus 公文状态
     * @param createUserId 创建人ID
     * @param createDeptId 创建部门ID
     * @param currentHandler 当前处理人ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 公文列表
     */
    IPage<DocDocument> selectDocumentPage(Page<DocDocument> page,
                                          @Param("docTitle") String docTitle,
                                          @Param("docType") String docType,
                                          @Param("docStatus") String docStatus,
                                          @Param("createUserId") String createUserId,
                                          @Param("createDeptId") String createDeptId,
                                          @Param("currentHandler") String currentHandler,
                                          @Param("startTime") Date startTime,
                                          @Param("endTime") Date endTime);

    /**
     * 根据公文ID查询公文详细信息（包含创建人、部门、处理人等信息）
     * 
     * @param docId 公文ID
     * @return 公文详细信息
     */
    DocDocument selectDocumentDetailById(@Param("docId") String docId);

    /**
     * 查询用户待处理的公文列表
     * 
     * @param page 分页参数
     * @param userId 用户ID
     * @return 待处理公文列表
     */
    IPage<DocDocument> selectPendingDocuments(Page<DocDocument> page, @Param("userId") String userId);

    /**
     * 查询用户已处理的公文列表
     * 
     * @param page 分页参数
     * @param userId 用户ID
     * @return 已处理公文列表
     */
    IPage<DocDocument> selectProcessedDocuments(Page<DocDocument> page, @Param("userId") String userId);

    /**
     * 查询部门相关的公文列表
     * 
     * @param page 分页参数
     * @param departmentId 部门ID
     * @param docStatus 公文状态
     * @return 部门公文列表
     */
    IPage<DocDocument> selectDepartmentDocuments(Page<DocDocument> page,
                                                 @Param("departmentId") String departmentId,
                                                 @Param("docStatus") String docStatus);

    /**
     * 查询已发送的公文列表（供领导查看）
     * 
     * @param page 分页参数
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 已发送公文列表
     */
    IPage<DocDocument> selectSentDocuments(Page<DocDocument> page,
                                           @Param("startTime") Date startTime,
                                           @Param("endTime") Date endTime);

    /**
     * 检查公文编号是否唯一
     * 
     * @param docNumber 公文编号
     * @param docId 公文ID（排除自己）
     * @return 数量
     */
    int checkDocNumberUnique(@Param("docNumber") String docNumber, @Param("docId") String docId);

    /**
     * 统计用户待处理公文数量
     * 
     * @param userId 用户ID
     * @return 待处理数量
     */
    int countPendingDocuments(@Param("userId") String userId);

    /**
     * 统计部门公文数量
     * 
     * @param departmentId 部门ID
     * @param docStatus 公文状态
     * @return 公文数量
     */
    int countDepartmentDocuments(@Param("departmentId") String departmentId, @Param("docStatus") String docStatus);

    /**
     * 查询即将到期的公文列表
     * 
     * @param days 天数
     * @return 即将到期的公文列表
     */
    List<DocDocument> selectExpiringDocuments(@Param("days") int days);

    /**
     * 更新公文状态和当前处理人
     * 
     * @param docId 公文ID
     * @param docStatus 公文状态
     * @param currentHandler 当前处理人
     * @param currentStep 当前步骤
     * @return 影响行数
     */
    int updateDocumentStatus(@Param("docId") String docId,
                             @Param("docStatus") String docStatus,
                             @Param("currentHandler") String currentHandler,
                             @Param("currentStep") String currentStep);

    /**
     * 根据条件搜索公文
     * 
     * @param page 分页参数
     * @param keyword 关键词（标题、内容）
     * @param docType 公文类型
     * @param createDeptId 创建部门ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 搜索结果
     */
    IPage<DocDocument> searchDocuments(Page<DocDocument> page,
                                       @Param("keyword") String keyword,
                                       @Param("docType") String docType,
                                       @Param("createDeptId") String createDeptId,
                                       @Param("startTime") Date startTime,
                                       @Param("endTime") Date endTime);
}
