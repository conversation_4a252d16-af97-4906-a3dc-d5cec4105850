import request from '@/utils/request'

// 用户登录
export function login(data) {
  return request({
    url: '/api/login',
    method: 'post',
    data
  })
}

// 获取用户信息
export function getUserInfo(userId) {
  return request({
    url: '/api/userinfo',
    method: 'get',
    params: { userId }
  })
}

// 用户登出
export function logout() {
  return request({
    url: '/api/logout',
    method: 'post'
  })
}

// 修改密码
export function changePassword(data) {
  return request({
    url: '/api/changePassword',
    method: 'post',
    data
  })
}

// 更新个人信息
export function updateProfile(data) {
  return request({
    url: '/api/updateProfile',
    method: 'post',
    data
  })
}
