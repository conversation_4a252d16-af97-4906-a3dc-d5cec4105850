<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>API测试</title>
  <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      color: #333;
    }
    .test-section {
      margin-bottom: 20px;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }
    button {
      padding: 8px 15px;
      background-color: #4F46E5;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 10px;
    }
    button:hover {
      background-color: #4338CA;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
      margin-top: 10px;
    }
    .success {
      color: #065F46;
    }
    .error {
      color: #B91C1C;
    }
  </style>
</head>
<body>
  <h1>API测试工具</h1>
  
  <div class="test-section">
    <h2>测试1: 测试接口</h2>
    <button onclick="testSimpleGet()">测试GET请求</button>
    <pre id="simple-result">点击按钮开始测试...</pre>
  </div>
  
  <div class="test-section">
    <h2>测试2: 通过代理请求</h2>
    <button onclick="testProxyRequest()">测试代理请求</button>
    <pre id="proxy-result">点击按钮开始测试...</pre>
  </div>
  
  <div class="test-section">
    <h2>测试3: 登录请求</h2>
    <button onclick="testLogin()">测试登录</button>
    <pre id="login-result">点击按钮开始测试...</pre>
  </div>

  <script>
    // 测试简单GET请求
    async function testSimpleGet() {
      const resultElement = document.getElementById('simple-result');
      resultElement.innerHTML = '正在请求...';
      resultElement.className = '';

      try {
        const response = await fetch('http://localhost:8080/api/hello', {
          method: 'GET',
          mode: 'cors'
        });

        const data = await response.json();
        resultElement.innerHTML = '请求成功:\n' + JSON.stringify(data, null, 2);
        resultElement.className = 'success';
      } catch (error) {
        resultElement.innerHTML = '请求失败:\n' + error.message;
        resultElement.className = 'error';
      }
    }
    
    // 测试通过代理请求
    async function testProxyRequest() {
      const resultElement = document.getElementById('proxy-result');
      resultElement.innerHTML = '正在请求...';
      resultElement.className = '';
      
      try {
        const response = await axios.get('/api/login', {
          timeout: 5000
        });
        resultElement.innerHTML = '请求成功:\n' + JSON.stringify(response.data, null, 2);
        resultElement.className = 'success';
      } catch (error) {
        resultElement.innerHTML = '请求失败:\n' + error.message;
        if (error.response) {
          resultElement.innerHTML += '\n\n响应数据:\n' + JSON.stringify(error.response.data, null, 2);
        }
        resultElement.className = 'error';
      }
    }
    
    // 测试登录
    async function testLogin() {
      const resultElement = document.getElementById('login-result');
      resultElement.innerHTML = '正在登录...';
      resultElement.className = '';

      try {
        // 使用fetch API测试
        const response = await fetch('http://localhost:8080/api/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          body: JSON.stringify({
            username: 'admin',
            password: '123456'
          }),
          mode: 'cors',
          credentials: 'include'
        });

        const data = await response.json();
        resultElement.innerHTML = '登录成功:\n' + JSON.stringify(data, null, 2);
        resultElement.className = 'success';
      } catch (error) {
        resultElement.innerHTML = '登录失败:\n' + error.message;
        resultElement.className = 'error';
      }
    }
  </script>
</body>
</html>
