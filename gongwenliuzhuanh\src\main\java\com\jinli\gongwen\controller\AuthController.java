package com.jinli.gongwen.controller;

import com.jinli.gongwen.common.Constants;
import com.jinli.gongwen.common.Result;
import com.jinli.gongwen.entity.SysUser;
import com.jinli.gongwen.service.SysUserService;
import com.jinli.gongwen.util.IpUtil;
import com.jinli.gongwen.util.JwtUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 认证控制器
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
@Api(tags = "认证管理")
@RestController
@CrossOrigin(origins = "*", allowCredentials = "false")
public class AuthController {

    @Autowired
    private SysUserService userService;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 处理预检请求
     */
    @RequestMapping(value = "/login", method = RequestMethod.OPTIONS)
    @CrossOrigin(origins = "*", allowedHeaders = "*", allowCredentials = "false")
    public Result<String> loginOptions() {
        return Result.success("OK");
    }

    /**
     * 测试接口
     */
    @GetMapping("/test")
    @CrossOrigin(origins = "*", allowCredentials = "false")
    public Result<String> test() {
        return Result.success("测试成功", "Hello World!");
    }

    /**
     * 用户登录
     */
    @ApiOperation("用户登录")
    @PostMapping("/login")
    @CrossOrigin(origins = "*", allowedHeaders = "*", methods = {RequestMethod.POST, RequestMethod.OPTIONS}, allowCredentials = "false")
    public Result<Map<String, Object>> login(@Valid @RequestBody AuthController.LoginRequest loginRequest,
                                             HttpServletRequest request) {
        try {
            String loginIp = IpUtil.getClientIp(request);
            String token = userService.login(loginRequest.getUsername(), 
                                           loginRequest.getPassword(), loginIp);
            
            // 获取用户信息
            SysUser user = userService.getUserByUsername(loginRequest.getUsername());
            
            Map<String, Object> data = new HashMap<>();
            data.put("token", token);
            data.put("userInfo", user);
            
            return Result.success("登录成功", data);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取当前用户信息
     */
    @ApiOperation("获取当前用户信息")
    @GetMapping("/userinfo")
    @CrossOrigin(origins = "*", allowCredentials = "false")
    public Result<SysUser> getUserInfo(@RequestParam(required = false) String userId,
                                       HttpServletRequest request) {
        try {
            String targetUserId = userId;

            // 如果没有提供userId，尝试从JWT token中获取
            if (targetUserId == null || targetUserId.trim().isEmpty() || "undefined".equals(targetUserId)) {
                String token = extractTokenFromRequest(request);
                if (token != null) {
                    try {
                        Map<String, Object> claims = jwtUtil.getClaimsFromToken(token);
                        targetUserId = (String) claims.get(Constants.JWT_CLAIMS_USER_ID);
                    } catch (Exception e) {
                        // JWT解析失败，使用默认用户ID（临时方案）
                        targetUserId = "USER001";
                    }
                } else {
                    // 没有token，使用默认用户ID（临时方案）
                    targetUserId = "USER001";
                }
            }

            if (targetUserId == null || targetUserId.trim().isEmpty()) {
                return Result.error("用户ID不能为空");
            }

            SysUser user = userService.getUserDetailById(targetUserId);
            if (user == null) {
                return Result.error("用户不存在");
            }
            // 清除敏感信息
            user.setPassword(null);
            return Result.success(user);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 从请求中提取JWT token
     */
    private String extractTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }

    /**
     * 用户登出
     */
    @ApiOperation("用户登出")
    @PostMapping("/logout")
    public Result<String> logout() {
        // JWT是无状态的，客户端删除token即可
        return Result.success("登出成功");
    }

    /**
     * 修改密码
     */
    @ApiOperation("修改密码")
    @PostMapping("/changePassword")
    public Result<String> changePassword(@Valid @RequestBody AuthController.ChangePasswordRequest request) {
        try {
            boolean success = userService.changePassword(request.getUserId(),
                                                       request.getOldPassword(),
                                                       request.getNewPassword());
            if (success) {
                return Result.success("密码修改成功");
            } else {
                return Result.error("密码修改失败");
            }
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 更新个人信息
     */
    @ApiOperation("更新个人信息")
    @PostMapping("/updateProfile")
    public Result<String> updateProfile(@Valid @RequestBody SysUser user) {
        try {
            // 清除不允许修改的字段
            user.setUsername(null);
            user.setPassword(null);
            user.setStatus(null);
            user.setCreateTime(null);

            boolean success = userService.updateProfile(user);
            if (success) {
                return Result.success("个人信息更新成功");
            } else {
                return Result.error("个人信息更新失败");
            }
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 登录请求对象
     */
    public static class LoginRequest {
        @ApiParam("用户名")
        private String username;
        
        @ApiParam("密码")
        private String password;

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }
    }

    /**
     * 修改密码请求对象
     */
    public static class ChangePasswordRequest {
        @ApiParam("用户ID")
        private String userId;
        
        @ApiParam("旧密码")
        private String oldPassword;
        
        @ApiParam("新密码")
        private String newPassword;

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getOldPassword() {
            return oldPassword;
        }

        public void setOldPassword(String oldPassword) {
            this.oldPassword = oldPassword;
        }

        public String getNewPassword() {
            return newPassword;
        }

        public void setNewPassword(String newPassword) {
            this.newPassword = newPassword;
        }
    }
}
