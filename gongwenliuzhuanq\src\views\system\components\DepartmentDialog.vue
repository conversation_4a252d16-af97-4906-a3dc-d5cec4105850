<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="上级部门" prop="parentId">
        <el-tree-select
          v-model="formData.parentId"
          :data="departmentTree"
          :props="{ value: 'deptId', label: 'deptName', children: 'children' }"
          placeholder="请选择上级部门"
          clearable
          check-strictly
          :render-after-expand="false"
          style="width: 100%"
        />
        <div class="form-tip">不选择则为顶级部门</div>
      </el-form-item>
      
      <el-form-item label="部门名称" prop="deptName">
        <el-input
          v-model="formData.deptName"
          placeholder="请输入部门名称"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="部门编码" prop="deptCode">
        <el-input
          v-model="formData.deptCode"
          placeholder="请输入部门编码"
          maxlength="50"
          show-word-limit
        />
        <div class="form-tip">部门编码用于系统内部识别，必须唯一</div>
      </el-form-item>
      
      <el-form-item label="负责人" prop="leader">
        <el-input
          v-model="formData.leader"
          placeholder="请输入负责人姓名"
          maxlength="20"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="联系电话" prop="phone">
        <el-input
          v-model="formData.phone"
          placeholder="请输入联系电话"
          maxlength="20"
        />
      </el-form-item>
      
      <el-form-item label="邮箱" prop="email">
        <el-input
          v-model="formData.email"
          placeholder="请输入邮箱地址"
          maxlength="50"
        />
      </el-form-item>
      
      <el-form-item label="显示顺序" prop="sort">
        <el-input-number
          v-model="formData.sort"
          :min="0"
          :max="999"
          controls-position="right"
          style="width: 200px"
        />
      </el-form-item>
      
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio label="1">正常</el-radio>
          <el-radio label="0">停用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, nextTick, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { createDepartment, updateDepartment, getDepartmentTree, checkDepartmentCode } from '@/api/department'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  departmentData: {
    type: Object,
    default: () => ({})
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  parentDepartment: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const loading = ref(false)
const formRef = ref()
const dialogVisible = ref(false)
const departmentTree = ref([])

const formData = reactive({
  deptId: '',
  parentId: '',
  deptName: '',
  deptCode: '',
  leader: '',
  phone: '',
  email: '',
  sort: 0,
  status: '1'
})

// 计算属性
const dialogTitle = computed(() => {
  if (props.parentDepartment) {
    return `新增下级部门 - ${props.parentDepartment.deptName}`
  }
  return props.isEdit ? '编辑部门' : '新增部门'
})

// 自定义验证规则
const validateDeptCode = async (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入部门编码'))
    return
  }
  
  try {
    const response = await checkDepartmentCode(value, formData.deptId)
    if (response.code === 200 && !response.data) {
      callback(new Error('部门编码已存在'))
    } else {
      callback()
    }
  } catch (error) {
    callback()
  }
}

// 表单验证规则
const rules = {
  deptName: [
    { required: true, message: '请输入部门名称', trigger: 'blur' },
    { min: 2, max: 50, message: '部门名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  deptCode: [
    { required: true, message: '请输入部门编码', trigger: 'blur' },
    { min: 2, max: 50, message: '部门编码长度在 2 到 50 个字符', trigger: 'blur' },
    { pattern: /^[A-Z0-9_]+$/, message: '部门编码只能包含大写字母、数字和下划线', trigger: 'blur' },
    { validator: validateDeptCode, trigger: 'blur' }
  ],
  leader: [
    { max: 20, message: '负责人姓名不能超过20个字符', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^(\d{3,4}-?)?\d{7,8}$|^1[3-9]\d{9}$/, message: '请输入正确的电话号码', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  sort: [
    { required: true, message: '请输入显示顺序', trigger: 'blur' },
    { type: 'number', min: 0, max: 999, message: '显示顺序必须在 0 到 999 之间', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 监听visible变化
watch(() => props.visible, (val) => {
  dialogVisible.value = val
  if (val) {
    loadDepartmentTree()
    initForm()
  }
})

watch(dialogVisible, (val) => {
  emit('update:visible', val)
})

// 加载部门树数据
const loadDepartmentTree = async () => {
  try {
    const response = await getDepartmentTree()
    if (response.code === 200) {
      departmentTree.value = response.data || []
    }
  } catch (error) {
    console.error('加载部门树失败:', error)
  }
}

// 初始化表单
const initForm = () => {
  nextTick(() => {
    formRef.value?.clearValidate()
  })
  
  if (props.isEdit && props.departmentData.deptId) {
    // 编辑模式，复制数据
    Object.assign(formData, {
      deptId: props.departmentData.deptId,
      parentId: props.departmentData.parentId || '',
      deptName: props.departmentData.deptName || '',
      deptCode: props.departmentData.deptCode || '',
      leader: props.departmentData.leader || '',
      phone: props.departmentData.phone || '',
      email: props.departmentData.email || '',
      sort: props.departmentData.sort || 0,
      status: props.departmentData.status || '1'
    })
  } else {
    // 新增模式，重置表单
    Object.assign(formData, {
      deptId: '',
      parentId: props.parentDepartment?.deptId || '',
      deptName: '',
      deptCode: '',
      leader: '',
      phone: '',
      email: '',
      sort: 0,
      status: '1'
    })
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    loading.value = true
    
    if (props.isEdit) {
      await updateDepartment(formData)
      ElMessage.success('部门更新成功')
    } else {
      await createDepartment(formData)
      ElMessage.success('部门创建成功')
    }
    
    emit('success')
    handleClose()
  } catch (error) {
    if (error !== false) { // 不是表单验证错误
      ElMessage.error(props.isEdit ? '部门更新失败' : '部门创建失败')
    }
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  formRef.value?.resetFields()
}
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.dialog-footer {
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto !important;
  }
}
</style>
