<template>
  <div class="document-search">
    <div class="page-header">
      <h1 class="page-title">公文查询</h1>
    </div>

    <!-- 高级搜索 -->
    <el-card class="search-card" shadow="never">
      <template #header>
        <span>高级搜索</span>
      </template>
      
      <el-form :model="searchForm" label-width="100px" class="search-form">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="公文标题">
              <el-input
                v-model="searchForm.title"
                placeholder="请输入公文标题"
                clearable
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="公文编号">
              <el-input
                v-model="searchForm.docId"
                placeholder="请输入公文编号"
                clearable
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="公文类型">
              <el-select
                v-model="searchForm.docType"
                placeholder="请选择公文类型"
                clearable
                style="width: 100%"
              >
                <el-option label="通知" value="NOTICE" />
                <el-option label="请示" value="REQUEST" />
                <el-option label="报告" value="REPORT" />
                <el-option label="意见" value="OPINION" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="发起人">
              <el-input
                v-model="searchForm.createUser"
                placeholder="请输入发起人"
                clearable
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="searchForm.createTime"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="公文状态">
              <el-select
                v-model="searchForm.status"
                placeholder="请选择公文状态"
                clearable
                style="width: 100%"
              >
                <el-option label="草稿" value="DRAFT" />
                <el-option label="处理中" value="PROCESSING" />
                <el-option label="已完成" value="COMPLETED" />
                <el-option label="已归档" value="ARCHIVED" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="24">
            <el-form-item>
              <el-button type="primary" @click="handleSearch">
                <el-icon><Search /></el-icon>
                搜索
              </el-button>
              <el-button @click="resetSearch">重置</el-button>
              <el-button type="info" @click="exportData">
                <el-icon><Download /></el-icon>
                导出
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 搜索结果 -->
    <el-card class="table-card" shadow="never" v-if="showResults">
      <template #header>
        <span>搜索结果 (共 {{ pagination.total }} 条)</span>
      </template>
      
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="docId" label="公文编号" width="120" />
        <el-table-column prop="docTitle" label="公文标题" min-width="200" show-overflow-tooltip />
        <el-table-column prop="docType" label="公文类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getDocTypeColor(row.docType)" size="small">
              {{ getDocTypeName(row.docType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createUserName" label="发起人" width="100" />
        <el-table-column prop="createTime" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="docStatus" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.docStatus)" size="small">
              {{ getStatusName(row.docStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="info" size="small" @click="handleView(row)">查看</el-button>
            <el-button type="primary" size="small" @click="handleDetail(row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { getDocumentPage } from '@/api/document'
import dayjs from 'dayjs'
import { Search, Download } from '@element-plus/icons-vue'

const loading = ref(false)
const tableData = ref([])
const showResults = ref(false)

const searchForm = reactive({
  title: '',
  docId: '',
  docType: '',
  createUser: '',
  createTime: null,
  status: ''
})

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

const formatTime = (timestamp) => {
  return timestamp ? dayjs(timestamp).format('YYYY-MM-DD HH:mm') : '-'
}

const getDocTypeName = (type) => {
  const typeMap = {
    'NOTICE': '通知',
    'REQUEST': '请示',
    'REPORT': '报告',
    'OPINION': '意见'
  }
  return typeMap[type] || type
}

const getDocTypeColor = (type) => {
  const colorMap = {
    'NOTICE': 'primary',
    'REQUEST': 'warning',
    'REPORT': 'success',
    'OPINION': 'info'
  }
  return colorMap[type] || 'info'
}

const getStatusName = (status) => {
  const statusMap = {
    'DRAFT': '草稿',
    'PROCESSING': '处理中',
    'COMPLETED': '已完成',
    'ARCHIVED': '已归档'
  }
  return statusMap[status] || status
}

const getStatusColor = (status) => {
  const colorMap = {
    'DRAFT': 'info',
    'PROCESSING': 'warning',
    'COMPLETED': 'success',
    'ARCHIVED': 'primary'
  }
  return colorMap[status] || 'info'
}

const handleSearch = async () => {
  loading.value = true
  showResults.value = true
  
  try {
    const params = {
      pageNum: pagination.current,
      pageSize: pagination.size,
      ...searchForm
    }
    
    const response = await getDocumentPage(params)
    if (response.code === 200) {
      tableData.value = response.data.records || []
      pagination.total = response.data.total || 0
    }
  } catch (error) {
    console.error('搜索公文失败:', error)
    ElMessage.error('搜索失败')
  } finally {
    loading.value = false
  }
}

const resetSearch = () => {
  Object.assign(searchForm, {
    title: '',
    docId: '',
    docType: '',
    createUser: '',
    createTime: null,
    status: ''
  })
  showResults.value = false
  tableData.value = []
  pagination.current = 1
  pagination.total = 0
}

const exportData = () => {
  ElMessage.success('导出功能开发中...')
}

const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  handleSearch()
}

const handleCurrentChange = (current) => {
  pagination.current = current
  handleSearch()
}

const handleView = (row) => {
  ElMessage.info(`查看公文: ${row.docTitle}`)
}

const handleDetail = (row) => {
  ElMessage.info(`查看详情: ${row.docTitle}`)
}
</script>

<style lang="scss" scoped>
.document-search {
  .page-header {
    margin-bottom: 20px;
    
    .page-title {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .search-card {
    margin-bottom: 20px;
    
    .search-form {
      .el-form-item {
        margin-bottom: 20px;
      }
    }
  }
  
  .table-card {
    .pagination-container {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }
  }
}
</style>
