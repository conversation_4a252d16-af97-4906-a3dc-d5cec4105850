<template>
  <div class="vice-director-dashboard">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon pending-icon">
              <i class="el-icon-s-check"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ dashboardData.pendingReviewCount || 0 }}</div>
              <div class="stats-label">待审核</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon today-icon">
              <i class="el-icon-date"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ dashboardData.todayReviewedCount || 0 }}</div>
              <div class="stats-label">今日审核</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon urgent-icon">
              <i class="el-icon-warning"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ dashboardData.urgentDocumentCount || 0 }}</div>
              <div class="stats-label">紧急公文</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon rate-icon">
              <i class="el-icon-pie-chart"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ ((dashboardData.approvalRate || 0) * 100).toFixed(1) }}%</div>
              <div class="stats-label">通过率</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="chart-card">
          <div slot="header" class="card-header">
            <span>分管部门公文统计</span>
          </div>
          <div id="deptDocChart" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="chart-card">
          <div slot="header" class="card-header">
            <span>本周审核趋势</span>
          </div>
          <div id="weeklyReviewChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快捷操作和部门效率 -->
    <el-row :gutter="20" class="actions-row">
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="action-card">
          <div slot="header" class="card-header">
            <span>快捷操作</span>
          </div>
          <div class="action-buttons">
            <el-button type="primary" @click="goToPendingReview">
              <i class="el-icon-s-check"></i> 待审核公文
            </el-button>
            <el-button type="success" @click="goToReviewHistory">
              <i class="el-icon-document"></i> 审核历史
            </el-button>
            <el-button type="warning" @click="goToUrgentDocuments">
              <i class="el-icon-warning"></i> 紧急公文
            </el-button>
            <el-button type="info" @click="goToAllDocuments">
              <i class="el-icon-folder"></i> 所有公文
            </el-button>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="efficiency-card">
          <div slot="header" class="card-header">
            <span>分管部门效率排名</span>
          </div>
          <div class="efficiency-list">
            <div
              v-for="dept in dashboardData.deptEfficiencyRanking"
              :key="dept.deptId"
              class="efficiency-item"
            >
              <div class="efficiency-rank">{{ dept.ranking }}</div>
              <div class="efficiency-info">
                <div class="efficiency-name">{{ dept.deptName }}</div>
                <div class="efficiency-stats">
                  <span>公文: {{ dept.documentCount }}</span>
                  <span>及时率: {{ (dept.timelyRate * 100).toFixed(1) }}%</span>
                </div>
              </div>
              <div class="efficiency-time">{{ dept.avgProcessTime }}h</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 待审核和最近审核 -->
    <el-row :gutter="20" class="review-row">
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="review-card">
          <div slot="header" class="card-header">
            <span>待审核公文</span>
            <el-button type="text" @click="goToPendingReview">查看更多</el-button>
          </div>
          <div class="review-list">
            <div
              v-for="doc in dashboardData.pendingReviews"
              :key="doc.docId"
              class="review-item"
              @click="reviewDocument(doc)"
            >
              <div class="review-header">
                <div class="review-title">{{ doc.docTitle }}</div>
                <el-tag :type="getLevelType(doc.docLevel)" size="mini">
                  {{ doc.docLevel }}
                </el-tag>
              </div>
              <div class="review-meta">
                <span class="review-dept">{{ doc.createDeptName }}</span>
                <span class="review-type">{{ getDocTypeLabel(doc.docType) }}</span>
                <span class="review-time">{{ doc.createTime }}</span>
              </div>
              <div class="review-waiting">
                <el-tag :type="getWaitingType(doc.waitingHours)" size="mini">
                  等待 {{ doc.waitingHours }} 小时
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="review-card">
          <div slot="header" class="card-header">
            <span>最近审核</span>
            <el-button type="text" @click="goToReviewHistory">查看更多</el-button>
          </div>
          <div class="review-list">
            <div
              v-for="review in dashboardData.recentReviews"
              :key="review.docId"
              class="review-item"
              @click="viewDocument(review)"
            >
              <div class="review-header">
                <div class="review-title">{{ review.docTitle }}</div>
                <el-tag :type="review.isApproved ? 'success' : 'danger'" size="mini">
                  {{ review.isApproved ? '通过' : '退回' }}
                </el-tag>
              </div>
              <div class="review-meta">
                <span class="review-dept">{{ review.createDeptName }}</span>
                <span class="review-type">{{ getDocTypeLabel(review.docType) }}</span>
                <span class="review-time">{{ review.reviewTime }}</span>
              </div>
              <div class="review-opinion" v-if="review.reviewOpinion">
                {{ review.reviewOpinion }}
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getViceDirectorDashboard } from '@/api/vice/dashboard'

export default {
  name: 'ViceDirectorDashboard',
  data() {
    return {
      dashboardData: {
        pendingReviewCount: 0,
        todayReviewedCount: 0,
        urgentDocumentCount: 0,
        approvalRate: 0,
        deptDocumentStats: {},
        weeklyReviewTrend: [],
        pendingReviews: [],
        recentReviews: [],
        deptEfficiencyRanking: []
      },
      deptDocChart: null,
      weeklyReviewChart: null
    }
  },
  mounted() {
    this.initData()
    this.initCharts()
    // 定时刷新数据
    this.timer = setInterval(() => {
      this.refreshData()
    }, 30000) // 每30秒刷新一次
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
    if (this.deptDocChart) {
      this.deptDocChart.dispose()
    }
    if (this.weeklyReviewChart) {
      this.weeklyReviewChart.dispose()
    }
  },
  methods: {
    // 初始化数据
    async initData() {
      await this.getDashboardData()
    },

    // 获取仪表板数据
    async getDashboardData() {
      try {
        const response = await getViceDirectorDashboard()
        this.dashboardData = response.data
        this.updateCharts()
      } catch (error) {
        this.$message.error('获取仪表板数据失败')
      }
    },

    // 刷新数据
    refreshData() {
      this.getDashboardData()
    },

    // 初始化图表
    initCharts() {
      this.initDeptDocChart()
      this.initWeeklyReviewChart()
    },

    // 初始化部门公文图表
    initDeptDocChart() {
      const chartDom = document.getElementById('deptDocChart')
      this.deptDocChart = echarts.init(chartDom)
    },

    // 初始化周审核图表
    initWeeklyReviewChart() {
      const chartDom = document.getElementById('weeklyReviewChart')
      this.weeklyReviewChart = echarts.init(chartDom)
    },

    // 更新图表
    updateCharts() {
      this.updateDeptDocChart()
      this.updateWeeklyReviewChart()
    },

    // 更新部门公文图表
    updateDeptDocChart() {
      if (!this.deptDocChart) return
      
      const data = Object.entries(this.dashboardData.deptDocumentStats || {}).map(([key, value]) => ({
        name: this.getDeptLabel(key),
        value: value
      }))
      
      const option = {
        tooltip: {
          trigger: 'item'
        },
        series: [
          {
            name: '部门公文',
            type: 'pie',
            radius: '60%',
            data: data,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      this.deptDocChart.setOption(option)
    },

    // 更新周审核图表
    updateWeeklyReviewChart() {
      if (!this.weeklyReviewChart) return
      
      const weeklyData = this.dashboardData.weeklyReviewTrend || []
      const dates = weeklyData.map(item => item.date)
      const reviewCount = weeklyData.map(item => item.reviewCount)
      const approvedCount = weeklyData.map(item => item.approvedCount)
      const rejectedCount = weeklyData.map(item => item.rejectedCount)
      
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['审核总数', '通过', '退回']
        },
        xAxis: {
          type: 'category',
          data: dates
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '审核总数',
            type: 'line',
            data: reviewCount,
            itemStyle: { color: '#409EFF' }
          },
          {
            name: '通过',
            type: 'line',
            data: approvedCount,
            itemStyle: { color: '#67C23A' }
          },
          {
            name: '退回',
            type: 'line',
            data: rejectedCount,
            itemStyle: { color: '#F56C6C' }
          }
        ]
      }
      this.weeklyReviewChart.setOption(option)
    },

    // 获取部门标签
    getDeptLabel(deptId) {
      const deptMap = {
        'DEPT003': '生产部',
        'DEPT004': '销售部',
        'DEPT005': '财务部'
      }
      return deptMap[deptId] || deptId
    },

    // 获取公文类型标签
    getDocTypeLabel(type) {
      const typeMap = {
        'NOTICE': '通知',
        'REPORT': '报告',
        'REQUEST': '请示',
        'DECISION': '决定',
        'OPINION': '意见',
        'LETTER': '函',
        'MINUTES': '会议纪要'
      }
      return typeMap[type] || type
    },

    // 获取级别类型
    getLevelType(level) {
      const typeMap = {
        '普通': 'info',
        '紧急': 'warning',
        '特急': 'danger'
      }
      return typeMap[level] || 'info'
    },

    // 获取等待时间类型
    getWaitingType(hours) {
      if (hours > 24) return 'danger'
      if (hours > 8) return 'warning'
      return 'success'
    },

    // 导航到待审核公文
    goToPendingReview() {
      this.$router.push('/vice/pending-review')
    },

    // 导航到审核历史
    goToReviewHistory() {
      this.$router.push('/vice/review-history')
    },

    // 导航到紧急公文
    goToUrgentDocuments() {
      this.$router.push('/vice/urgent-documents')
    },

    // 导航到所有公文
    goToAllDocuments() {
      this.$router.push('/vice/all-documents')
    },

    // 审核公文
    reviewDocument(doc) {
      this.$router.push(`/vice/review/${doc.docId}`)
    },

    // 查看公文
    viewDocument(doc) {
      this.$router.push(`/vice/document/${doc.docId}`)
    }
  }
}
</script>

<style scoped>
.vice-director-dashboard {
  padding: 20px;
}

.stats-row,
.charts-row,
.actions-row,
.review-row {
  margin-bottom: 20px;
}

.stats-card {
  height: 120px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: white;
}

.pending-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.today-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.urgent-icon {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.rate-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.chart-card {
  height: 400px;
}

.chart-container {
  height: 320px;
}

.action-card,
.efficiency-card {
  height: 300px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.action-buttons .el-button {
  flex: 1;
  min-width: 120px;
}

.efficiency-list {
  max-height: 240px;
  overflow-y: auto;
}

.efficiency-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.efficiency-rank {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #409EFF;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 15px;
}

.efficiency-info {
  flex: 1;
}

.efficiency-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 5px;
}

.efficiency-stats {
  font-size: 12px;
  color: #909399;
}

.efficiency-stats span {
  margin-right: 10px;
}

.efficiency-time {
  font-weight: bold;
  color: #409EFF;
}

.review-card {
  height: 400px;
}

.review-list {
  max-height: 320px;
  overflow-y: auto;
}

.review-item {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s;
}

.review-item:hover {
  background-color: #f8f9fa;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.review-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  margin-right: 10px;
}

.review-meta {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 5px;
  font-size: 12px;
  color: #909399;
}

.review-waiting,
.review-opinion {
  font-size: 12px;
  color: #606266;
  margin-top: 5px;
}

.review-opinion {
  background-color: #f5f7fa;
  padding: 5px;
  border-radius: 3px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .vice-director-dashboard {
    padding: 10px;
  }
  
  .stats-card {
    margin-bottom: 10px;
  }
  
  .chart-card {
    height: 350px;
    margin-bottom: 10px;
  }
  
  .chart-container {
    height: 270px;
  }
}

@media (max-width: 800px) {
  .stats-content {
    flex-direction: column;
    text-align: center;
  }
  
  .stats-icon {
    margin-right: 0;
    margin-bottom: 10px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-buttons .el-button {
    width: 100%;
  }
  
  .efficiency-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .efficiency-rank {
    margin-bottom: 10px;
  }
}
</style>
