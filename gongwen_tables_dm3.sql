-- =====================================================
-- 河北金力集团公文流转系统表结构 (达梦3数据库)
-- =====================================================

-- 部门表
CREATE TABLE SYS_DEPARTMENT (
    DEPT_ID VARCHAR(32) PRIMARY KEY,
    DEPT_NAME VARCHAR(100) NOT NULL,
    DEPT_CODE VARCHAR(50) NOT NULL UNIQUE,
    PARENT_ID VARCHAR(32),
    ORDER_NUM INTEGER DEFAULT 0,
    LEADER VARCHAR(100),
    PHONE VARCHAR(20),
    EMAIL VARCHAR(100),
    STATUS CHAR(1) DEFAULT '1',
    CREATE_TIME TIMESTAMP DEFAULT SYSDATE,
    UPDATE_TIME TIMESTAMP DEFAULT SYSDATE,
    REMARK VARCHAR(500)
);

-- 角色表
CREATE TABLE SYS_ROLE (
    ROLE_ID VARCHAR(32) PRIMARY KEY,
    ROLE_NAME VARCHAR(100) NOT NULL,
    ROLE_KEY VARCHAR(100) NOT NULL UNIQUE,
    ROLE_SORT INTEGER DEFAULT 0,
    STATUS CHAR(1) DEFAULT '1',
    CREATE_TIME TIMESTAMP DEFAULT SYSDATE,
    UPDATE_TIME TIMESTAMP DEFAULT SYSDATE,
    REMARK VARCHAR(500)
);

-- 用户表
CREATE TABLE SYS_USER (
    USER_ID VARCHAR(32) PRIMARY KEY,
    USERNAME VARCHAR(50) NOT NULL UNIQUE,
    PASSWORD VARCHAR(255) NOT NULL,
    REAL_NAME VARCHAR(100) NOT NULL,
    EMAIL VARCHAR(100),
    PHONE VARCHAR(20),
    DEPARTMENT_ID VARCHAR(32),
    STATUS CHAR(1) DEFAULT '1',
    LOGIN_IP VARCHAR(128),
    LOGIN_DATE TIMESTAMP,
    CREATE_TIME TIMESTAMP DEFAULT SYSDATE,
    UPDATE_TIME TIMESTAMP DEFAULT SYSDATE,
    REMARK VARCHAR(500)
);

-- 用户角色关联表
CREATE TABLE SYS_USER_ROLE (
    USER_ID VARCHAR(32) NOT NULL,
    ROLE_ID VARCHAR(32) NOT NULL,
    PRIMARY KEY (USER_ID, ROLE_ID)
);

-- 公文表
CREATE TABLE DOC_DOCUMENT (
    DOC_ID VARCHAR(32) PRIMARY KEY,
    DOC_TITLE VARCHAR(200) NOT NULL,
    DOC_NUMBER VARCHAR(100) UNIQUE,
    DOC_TYPE VARCHAR(50) NOT NULL,
    DOC_LEVEL VARCHAR(20),
    DOC_CONTENT CLOB,
    DOC_STATUS VARCHAR(20) DEFAULT 'DRAFT',
    CREATE_USER_ID VARCHAR(32) NOT NULL,
    CREATE_DEPT_ID VARCHAR(32) NOT NULL,
    CREATE_TIME TIMESTAMP DEFAULT SYSDATE,
    UPDATE_TIME TIMESTAMP DEFAULT SYSDATE,
    CURRENT_HANDLER VARCHAR(32),
    CURRENT_STEP VARCHAR(50),
    DEADLINE TIMESTAMP,
    REMARK VARCHAR(500)
);

-- 公文流转记录表
CREATE TABLE DOC_FLOW_RECORD (
    RECORD_ID VARCHAR(32) PRIMARY KEY,
    DOC_ID VARCHAR(32) NOT NULL,
    FROM_USER_ID VARCHAR(32),
    TO_USER_ID VARCHAR(32),
    FROM_DEPT_ID VARCHAR(32),
    TO_DEPT_ID VARCHAR(32),
    FLOW_STEP VARCHAR(50) NOT NULL,
    ACTION VARCHAR(20) NOT NULL,
    OPINION CLOB,
    PROCESS_TIME TIMESTAMP DEFAULT SYSDATE,
    REMARK VARCHAR(500)
);

-- 公文附件表
CREATE TABLE DOC_ATTACHMENT (
    ATTACHMENT_ID VARCHAR(32) PRIMARY KEY,
    DOC_ID VARCHAR(32) NOT NULL,
    FILE_NAME VARCHAR(200) NOT NULL,
    FILE_PATH VARCHAR(500) NOT NULL,
    FILE_SIZE BIGINT,
    FILE_TYPE VARCHAR(50),
    UPLOAD_USER_ID VARCHAR(32),
    UPLOAD_TIME TIMESTAMP DEFAULT SYSDATE
);

-- 公文签收记录表
CREATE TABLE DOC_RECEIVE_RECORD (
    RECEIVE_ID VARCHAR(32) PRIMARY KEY,
    DOC_ID VARCHAR(32) NOT NULL,
    RECEIVE_USER_ID VARCHAR(32) NOT NULL,
    RECEIVE_DEPT_ID VARCHAR(32) NOT NULL,
    RECEIVE_TIME TIMESTAMP DEFAULT SYSDATE,
    READ_TIME TIMESTAMP,
    STATUS CHAR(1) DEFAULT '0',
    REMARK VARCHAR(500)
);

-- 流程配置表
CREATE TABLE SYS_FLOW_CONFIG (
    CONFIG_ID VARCHAR(32) PRIMARY KEY,
    FLOW_NAME VARCHAR(100) NOT NULL,
    FLOW_KEY VARCHAR(100) NOT NULL UNIQUE,
    FLOW_STEPS CLOB,
    STATUS CHAR(1) DEFAULT '1',
    CREATE_TIME TIMESTAMP DEFAULT SYSDATE,
    UPDATE_TIME TIMESTAMP DEFAULT SYSDATE
);

-- 系统参数配置表
CREATE TABLE SYS_CONFIG (
    CONFIG_ID VARCHAR(32) PRIMARY KEY,
    CONFIG_NAME VARCHAR(100) NOT NULL,
    CONFIG_KEY VARCHAR(100) NOT NULL UNIQUE,
    CONFIG_VALUE VARCHAR(500),
    CONFIG_TYPE CHAR(1) DEFAULT 'N',
    CREATE_TIME TIMESTAMP DEFAULT SYSDATE,
    UPDATE_TIME TIMESTAMP DEFAULT SYSDATE,
    REMARK VARCHAR(500)
);

-- 公文类型字典表
CREATE TABLE SYS_DOC_TYPE (
    TYPE_ID VARCHAR(32) PRIMARY KEY,
    TYPE_NAME VARCHAR(100) NOT NULL,
    TYPE_CODE VARCHAR(50) NOT NULL UNIQUE,
    PARENT_ID VARCHAR(32),
    ORDER_NUM INTEGER DEFAULT 0,
    STATUS CHAR(1) DEFAULT '1',
    CREATE_TIME TIMESTAMP DEFAULT SYSDATE,
    REMARK VARCHAR(500)
);

-- 系统日志表
CREATE TABLE SYS_LOG (
    LOG_ID VARCHAR(32) PRIMARY KEY,
    LOG_TYPE VARCHAR(20),
    LOG_TITLE VARCHAR(200),
    BUSINESS_TYPE VARCHAR(20),
    METHOD VARCHAR(100),
    REQUEST_METHOD VARCHAR(10),
    OPERATOR_TYPE VARCHAR(20),
    OPER_NAME VARCHAR(50),
    DEPT_NAME VARCHAR(50),
    OPER_URL VARCHAR(255),
    OPER_IP VARCHAR(128),
    OPER_LOCATION VARCHAR(255),
    OPER_PARAM VARCHAR(2000),
    JSON_RESULT VARCHAR(2000),
    STATUS CHAR(1) DEFAULT '0',
    ERROR_MSG VARCHAR(2000),
    OPER_TIME TIMESTAMP DEFAULT SYSDATE
);

-- 外键约束
ALTER TABLE SYS_DEPARTMENT ADD CONSTRAINT FK_DEPT_PARENT 
    FOREIGN KEY (PARENT_ID) REFERENCES SYS_DEPARTMENT(DEPT_ID);

ALTER TABLE SYS_USER ADD CONSTRAINT FK_USER_DEPT 
    FOREIGN KEY (DEPARTMENT_ID) REFERENCES SYS_DEPARTMENT(DEPT_ID);

ALTER TABLE SYS_USER_ROLE ADD CONSTRAINT FK_USER_ROLE_USER 
    FOREIGN KEY (USER_ID) REFERENCES SYS_USER(USER_ID);
ALTER TABLE SYS_USER_ROLE ADD CONSTRAINT FK_USER_ROLE_ROLE 
    FOREIGN KEY (ROLE_ID) REFERENCES SYS_ROLE(ROLE_ID);

ALTER TABLE DOC_DOCUMENT ADD CONSTRAINT FK_DOC_CREATE_USER 
    FOREIGN KEY (CREATE_USER_ID) REFERENCES SYS_USER(USER_ID);
ALTER TABLE DOC_DOCUMENT ADD CONSTRAINT FK_DOC_CREATE_DEPT 
    FOREIGN KEY (CREATE_DEPT_ID) REFERENCES SYS_DEPARTMENT(DEPT_ID);
ALTER TABLE DOC_DOCUMENT ADD CONSTRAINT FK_DOC_CURRENT_HANDLER 
    FOREIGN KEY (CURRENT_HANDLER) REFERENCES SYS_USER(USER_ID);

ALTER TABLE DOC_FLOW_RECORD ADD CONSTRAINT FK_FLOW_DOC 
    FOREIGN KEY (DOC_ID) REFERENCES DOC_DOCUMENT(DOC_ID);
ALTER TABLE DOC_FLOW_RECORD ADD CONSTRAINT FK_FLOW_FROM_USER 
    FOREIGN KEY (FROM_USER_ID) REFERENCES SYS_USER(USER_ID);
ALTER TABLE DOC_FLOW_RECORD ADD CONSTRAINT FK_FLOW_TO_USER 
    FOREIGN KEY (TO_USER_ID) REFERENCES SYS_USER(USER_ID);

ALTER TABLE DOC_ATTACHMENT ADD CONSTRAINT FK_ATTACH_DOC 
    FOREIGN KEY (DOC_ID) REFERENCES DOC_DOCUMENT(DOC_ID);
ALTER TABLE DOC_ATTACHMENT ADD CONSTRAINT FK_ATTACH_USER 
    FOREIGN KEY (UPLOAD_USER_ID) REFERENCES SYS_USER(USER_ID);

ALTER TABLE DOC_RECEIVE_RECORD ADD CONSTRAINT FK_RECEIVE_DOC 
    FOREIGN KEY (DOC_ID) REFERENCES DOC_DOCUMENT(DOC_ID);
ALTER TABLE DOC_RECEIVE_RECORD ADD CONSTRAINT FK_RECEIVE_USER 
    FOREIGN KEY (RECEIVE_USER_ID) REFERENCES SYS_USER(USER_ID);
ALTER TABLE DOC_RECEIVE_RECORD ADD CONSTRAINT FK_RECEIVE_DEPT 
    FOREIGN KEY (RECEIVE_DEPT_ID) REFERENCES SYS_DEPARTMENT(DEPT_ID);

ALTER TABLE SYS_DOC_TYPE ADD CONSTRAINT FK_DOC_TYPE_PARENT 
    FOREIGN KEY (PARENT_ID) REFERENCES SYS_DOC_TYPE(TYPE_ID);

-- 检查约束
ALTER TABLE SYS_USER ADD CONSTRAINT CHK_USER_STATUS 
    CHECK (STATUS IN ('0', '1'));
ALTER TABLE SYS_ROLE ADD CONSTRAINT CHK_ROLE_STATUS 
    CHECK (STATUS IN ('0', '1'));
ALTER TABLE SYS_DEPARTMENT ADD CONSTRAINT CHK_DEPT_STATUS 
    CHECK (STATUS IN ('0', '1'));
ALTER TABLE DOC_DOCUMENT ADD CONSTRAINT CHK_DOC_STATUS 
    CHECK (DOC_STATUS IN ('DRAFT', 'PROCESSING', 'APPROVED', 'REJECTED', 'COMPLETED'));
ALTER TABLE DOC_DOCUMENT ADD CONSTRAINT CHK_DOC_LEVEL 
    CHECK (DOC_LEVEL IN ('普通', '紧急', '特急'));
ALTER TABLE DOC_FLOW_RECORD ADD CONSTRAINT CHK_FLOW_ACTION 
    CHECK (ACTION IN ('SUBMIT', 'APPROVE', 'REJECT', 'EDIT', 'SEND', 'RECEIVE', 'TRANSFER'));
ALTER TABLE DOC_FLOW_RECORD ADD CONSTRAINT CHK_FLOW_STEP 
    CHECK (FLOW_STEP IN ('DRAFT', 'OFFICE_EDIT', 'VICE_REVIEW', 'DIRECTOR_APPROVE', 'OFFICE_SEND', 'DEPT_RECEIVE'));
ALTER TABLE DOC_RECEIVE_RECORD ADD CONSTRAINT CHK_RECEIVE_STATUS 
    CHECK (STATUS IN ('0', '1'));
ALTER TABLE SYS_CONFIG ADD CONSTRAINT CHK_CONFIG_TYPE 
    CHECK (CONFIG_TYPE IN ('Y', 'N'));
ALTER TABLE SYS_DOC_TYPE ADD CONSTRAINT CHK_DOC_TYPE_STATUS 
    CHECK (STATUS IN ('0', '1'));
ALTER TABLE SYS_FLOW_CONFIG ADD CONSTRAINT CHK_FLOW_CONFIG_STATUS 
    CHECK (STATUS IN ('0', '1'));

COMMIT;
