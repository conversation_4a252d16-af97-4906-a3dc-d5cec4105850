package com.jinli.gongwen.common;

/**
 * 返回状态码枚举
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
public enum ResultCode {
    
    SUCCESS(200, "操作成功"),
    ERROR(500, "操作失败"),
    VALIDATE_FAILED(400, "参数检验失败"),
    UNAUTHORIZED(401, "暂未登录或token已经过期"),
    FORBIDDEN(403, "没有相关权限"),
    NOT_FOUND(404, "资源不存在"),
    
    // 用户相关
    USER_NOT_FOUND(1001, "用户不存在"),
    USER_PASSWORD_ERROR(1002, "用户名或密码错误"),
    USER_ACCOUNT_LOCKED(1003, "账户已被锁定"),
    USER_ACCOUNT_DISABLED(1004, "账户已被禁用"),
    USER_ALREADY_EXISTS(1005, "用户已存在"),
    
    // 公文相关
    DOCUMENT_NOT_FOUND(2001, "公文不存在"),
    DOCUMENT_STATUS_ERROR(2002, "公文状态错误"),
    DOCUMENT_PERMISSION_DENIED(2003, "无权限操作此公文"),
    DOCUMENT_ALREADY_PROCESSED(2004, "公文已被处理"),
    
    // 流程相关
    FLOW_STEP_ERROR(3001, "流程步骤错误"),
    FLOW_PERMISSION_DENIED(3002, "无权限执行此流程操作"),
    FLOW_ALREADY_COMPLETED(3003, "流程已完成"),
    
    // 文件相关
    FILE_UPLOAD_ERROR(4001, "文件上传失败"),
    FILE_TYPE_ERROR(4002, "文件类型不支持"),
    FILE_SIZE_ERROR(4003, "文件大小超出限制"),
    FILE_NOT_FOUND(4004, "文件不存在");

    private final Integer code;
    private final String message;

    ResultCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
