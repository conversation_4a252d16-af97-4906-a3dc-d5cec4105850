package com.jinli.service.impl;

import com.jinli.entity.SysDepartment;
import com.jinli.gongwen.mapper.SysDepartmentMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

/**
 * 部门服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-30
 */
@Service("sysDepartmentService")
public class SysDepartmentServiceSimple {

    private static final Logger log = LoggerFactory.getLogger(SysDepartmentServiceSimple.class);

    @Autowired
    private SysDepartmentMapper departmentMapper;

    /**
     * 查询所有启用的部门列表
     */
    public List<SysDepartment> selectAllEnabledDepartments() {
        log.info("查询所有启用的部门列表");
        
        try {
            List<com.jinli.gongwen.entity.SysDepartment> gongwenDepartments = departmentMapper.selectAllEnabledDepartments();
            
            List<SysDepartment> departments = new ArrayList<>();
            for (com.jinli.gongwen.entity.SysDepartment gongwenDept : gongwenDepartments) {
                SysDepartment dept = convertToNewDepartment(gongwenDept);
                departments.add(dept);
            }
            
            return departments;
        } catch (Exception e) {
            log.error("查询部门列表失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 转换部门实体
     */
    private SysDepartment convertToNewDepartment(com.jinli.gongwen.entity.SysDepartment gongwenDept) {
        SysDepartment dept = new SysDepartment();
        dept.setDeptId(gongwenDept.getDeptId());
        dept.setDeptName(gongwenDept.getDeptName());
        dept.setDeptCode(gongwenDept.getDeptCode());
        dept.setParentId(gongwenDept.getParentId());
        dept.setLeader(gongwenDept.getLeader());
        dept.setPhone(gongwenDept.getPhone());
        dept.setEmail(gongwenDept.getEmail());
        dept.setSort(gongwenDept.getOrderNum());
        dept.setStatus(gongwenDept.getStatus());
        dept.setRemark(gongwenDept.getRemark());
        
        // 转换Date到LocalDateTime
        if (gongwenDept.getCreateTime() != null) {
            dept.setCreateTime(gongwenDept.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
        }
        if (gongwenDept.getUpdateTime() != null) {
            dept.setUpdateTime(gongwenDept.getUpdateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
        }
        
        return dept;
    }
}
