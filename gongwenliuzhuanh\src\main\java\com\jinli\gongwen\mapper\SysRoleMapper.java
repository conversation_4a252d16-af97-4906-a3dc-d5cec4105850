package com.jinli.gongwen.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinli.gongwen.entity.SysRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色信息表 Mapper 接口
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
@Mapper
public interface SysRoleMapper extends BaseMapper<SysRole> {

    /**
     * 分页查询角色列表
     * 
     * @param page 分页参数
     * @param roleName 角色名称（模糊查询）
     * @param roleKey 角色标识（模糊查询）
     * @param status 状态
     * @return 角色列表
     */
    IPage<SysRole> selectRolePage(Page<SysRole> page, 
                                  @Param("roleName") String roleName,
                                  @Param("roleKey") String roleKey,
                                  @Param("status") String status);

    /**
     * 根据角色ID查询角色详细信息
     * 
     * @param roleId 角色ID
     * @return 角色信息
     */
    SysRole selectRoleDetailById(@Param("roleId") String roleId);

    /**
     * 检查角色名称是否唯一
     * 
     * @param roleName 角色名称
     * @param roleId 角色ID（排除自己）
     * @return 数量
     */
    Integer checkRoleNameUnique(@Param("roleName") String roleName, @Param("roleId") String roleId);

    /**
     * 检查角色标识是否唯一
     * 
     * @param roleKey 角色标识
     * @param roleId 角色ID（排除自己）
     * @return 数量
     */
    Integer checkRoleKeyUnique(@Param("roleKey") String roleKey, @Param("roleId") String roleId);

    /**
     * 查询所有启用的角色
     * 
     * @return 角色列表
     */
    List<SysRole> selectAllEnabledRoles();

    /**
     * 根据用户ID查询角色列表
     * 
     * @param userId 用户ID
     * @return 角色列表
     */
    List<SysRole> selectRolesByUserId(@Param("userId") String userId);

    /**
     * 统计角色下的用户数量
     * 
     * @param roleId 角色ID
     * @return 用户数量
     */
    Integer countUsersByRoleId(@Param("roleId") String roleId);
}
