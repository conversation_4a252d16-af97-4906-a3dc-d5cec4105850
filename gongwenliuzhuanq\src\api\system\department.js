import request from '@/utils/request'

// 获取所有启用的部门列表
export function getAllDepartments() {
  return request({
    url: '/system/department/all',
    method: 'get'
  })
}

// 获取部门列表
export function getDepartmentList(params) {
  return request({
    url: '/system/department/list',
    method: 'get',
    params
  })
}

// 获取部门详情
export function getDepartmentDetail(deptId) {
  return request({
    url: `/system/department/${deptId}`,
    method: 'get'
  })
}

// 新增部门
export function createDepartment(data) {
  return request({
    url: '/system/department',
    method: 'post',
    data
  })
}

// 修改部门
export function updateDepartment(data) {
  return request({
    url: '/system/department',
    method: 'put',
    data
  })
}

// 删除部门
export function deleteDepartment(deptId) {
  return request({
    url: `/system/department/${deptId}`,
    method: 'delete'
  })
}

// 启用/停用部门
export function changeDepartmentStatus(deptId, status) {
  return request({
    url: `/system/department/${deptId}/status`,
    method: 'put',
    params: { status }
  })
}
