# 河北金力集团公文流转系统

## 项目简介

河北金力集团公文流转系统是一个基于Spring Boot + Vue.js的前后端分离项目，专为企业公文流转管理而设计。系统支持完整的公文生命周期管理，包括拟制、审核、审签、发送、签收等环节。

## 技术栈

### 后端技术
- **框架**: Spring Boot 2.7.0
- **数据库**: 达梦数据库 DM3
- **ORM**: MyBatis Plus 3.5.2
- **安全**: Spring Security + JWT
- **文档**: Swagger 3.0
- **工具**: Hutool、FastJSON

### 前端技术
- **框架**: Vue.js 3.2
- **构建工具**: Vite 3.0
- **UI库**: Element Plus 2.2
- **路由**: Vue Router 4.1
- **状态管理**: Pinia 2.0
- **图表**: ECharts 5.4

## 项目结构

```
├── gongwenliuzhuanh/          # 后端Spring Boot项目
│   ├── src/main/java/
│   │   └── com/jinli/gongwen/
│   │       ├── common/        # 通用类
│   │       ├── config/        # 配置类
│   │       ├── controller/    # 控制器
│   │       ├── entity/        # 实体类
│   │       ├── mapper/        # Mapper接口
│   │       ├── service/       # 服务层
│   │       └── util/          # 工具类
│   └── src/main/resources/
│       ├── mapper/            # MyBatis XML映射文件
│       └── application.properties
├── gongwenliuzhuanq/          # 前端Vue.js项目
│   ├── src/
│   │   ├── api/               # API接口
│   │   ├── components/        # 公共组件
│   │   ├── layout/            # 布局组件
│   │   ├── router/            # 路由配置
│   │   ├── stores/            # 状态管理
│   │   ├── styles/            # 样式文件
│   │   ├── utils/             # 工具函数
│   │   └── views/             # 页面组件
│   └── package.json
└── gongwen_tables_dm3.sql     # 数据库表结构
```

## 功能特性

### 用户角色管理
- **系统管理员**: 拥有所有权限，管理用户、角色、部门
- **厂长**: 负责最终审签，查看所有公文
- **副厂长**: 按专业分工审核相关公文（生产、销售、财务）
- **办公室**: 负责公文修改、格式规范、发送流转
- **部门用户**: 拟制公文、签收公文

### 公文流转流程
1. **部门拟稿** → 部门用户创建公文草稿
2. **办公室修改** → 办公室规范格式、修改内容
3. **副厂长审核** → 相关副厂长审核公文内容
4. **厂长审签** → 厂长最终审批签发
5. **办公室发送** → 办公室发送到目标部门
6. **部门签收** → 目标部门签收确认

### 核心功能
- ✅ 公文拟制、编辑、删除
- ✅ 公文流转、审核、审签
- ✅ 公文发送、签收管理
- ✅ 公文查询、搜索、统计
- ✅ 用户权限和角色管理
- ✅ 部门组织架构管理
- ✅ 系统日志和操作记录
- ✅ 文件附件上传下载

## 快速开始

### 环境要求
- JDK 8+
- Node.js 16+
- 达梦数据库 DM3
- Maven 3.6+

### 数据库初始化
1. 安装并启动达梦数据库
2. 执行 `gongwen_tables_dm3.sql` 创建数据库表结构
3. 修改后端配置文件中的数据库连接信息

### 后端启动
```bash
cd gongwenliuzhuanh
mvn clean install
mvn spring-boot:run
```

后端服务将在 http://localhost:8080 启动

### 前端启动
```bash
cd gongwenliuzhuanq
npm install
npm run dev
```

前端应用将在 http://localhost:3000 启动

## 测试账号

| 角色 | 用户名 | 密码 | 说明 |
|------|--------|------|------|
| 系统管理员 | admin | 123456 | 拥有所有权限 |
| 厂长 | director | 123456 | 最终审签权限 |
| 生产副厂长 | vice_prod | 123456 | 生产相关公文审核 |
| 销售副厂长 | vice_sales | 123456 | 销售相关公文审核 |
| 财务副厂长 | vice_finance | 123456 | 财务相关公文审核 |
| 办公室 | office | 123456 | 公文修改和发送 |
| 主厂区用户 | main_user | 123456 | 部门用户权限 |

## API文档

启动后端服务后，访问 http://localhost:8080/api/swagger-ui/index.html 查看完整的API文档。

## 开发说明

### 后端开发
- 使用MyBatis Plus进行数据库操作
- 统一的返回结果封装（Result类）
- JWT token认证和权限控制
- 全局异常处理和日志记录

### 前端开发
- 使用Vue 3 Composition API
- Element Plus组件库
- Axios请求拦截和响应处理
- 路由守卫和权限控制

## 部署说明

### 生产环境部署
1. 修改数据库连接配置
2. 构建前端项目：`npm run build`
3. 打包后端项目：`mvn clean package`
4. 部署到服务器并启动服务

### Docker部署（可选）
项目支持Docker容器化部署，具体配置文件可根据需要添加。

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。

## 联系方式

如有问题或建议，请联系项目维护者。

---

© 2025 河北金力集团. All rights reserved.
