package com.gongwenliuzhuanh;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

@SpringBootApplication(scanBasePackages = "com.jinli.gongwen", exclude = {
    org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration.class
})
@MapperScan("com.jinli.gongwen.mapper")
@RestController
public class GongwenliuzhuanhApplication {

	public static void main(String[] args) {
		SpringApplication.run(GongwenliuzhuanhApplication.class, args);
	}

	/**
	 * 简单的测试接口
	 */
	@GetMapping("/test/hello")
	@CrossOrigin(origins = "*", allowCredentials = "false")
	public Map<String, Object> hello(HttpServletResponse response) {
		// 手动设置CORS头
		response.setHeader("Access-Control-Allow-Origin", "*");
		response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
		response.setHeader("Access-Control-Allow-Headers", "*");

		Map<String, Object> result = new HashMap<>();
		result.put("message", "Hello World!");
		result.put("timestamp", System.currentTimeMillis());
		result.put("status", "success");
		return result;
	}

	/**
	 * 登录测试接口
	 */
	@PostMapping("/test/login")
	@CrossOrigin(origins = "*", allowCredentials = "false")
	public Map<String, Object> testLogin(@RequestBody Map<String, Object> request, HttpServletResponse response) {
		// 手动设置CORS头
		response.setHeader("Access-Control-Allow-Origin", "*");
		response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
		response.setHeader("Access-Control-Allow-Headers", "*");

		Map<String, Object> result = new HashMap<>();
		result.put("code", 200);
		result.put("message", "登录成功");

		Map<String, Object> data = new HashMap<>();
		data.put("token", "test-token-123456");
		data.put("userInfo", request);
		result.put("data", data);

		return result;
	}

	/**
	 * OPTIONS请求处理
	 */
	@RequestMapping(value = "/**", method = RequestMethod.OPTIONS)
	public void options(HttpServletResponse response) {
		response.setHeader("Access-Control-Allow-Origin", "*");
		response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
		response.setHeader("Access-Control-Allow-Headers", "*");
		response.setHeader("Access-Control-Max-Age", "3600");
		response.setStatus(200);
	}

	/**
	 * 获取用户信息接口（测试用）
	 */
	@GetMapping("/test/userinfo")
	@CrossOrigin(origins = "*", allowCredentials = "false")
	public Map<String, Object> getTestUserInfo(@RequestParam(required = false) String userId, HttpServletResponse response) {
		// 手动设置CORS头
		response.setHeader("Access-Control-Allow-Origin", "*");
		response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
		response.setHeader("Access-Control-Allow-Headers", "*");

		Map<String, Object> result = new HashMap<>();
		result.put("code", 200);
		result.put("message", "获取用户信息成功");

		Map<String, Object> userInfo = new HashMap<>();
		userInfo.put("userId", "USER001");
		userInfo.put("username", "admin");
		userInfo.put("realName", "系统管理员");
		userInfo.put("email", "<EMAIL>");
		userInfo.put("phone", "13800000001");
		userInfo.put("departmentId", "DEPT002");
		userInfo.put("departmentName", "办公室");
		userInfo.put("roles", new String[]{"系统管理员"});

		result.put("data", userInfo);
		return result;
	}

	/**
	 * 获取文档统计信息（测试用）
	 */
	@GetMapping("/test/document/statistics")
	@CrossOrigin(origins = "*", allowCredentials = "false")
	public Map<String, Object> getTestDocumentStatistics(HttpServletResponse response) {
		// 手动设置CORS头
		response.setHeader("Access-Control-Allow-Origin", "*");
		response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
		response.setHeader("Access-Control-Allow-Headers", "*");

		Map<String, Object> result = new HashMap<>();
		result.put("code", 200);
		result.put("message", "获取统计信息成功");

		Map<String, Object> statistics = new HashMap<>();
		statistics.put("totalDocuments", 156);
		statistics.put("pendingDocuments", 23);
		statistics.put("completedDocuments", 133);
		statistics.put("todayDocuments", 8);

		result.put("data", statistics);
		return result;
	}

	/**
	 * 获取待处理公文列表（测试用）
	 */
	@GetMapping("/test/document/pending")
	@CrossOrigin(origins = "*", allowCredentials = "false")
	public Map<String, Object> getTestPendingDocuments(
			@RequestParam(defaultValue = "1") int pageNum,
			@RequestParam(defaultValue = "10") int pageSize,
			HttpServletResponse response) {

		// 手动设置CORS头
		response.setHeader("Access-Control-Allow-Origin", "*");
		response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
		response.setHeader("Access-Control-Allow-Headers", "*");

		Map<String, Object> result = new HashMap<>();
		result.put("code", 200);
		result.put("message", "获取待处理公文成功");

		// 模拟分页数据
		Map<String, Object> pageData = new HashMap<>();
		pageData.put("current", pageNum);
		pageData.put("size", pageSize);
		pageData.put("total", 23);
		pageData.put("pages", 3);

		// 模拟公文列表
		Map<String, Object>[] documents = new Map[Math.min(pageSize, 5)];
		for (int i = 0; i < documents.length; i++) {
			Map<String, Object> doc = new HashMap<>();
			doc.put("docId", "DOC00" + (i + 1));
			doc.put("docTitle", "关于" + (i == 0 ? "加强安全生产管理" : i == 1 ? "采购新设备" : i == 2 ? "召开年度工作会议" : i == 3 ? "员工培训计划" : "质量检验标准修订") + "的" + (i % 2 == 0 ? "通知" : "请示"));
			doc.put("docType", i % 2 == 0 ? "NOTICE" : "REQUEST");
			doc.put("docLevel", i == 0 || i == 2 ? "紧急" : "普通");
			doc.put("createUserName", i % 2 == 0 ? "李主任" : "王部长");
			doc.put("createTime", System.currentTimeMillis() - (i + 1) * 86400000L);
			doc.put("deadline", System.currentTimeMillis() + (7 - i) * 86400000L);
			doc.put("currentStep", "待审核");
			documents[i] = doc;
		}

		pageData.put("records", documents);
		result.put("data", pageData);
		return result;
	}

	/**
	 * 获取公文列表（分页，测试用）
	 */
	@GetMapping("/test/document/page")
	@CrossOrigin(origins = "*", allowCredentials = "false")
	public Map<String, Object> getTestDocumentPage(
			@RequestParam(defaultValue = "1") int pageNum,
			@RequestParam(defaultValue = "10") int pageSize,
			HttpServletResponse response) {

		// 手动设置CORS头
		response.setHeader("Access-Control-Allow-Origin", "*");
		response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
		response.setHeader("Access-Control-Allow-Headers", "*");

		Map<String, Object> result = new HashMap<>();
		result.put("code", 200);
		result.put("message", "获取公文列表成功");

		// 模拟分页数据
		Map<String, Object> pageData = new HashMap<>();
		pageData.put("current", pageNum);
		pageData.put("size", pageSize);
		pageData.put("total", 156);
		pageData.put("pages", 16);

		// 模拟公文列表
		Map<String, Object>[] documents = new Map[Math.min(pageSize, 5)];
		for (int i = 0; i < documents.length; i++) {
			Map<String, Object> doc = new HashMap<>();
			doc.put("docId", "REC00" + (i + 1)); // 使用REC前缀区分最近公文
			doc.put("docTitle", "关于" + (i == 0 ? "2025年第一季度生产计划" : i == 1 ? "财务预算执行情况" : i == 2 ? "技术改造项目" : i == 3 ? "物流配送优化方案" : "安全生产管理") + "的" + (i % 3 == 0 ? "报告" : i % 3 == 1 ? "意见" : "通知"));
			doc.put("docType", i % 3 == 0 ? "REPORT" : i % 3 == 1 ? "OPINION" : "NOTICE");
			doc.put("docLevel", i % 2 == 0 ? "普通" : "紧急");
			doc.put("docStatus", i % 4 == 0 ? "COMPLETED" : i % 4 == 1 ? "PROCESSING" : i % 4 == 2 ? "APPROVED" : "DRAFT");
			doc.put("createUserName", i % 2 == 0 ? "张部长" : "李主任");
			doc.put("createTime", System.currentTimeMillis() - (i + 1) * 86400000L);
			doc.put("updateTime", System.currentTimeMillis() - i * 43200000L);
			documents[i] = doc;
		}

		pageData.put("records", documents);
		result.put("data", pageData);
		return result;
	}

	/**
	 * 根据ID获取公文详情（测试用）
	 */
	@GetMapping("/test/document/{docId}")
	@CrossOrigin(origins = "*", allowCredentials = "false")
	public Map<String, Object> getTestDocumentDetail(@PathVariable String docId, HttpServletResponse response) {
		// 手动设置CORS头
		response.setHeader("Access-Control-Allow-Origin", "*");
		response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
		response.setHeader("Access-Control-Allow-Headers", "*");

		Map<String, Object> result = new HashMap<>();
		result.put("code", 200);
		result.put("message", "获取公文详情成功");

		// 根据docId返回不同的公文内容
		Map<String, Object> document = new HashMap<>();
		document.put("docId", docId);

		// 根据不同的docId设置不同的内容
		if ("DOC001".equals(docId)) {
			document.put("docTitle", "关于加强安全生产管理的通知");
			document.put("docType", "NOTICE");
			document.put("docLevel", "紧急");
			document.put("docStatus", "PROCESSING");
			document.put("createUserName", "李主任");
			document.put("createTime", System.currentTimeMillis() - 2 * 24 * 60 * 60 * 1000L);
			document.put("updateTime", System.currentTimeMillis() - 1 * 24 * 60 * 60 * 1000L);
			document.put("deadline", System.currentTimeMillis() + 5 * 24 * 60 * 60 * 1000L);
			document.put("content",
				"<p>各部门、各单位：</p>" +
				"<p>为进一步加强安全生产管理，确保企业安全稳定运行，现就有关事项通知如下：</p>" +
				"<p><strong>一、提高安全意识，严格落实安全生产责任制</strong></p>" +
				"<p>各部门要高度重视安全生产工作，严格按照'谁主管、谁负责'的原则，层层落实安全生产责任制。</p>" +
				"<p><strong>二、加强安全检查，及时消除安全隐患</strong></p>" +
				"<p>定期开展安全检查，发现问题及时整改，确保生产环境安全。</p>" +
				"<p><strong>三、完善应急预案，提高应急处置能力</strong></p>" +
				"<p>建立健全应急预案，加强应急演练，提高突发事件处置能力。</p>" +
				"<p>请各部门认真贯彻执行。</p>" +
				"<p style='text-align: right;'>河北金力集团<br/>2025年7月21日</p>"
			);
		} else if ("DOC002".equals(docId)) {
			document.put("docTitle", "关于采购新设备的请示");
			document.put("docType", "REQUEST");
			document.put("docLevel", "普通");
			document.put("docStatus", "PROCESSING");
			document.put("createUserName", "王部长");
			document.put("createTime", System.currentTimeMillis() - 1 * 24 * 60 * 60 * 1000L);
			document.put("updateTime", System.currentTimeMillis() - 12 * 60 * 60 * 1000L);
			document.put("deadline", System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000L);
			document.put("content",
				"<p>集团领导：</p>" +
				"<p>根据生产发展需要，我部门拟采购以下设备：</p>" +
				"<p><strong>一、设备清单</strong></p>" +
				"<p>1. 数控机床 2台，单价50万元，合计100万元</p>" +
				"<p>2. 检测设备 1套，价格30万元</p>" +
				"<p>3. 辅助工具若干，预算10万元</p>" +
				"<p><strong>二、采购理由</strong></p>" +
				"<p>现有设备老化严重，影响生产效率和产品质量，急需更新换代。</p>" +
				"<p><strong>三、资金来源</strong></p>" +
				"<p>建议从设备更新改造专项资金中列支。</p>" +
				"<p>以上请示，请领导审批。</p>" +
				"<p style='text-align: right;'>生产部<br/>2025年7月20日</p>"
			);
		} else if ("DOC003".equals(docId)) {
			document.put("docTitle", "关于召开年度工作会议的通知");
			document.put("docType", "NOTICE");
			document.put("docLevel", "紧急");
			document.put("docStatus", "APPROVED");
			document.put("createUserName", "李主任");
			document.put("createTime", System.currentTimeMillis() - 3 * 24 * 60 * 60 * 1000L);
			document.put("updateTime", System.currentTimeMillis() - 2 * 24 * 60 * 60 * 1000L);
			document.put("deadline", System.currentTimeMillis() + 3 * 24 * 60 * 60 * 1000L);
			document.put("content",
				"<p>各部门、各单位：</p>" +
				"<p>经研究决定，定于2025年7月25日召开年度工作会议，现将有关事项通知如下：</p>" +
				"<p><strong>一、会议时间</strong></p>" +
				"<p>2025年7月25日（星期四）上午9:00</p>" +
				"<p><strong>二、会议地点</strong></p>" +
				"<p>集团总部三楼会议室</p>" +
				"<p><strong>三、参会人员</strong></p>" +
				"<p>各部门负责人、项目经理及相关人员</p>" +
				"<p><strong>四、会议议程</strong></p>" +
				"<p>1. 年度工作总结报告</p>" +
				"<p>2. 下年度工作计划</p>" +
				"<p>3. 重点项目进展汇报</p>" +
				"<p>请各部门按时参加，不得缺席。</p>" +
				"<p style='text-align: right;'>办公室<br/>2025年7月18日</p>"
			);
		} else if ("REC001".equals(docId)) {
			document.put("docTitle", "关于2025年第一季度生产计划的报告");
			document.put("docType", "REPORT");
			document.put("docLevel", "普通");
			document.put("docStatus", "COMPLETED");
			document.put("createUserName", "张部长");
			document.put("createTime", System.currentTimeMillis() - 1 * 24 * 60 * 60 * 1000L);
			document.put("updateTime", System.currentTimeMillis() - 12 * 60 * 60 * 1000L);
			document.put("deadline", System.currentTimeMillis() + 10 * 24 * 60 * 60 * 1000L);
			document.put("content",
				"<p>集团领导：</p>" +
				"<p>现将2025年第一季度生产计划执行情况报告如下：</p>" +
				"<p><strong>一、生产任务完成情况</strong></p>" +
				"<p>第一季度共完成产品生产12000台，完成率达到105%，超额完成生产任务。</p>" +
				"<p><strong>二、质量控制情况</strong></p>" +
				"<p>产品合格率达到99.2%，较去年同期提升0.8个百分点。</p>" +
				"<p><strong>三、存在问题及改进措施</strong></p>" +
				"<p>1. 部分设备老化，影响生产效率</p>" +
				"<p>2. 建议加快设备更新改造进度</p>" +
				"<p>以上报告，请领导审阅。</p>" +
				"<p style='text-align: right;'>生产部<br/>2025年7月20日</p>"
			);
		} else if ("REC002".equals(docId)) {
			document.put("docTitle", "关于财务预算执行情况的意见");
			document.put("docType", "OPINION");
			document.put("docLevel", "紧急");
			document.put("docStatus", "PROCESSING");
			document.put("createUserName", "李主任");
			document.put("createTime", System.currentTimeMillis() - 2 * 24 * 60 * 60 * 1000L);
			document.put("updateTime", System.currentTimeMillis() - 1 * 24 * 60 * 60 * 1000L);
			document.put("deadline", System.currentTimeMillis() + 5 * 24 * 60 * 60 * 1000L);
			document.put("content",
				"<p>财务部：</p>" +
				"<p>经审查2025年上半年财务预算执行情况，现提出如下意见：</p>" +
				"<p><strong>一、预算执行总体情况</strong></p>" +
				"<p>上半年实际支出与预算基本吻合，执行率为96.8%。</p>" +
				"<p><strong>二、存在的问题</strong></p>" +
				"<p>1. 部分项目支出超预算</p>" +
				"<p>2. 费用控制有待加强</p>" +
				"<p><strong>三、改进建议</strong></p>" +
				"<p>1. 严格执行预算审批制度</p>" +
				"<p>2. 加强成本控制和监督</p>" +
				"<p>请财务部认真研究并落实相关建议。</p>" +
				"<p style='text-align: right;'>审计部<br/>2025年7月19日</p>"
			);
		} else if ("REC003".equals(docId)) {
			document.put("docTitle", "关于技术改造项目的通知");
			document.put("docType", "NOTICE");
			document.put("docLevel", "普通");
			document.put("docStatus", "APPROVED");
			document.put("createUserName", "张部长");
			document.put("createTime", System.currentTimeMillis() - 3 * 24 * 60 * 60 * 1000L);
			document.put("updateTime", System.currentTimeMillis() - 2 * 24 * 60 * 60 * 1000L);
			document.put("deadline", System.currentTimeMillis() + 15 * 24 * 60 * 60 * 1000L);
			document.put("content",
				"<p>各相关部门：</p>" +
				"<p>为提升生产效率和产品质量，决定启动技术改造项目，现将有关事项通知如下：</p>" +
				"<p><strong>一、改造内容</strong></p>" +
				"<p>1. 生产线自动化升级</p>" +
				"<p>2. 质检设备更新</p>" +
				"<p>3. 信息化系统集成</p>" +
				"<p><strong>二、实施时间</strong></p>" +
				"<p>2025年8月1日至2025年10月31日</p>" +
				"<p><strong>三、工作要求</strong></p>" +
				"<p>各部门要积极配合，确保项目顺利实施。</p>" +
				"<p style='text-align: right;'>技术部<br/>2025年7月18日</p>"
			);
		} else {
			// 默认内容
			document.put("docTitle", "公文标题");
			document.put("docType", "NOTICE");
			document.put("docLevel", "普通");
			document.put("docStatus", "DRAFT");
			document.put("createUserName", "系统管理员");
			document.put("createTime", System.currentTimeMillis());
			document.put("updateTime", System.currentTimeMillis());
			document.put("deadline", System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000L);
			document.put("content",
				"<p>这是一份示例公文内容。</p>" +
				"<p>公文编号：" + docId + "</p>" +
				"<p>请根据实际情况填写具体内容。</p>"
			);
		}

		result.put("data", document);
		return result;
	}
}
