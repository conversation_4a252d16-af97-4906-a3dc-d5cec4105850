package com.jinli.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户实体类
 * 
 * <AUTHOR>
 * @since 2025-01-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYS_USER")
public class SysUser implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @TableId(value = "USER_ID", type = IdType.ASSIGN_ID)
    private String userId;

    /**
     * 用户名
     */
    @TableField("USERNAME")
    private String username;

    /**
     * 密码
     */
    @TableField("PASSWORD")
    private String password;

    /**
     * 真实姓名
     */
    @TableField("REAL_NAME")
    private String realName;

    /**
     * 邮箱
     */
    @TableField("EMAIL")
    private String email;

    /**
     * 手机号
     */
    @TableField("PHONE")
    private String phone;

    /**
     * 部门ID
     */
    @TableField("DEPARTMENT_ID")
    private String departmentId;

    /**
     * 用户状态（1正常 0停用）
     */
    @TableField("STATUS")
    private String status;

    /**
     * 登录IP
     */
    @TableField("LOGIN_IP")
    private String loginIp;

    /**
     * 登录时间
     */
    @TableField("LOGIN_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime loginDate;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("UPDATE_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    // 非数据库字段
    /**
     * 部门名称
     */
    @TableField(exist = false)
    private String deptName;

    /**
     * 角色名称
     */
    @TableField(exist = false)
    private String roleName;

    /**
     * 角色标识
     */
    @TableField(exist = false)
    private String roleKey;

    /**
     * 角色ID列表
     */
    @TableField(exist = false)
    private List<String> roleIds;

    /**
     * 权限列表
     */
    @TableField(exist = false)
    private List<String> permissions;

    // 手动添加getter/setter方法（以防Lombok不工作）
    public String getUserId() { return userId; }
    public void setUserId(String userId) { this.userId = userId; }
    
    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }
    
    public String getPassword() { return password; }
    public void setPassword(String password) { this.password = password; }
    
    public String getRealName() { return realName; }
    public void setRealName(String realName) { this.realName = realName; }
    
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    
    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }
    
    public String getDepartmentId() { return departmentId; }
    public void setDepartmentId(String departmentId) { this.departmentId = departmentId; }
    
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    
    public String getLoginIp() { return loginIp; }
    public void setLoginIp(String loginIp) { this.loginIp = loginIp; }
    
    public LocalDateTime getLoginDate() { return loginDate; }
    public void setLoginDate(LocalDateTime loginDate) { this.loginDate = loginDate; }
    
    public LocalDateTime getCreateTime() { return createTime; }
    public void setCreateTime(LocalDateTime createTime) { this.createTime = createTime; }
    
    public LocalDateTime getUpdateTime() { return updateTime; }
    public void setUpdateTime(LocalDateTime updateTime) { this.updateTime = updateTime; }
    
    public String getRemark() { return remark; }
    public void setRemark(String remark) { this.remark = remark; }
    
    public String getDeptName() { return deptName; }
    public void setDeptName(String deptName) { this.deptName = deptName; }
    
    public String getRoleName() { return roleName; }
    public void setRoleName(String roleName) { this.roleName = roleName; }
    
    public String getRoleKey() { return roleKey; }
    public void setRoleKey(String roleKey) { this.roleKey = roleKey; }
    
    public List<String> getRoleIds() { return roleIds; }
    public void setRoleIds(List<String> roleIds) { this.roleIds = roleIds; }
    
    public List<String> getPermissions() { return permissions; }
    public void setPermissions(List<String> permissions) { this.permissions = permissions; }

    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        return "1".equals(status) ? "正常" : "停用";
    }

    /**
     * 检查用户状态是否正常
     */
    public Boolean isNormal() {
        return "1".equals(status);
    }

    /**
     * 检查用户状态是否停用
     */
    public Boolean isDisabled() {
        return "0".equals(status);
    }

    /**
     * 获取格式化的创建时间
     */
    public String getCreateTimeStr() {
        return createTime != null ? createTime.toString() : "";
    }

    /**
     * 获取格式化的更新时间
     */
    public String getUpdateTimeStr() {
        return updateTime != null ? updateTime.toString() : "";
    }

    /**
     * 获取格式化的登录时间
     */
    public String getLoginDateStr() {
        return loginDate != null ? loginDate.toString() : "";
    }

    /**
     * 检查是否为系统管理员
     */
    public Boolean isAdmin() {
        return "ADMIN".equals(roleKey);
    }

    /**
     * 清除敏感信息
     */
    public void clearSensitiveInfo() {
        this.password = null;
    }
}
