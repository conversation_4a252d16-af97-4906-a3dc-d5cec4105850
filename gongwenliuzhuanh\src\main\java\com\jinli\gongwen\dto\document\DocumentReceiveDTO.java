package com.jinli.gongwen.dto.document;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;

/**
 * 公文签收数据传输对象
 */
@Data
@ApiModel("公文签收数据传输对象")
public class DocumentReceiveDTO {

    @ApiModelProperty("公文ID")
    private String docId;

    @ApiModelProperty("签收用户ID")
    private String receiveUserId;

    @ApiModelProperty("签收部门ID")
    private String receiveDeptId;

    @ApiModelProperty("签收意见")
    @Size(max = 500, message = "签收意见长度不能超过500个字符")
    private String receiveOpinion;

    @ApiModelProperty("是否已阅读")
    private Boolean isRead = true;

    @ApiModelProperty("签收状态")
    private String receiveStatus = "RECEIVED"; // PENDING, RECEIVED, READ

    @ApiModelProperty("备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;
}
