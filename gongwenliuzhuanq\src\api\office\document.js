import request from '@/utils/request'

// 获取公文详情
export function getDocumentDetail(docId) {
  return request({
    url: `/api/office/document/${docId}`,
    method: 'get'
  })
}

// 编辑公文
export function editDocument(docId, data) {
  return request({
    url: `/api/office/document/${docId}/edit`,
    method: 'put',
    data
  })
}

// 公文流转
export function flowDocument(docId, data) {
  return request({
    url: `/api/office/document/${docId}/flow`,
    method: 'post',
    data
  })
}

// 删除公文
export function deleteDocument(docId) {
  return request({
    url: `/api/office/document/${docId}`,
    method: 'delete'
  })
}

// 发送公文
export function sendDocument(docId, data) {
  return request({
    url: `/api/office/document/${docId}/send`,
    method: 'post',
    data
  })
}

// 批量发送公文
export function batchSendDocuments(data) {
  return request({
    url: '/api/office/document/batchSend',
    method: 'post',
    data
  })
}

// 获取流转记录
export function getFlowRecords(docId) {
  return request({
    url: `/api/office/document/${docId}/flowRecords`,
    method: 'get'
  })
}

// 退回公文
export function returnDocument(docId, reason) {
  return request({
    url: `/api/office/document/${docId}/return`,
    method: 'post',
    params: { reason }
  })
}

// 获取可发送的部门列表
export function getSendTargets() {
  return request({
    url: '/api/office/sendTargets',
    method: 'get'
  })
}

// 获取发送记录
export function getSendRecords(params) {
  return request({
    url: '/api/office/sendRecords',
    method: 'get',
    params
  })
}
