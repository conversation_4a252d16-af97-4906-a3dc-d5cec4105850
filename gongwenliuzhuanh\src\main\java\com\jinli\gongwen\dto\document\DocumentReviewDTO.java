package com.jinli.gongwen.dto.document;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 公文审核数据传输对象
 */
@Data
@ApiModel("公文审核数据传输对象")
public class DocumentReviewDTO {

    @ApiModelProperty("公文ID")
    private String docId;

    @ApiModelProperty("审核用户ID")
    private String reviewUserId;

    @ApiModelProperty(value = "审核结果", required = true)
    @NotNull(message = "审核结果不能为空")
    private Boolean isApproved;

    @ApiModelProperty(value = "审核意见", required = true)
    @NotBlank(message = "审核意见不能为空")
    @Size(max = 500, message = "审核意见长度不能超过500个字符")
    private String reviewOpinion;

    @ApiModelProperty("审核类型")
    private String reviewType = "VICE_REVIEW"; // VICE_REVIEW, DIRECTOR_APPROVE

    @ApiModelProperty("紧急程度调整")
    private String urgencyAdjustment;

    @ApiModelProperty("建议处理方式")
    private String suggestedAction;

    @ApiModelProperty("是否需要补充材料")
    private Boolean needSupplementary = false;

    @ApiModelProperty("补充材料说明")
    @Size(max = 500, message = "补充材料说明长度不能超过500个字符")
    private String supplementaryNote;

    @ApiModelProperty("审核级别")
    private String reviewLevel; // NORMAL, IMPORTANT, URGENT

    @ApiModelProperty("备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;
}
