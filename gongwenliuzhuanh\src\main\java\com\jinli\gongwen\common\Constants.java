package com.jinli.gongwen.common;

/**
 * 系统常量
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
public class Constants {

    /**
     * 用户状态
     */
    public static final String USER_STATUS_NORMAL = "1";
    public static final String USER_STATUS_DISABLED = "0";

    /**
     * 角色键值
     */
    public static final String ROLE_ADMIN = "ADMIN";
    public static final String ROLE_DIRECTOR = "DIRECTOR";
    public static final String ROLE_VICE_DIRECTOR_PROD = "VICE_DIRECTOR_PROD";
    public static final String ROLE_VICE_DIRECTOR_SALES = "VICE_DIRECTOR_SALES";
    public static final String ROLE_VICE_DIRECTOR_FINANCE = "VICE_DIRECTOR_FINANCE";
    public static final String ROLE_OFFICE = "OFFICE";
    public static final String ROLE_DEPARTMENT = "DEPARTMENT";

    /**
     * 公文状态
     */
    public static final String DOC_STATUS_DRAFT = "DRAFT";
    public static final String DOC_STATUS_PROCESSING = "PROCESSING";
    public static final String DOC_STATUS_APPROVED = "APPROVED";
    public static final String DOC_STATUS_REJECTED = "REJECTED";
    public static final String DOC_STATUS_COMPLETED = "COMPLETED";

    /**
     * 公文紧急程度
     */
    public static final String DOC_LEVEL_NORMAL = "普通";
    public static final String DOC_LEVEL_URGENT = "紧急";
    public static final String DOC_LEVEL_VERY_URGENT = "特急";

    /**
     * 流程步骤
     */
    public static final String FLOW_STEP_DRAFT = "DRAFT";
    public static final String FLOW_STEP_OFFICE_EDIT = "OFFICE_EDIT";
    public static final String FLOW_STEP_VICE_REVIEW = "VICE_REVIEW";
    public static final String FLOW_STEP_DIRECTOR_APPROVE = "DIRECTOR_APPROVE";
    public static final String FLOW_STEP_OFFICE_SEND = "OFFICE_SEND";
    public static final String FLOW_STEP_DEPT_RECEIVE = "DEPT_RECEIVE";

    /**
     * 流程操作
     */
    public static final String FLOW_ACTION_SUBMIT = "SUBMIT";
    public static final String FLOW_ACTION_APPROVE = "APPROVE";
    public static final String FLOW_ACTION_REJECT = "REJECT";
    public static final String FLOW_ACTION_EDIT = "EDIT";
    public static final String FLOW_ACTION_SEND = "SEND";
    public static final String FLOW_ACTION_RECEIVE = "RECEIVE";
    public static final String FLOW_ACTION_TRANSFER = "TRANSFER";

    /**
     * 签收状态
     */
    public static final String RECEIVE_STATUS_UNREAD = "0";
    public static final String RECEIVE_STATUS_READ = "1";

    /**
     * 部门编码
     */
    public static final String DEPT_CODE_GROUP = "JINLI_GROUP";
    public static final String DEPT_CODE_MAIN_FACTORY = "MAIN_FACTORY";
    public static final String DEPT_CODE_BRANCH_1 = "BRANCH_1";
    public static final String DEPT_CODE_BRANCH_2 = "BRANCH_2";
    public static final String DEPT_CODE_BRANCH_3 = "BRANCH_3";
    public static final String DEPT_CODE_SALES = "SALES_DEPT";
    public static final String DEPT_CODE_FINANCE = "FINANCE_DEPT";
    public static final String DEPT_CODE_OFFICE = "OFFICE";

    /**
     * JWT相关
     */
    public static final String JWT_TOKEN_HEADER = "Authorization";
    public static final String JWT_TOKEN_PREFIX = "Bearer ";
    public static final String JWT_CLAIMS_USER_ID = "userId";
    public static final String JWT_CLAIMS_USERNAME = "username";
    public static final String JWT_CLAIMS_ROLE_KEY = "roleKey";

    /**
     * 文件上传
     */
    public static final String[] ALLOWED_FILE_TYPES = {
        "doc", "docx", "pdf", "txt", "xls", "xlsx", "ppt", "pptx", "jpg", "jpeg", "png", "gif"
    };
    public static final long MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

    /**
     * 系统配置键
     */
    public static final String CONFIG_KEY_SYSTEM_NAME = "sys.name";
    public static final String CONFIG_KEY_SYSTEM_VERSION = "sys.version";
    public static final String CONFIG_KEY_FILE_UPLOAD_PATH = "file.upload.path";
    public static final String CONFIG_KEY_DOC_NUMBER_PREFIX = "doc.number.prefix";
    public static final String CONFIG_KEY_DOC_DEFAULT_DEADLINE = "doc.default.deadline";

    /**
     * 默认值
     */
    public static final String DEFAULT_PASSWORD = "123456";
    public static final Integer DEFAULT_PAGE_SIZE = 10;
    public static final Integer DEFAULT_PAGE_NUM = 1;

    /**
     * 操作日志类型
     */
    public static final String LOG_TYPE_LOGIN = "LOGIN";
    public static final String LOG_TYPE_LOGOUT = "LOGOUT";
    public static final String LOG_TYPE_DOCUMENT = "DOCUMENT";
    public static final String LOG_TYPE_FLOW = "FLOW";
    public static final String LOG_TYPE_SYSTEM = "SYSTEM";

    /**
     * 业务类型
     */
    public static final String BUSINESS_TYPE_INSERT = "INSERT";
    public static final String BUSINESS_TYPE_UPDATE = "UPDATE";
    public static final String BUSINESS_TYPE_DELETE = "DELETE";
    public static final String BUSINESS_TYPE_SELECT = "SELECT";
    public static final String BUSINESS_TYPE_EXPORT = "EXPORT";
    public static final String BUSINESS_TYPE_IMPORT = "IMPORT";

    /**
     * 操作状态
     */
    public static final String OPERATION_STATUS_SUCCESS = "0";
    public static final String OPERATION_STATUS_FAIL = "1";

    /**
     * 通用状态
     */
    public static final String STATUS_NORMAL = "1";
    public static final String STATUS_DISABLED = "0";

    /**
     * 是否标识
     */
    public static final String YES = "Y";
    public static final String NO = "N";
}
