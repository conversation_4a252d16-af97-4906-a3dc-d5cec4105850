<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinli.gongwen.mapper.DocDocumentMapper">

    <!-- 公文信息结果映射 -->
    <resultMap id="DocumentResultMap" type="com.jinli.gongwen.entity.DocDocument">
        <id column="DOC_ID" property="docId"/>
        <result column="DOC_TITLE" property="docTitle"/>
        <result column="DOC_NUMBER" property="docNumber"/>
        <result column="DOC_TYPE" property="docType"/>
        <result column="DOC_LEVEL" property="docLevel"/>
        <result column="DOC_CONTENT" property="docContent"/>
        <result column="DOC_STATUS" property="docStatus"/>
        <result column="CREATE_USER_ID" property="createUserId"/>
        <result column="CREATE_DEPT_ID" property="createDeptId"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="UPDATE_TIME" property="updateTime"/>
        <result column="CURRENT_HANDLER" property="currentHandler"/>
        <result column="CURRENT_STEP" property="currentStep"/>
        <result column="DEADLINE" property="deadline"/>
        <result column="REMARK" property="remark"/>
        <result column="CREATE_USER_NAME" property="createUserName"/>
        <result column="CREATE_DEPT_NAME" property="createDeptName"/>
        <result column="CURRENT_HANDLER_NAME" property="currentHandlerName"/>
        <result column="DOC_TYPE_NAME" property="docTypeName"/>
    </resultMap>

    <!-- 分页查询公文列表（包含创建人、部门、处理人等信息） -->
    <select id="selectDocumentPage" resultMap="DocumentResultMap">
        SELECT d.*, 
               cu.REAL_NAME AS CREATE_USER_NAME,
               cd.DEPT_NAME AS CREATE_DEPT_NAME,
               ch.REAL_NAME AS CURRENT_HANDLER_NAME,
               dt.TYPE_NAME AS DOC_TYPE_NAME
        FROM DOC_DOCUMENT d
        LEFT JOIN SYS_USER cu ON d.CREATE_USER_ID = cu.USER_ID
        LEFT JOIN SYS_DEPARTMENT cd ON d.CREATE_DEPT_ID = cd.DEPT_ID
        LEFT JOIN SYS_USER ch ON d.CURRENT_HANDLER = ch.USER_ID
        LEFT JOIN SYS_DOC_TYPE dt ON d.DOC_TYPE = dt.TYPE_CODE
        <where>
            <if test="docTitle != null and docTitle != ''">
                AND d.DOC_TITLE LIKE CONCAT('%', #{docTitle}, '%')
            </if>
            <if test="docType != null and docType != ''">
                AND d.DOC_TYPE = #{docType}
            </if>
            <if test="docStatus != null and docStatus != ''">
                AND d.DOC_STATUS = #{docStatus}
            </if>
            <if test="createUserId != null and createUserId != ''">
                AND d.CREATE_USER_ID = #{createUserId}
            </if>
            <if test="createDeptId != null and createDeptId != ''">
                AND d.CREATE_DEPT_ID = #{createDeptId}
            </if>
            <if test="currentHandler != null and currentHandler != ''">
                AND d.CURRENT_HANDLER = #{currentHandler}
            </if>
            <if test="startTime != null">
                AND d.CREATE_TIME >= #{startTime}
            </if>
            <if test="endTime != null">
                AND d.CREATE_TIME &lt;= #{endTime}
            </if>
        </where>
        ORDER BY d.CREATE_TIME DESC
    </select>

    <!-- 根据公文ID查询公文详细信息（包含创建人、部门、处理人等信息） -->
    <select id="selectDocumentDetailById" resultMap="DocumentResultMap">
        SELECT d.*, 
               cu.REAL_NAME AS CREATE_USER_NAME,
               cd.DEPT_NAME AS CREATE_DEPT_NAME,
               ch.REAL_NAME AS CURRENT_HANDLER_NAME,
               dt.TYPE_NAME AS DOC_TYPE_NAME
        FROM DOC_DOCUMENT d
        LEFT JOIN SYS_USER cu ON d.CREATE_USER_ID = cu.USER_ID
        LEFT JOIN SYS_DEPARTMENT cd ON d.CREATE_DEPT_ID = cd.DEPT_ID
        LEFT JOIN SYS_USER ch ON d.CURRENT_HANDLER = ch.USER_ID
        LEFT JOIN SYS_DOC_TYPE dt ON d.DOC_TYPE = dt.TYPE_CODE
        WHERE d.DOC_ID = #{docId}
    </select>

    <!-- 查询用户待处理的公文列表 -->
    <select id="selectPendingDocuments" resultMap="DocumentResultMap">
        SELECT d.*, 
               cu.REAL_NAME AS CREATE_USER_NAME,
               cd.DEPT_NAME AS CREATE_DEPT_NAME,
               ch.REAL_NAME AS CURRENT_HANDLER_NAME,
               dt.TYPE_NAME AS DOC_TYPE_NAME
        FROM DOC_DOCUMENT d
        LEFT JOIN SYS_USER cu ON d.CREATE_USER_ID = cu.USER_ID
        LEFT JOIN SYS_DEPARTMENT cd ON d.CREATE_DEPT_ID = cd.DEPT_ID
        LEFT JOIN SYS_USER ch ON d.CURRENT_HANDLER = ch.USER_ID
        LEFT JOIN SYS_DOC_TYPE dt ON d.DOC_TYPE = dt.TYPE_CODE
        WHERE d.CURRENT_HANDLER = #{userId}
        AND d.DOC_STATUS = 'PROCESSING'
        ORDER BY d.CREATE_TIME DESC
    </select>

    <!-- 查询用户已处理的公文列表 -->
    <select id="selectProcessedDocuments" resultMap="DocumentResultMap">
        SELECT DISTINCT d.*, 
               cu.REAL_NAME AS CREATE_USER_NAME,
               cd.DEPT_NAME AS CREATE_DEPT_NAME,
               ch.REAL_NAME AS CURRENT_HANDLER_NAME,
               dt.TYPE_NAME AS DOC_TYPE_NAME
        FROM DOC_DOCUMENT d
        LEFT JOIN SYS_USER cu ON d.CREATE_USER_ID = cu.USER_ID
        LEFT JOIN SYS_DEPARTMENT cd ON d.CREATE_DEPT_ID = cd.DEPT_ID
        LEFT JOIN SYS_USER ch ON d.CURRENT_HANDLER = ch.USER_ID
        LEFT JOIN SYS_DOC_TYPE dt ON d.DOC_TYPE = dt.TYPE_CODE
        INNER JOIN DOC_FLOW_RECORD fr ON d.DOC_ID = fr.DOC_ID
        WHERE (fr.FROM_USER_ID = #{userId} OR fr.TO_USER_ID = #{userId})
        AND d.DOC_STATUS IN ('APPROVED', 'REJECTED', 'COMPLETED')
        ORDER BY d.UPDATE_TIME DESC
    </select>

    <!-- 查询部门相关的公文列表 -->
    <select id="selectDepartmentDocuments" resultMap="DocumentResultMap">
        SELECT d.*, 
               cu.REAL_NAME AS CREATE_USER_NAME,
               cd.DEPT_NAME AS CREATE_DEPT_NAME,
               ch.REAL_NAME AS CURRENT_HANDLER_NAME,
               dt.TYPE_NAME AS DOC_TYPE_NAME
        FROM DOC_DOCUMENT d
        LEFT JOIN SYS_USER cu ON d.CREATE_USER_ID = cu.USER_ID
        LEFT JOIN SYS_DEPARTMENT cd ON d.CREATE_DEPT_ID = cd.DEPT_ID
        LEFT JOIN SYS_USER ch ON d.CURRENT_HANDLER = ch.USER_ID
        LEFT JOIN SYS_DOC_TYPE dt ON d.DOC_TYPE = dt.TYPE_CODE
        WHERE d.CREATE_DEPT_ID = #{departmentId}
        <if test="docStatus != null and docStatus != ''">
            AND d.DOC_STATUS = #{docStatus}
        </if>
        ORDER BY d.CREATE_TIME DESC
    </select>

    <!-- 查询已发送的公文列表（供领导查看） -->
    <select id="selectSentDocuments" resultMap="DocumentResultMap">
        SELECT d.*, 
               cu.REAL_NAME AS CREATE_USER_NAME,
               cd.DEPT_NAME AS CREATE_DEPT_NAME,
               ch.REAL_NAME AS CURRENT_HANDLER_NAME,
               dt.TYPE_NAME AS DOC_TYPE_NAME
        FROM DOC_DOCUMENT d
        LEFT JOIN SYS_USER cu ON d.CREATE_USER_ID = cu.USER_ID
        LEFT JOIN SYS_DEPARTMENT cd ON d.CREATE_DEPT_ID = cd.DEPT_ID
        LEFT JOIN SYS_USER ch ON d.CURRENT_HANDLER = ch.USER_ID
        LEFT JOIN SYS_DOC_TYPE dt ON d.DOC_TYPE = dt.TYPE_CODE
        WHERE d.DOC_STATUS IN ('APPROVED', 'COMPLETED')
        <if test="startTime != null">
            AND d.CREATE_TIME >= #{startTime}
        </if>
        <if test="endTime != null">
            AND d.CREATE_TIME &lt;= #{endTime}
        </if>
        ORDER BY d.CREATE_TIME DESC
    </select>

    <!-- 检查公文编号是否唯一 -->
    <select id="checkDocNumberUnique" resultType="int">
        SELECT COUNT(1)
        FROM DOC_DOCUMENT
        WHERE DOC_NUMBER = #{docNumber}
        <if test="docId != null and docId != ''">
            AND DOC_ID != #{docId}
        </if>
    </select>

    <!-- 统计用户待处理公文数量 -->
    <select id="countPendingDocuments" resultType="int">
        SELECT COUNT(1)
        FROM DOC_DOCUMENT
        WHERE CURRENT_HANDLER = #{userId}
        AND DOC_STATUS = 'PROCESSING'
    </select>

    <!-- 统计部门公文数量 -->
    <select id="countDepartmentDocuments" resultType="int">
        SELECT COUNT(1)
        FROM DOC_DOCUMENT
        WHERE CREATE_DEPT_ID = #{departmentId}
        <if test="docStatus != null and docStatus != ''">
            AND DOC_STATUS = #{docStatus}
        </if>
    </select>

    <!-- 查询即将到期的公文列表 -->
    <select id="selectExpiringDocuments" resultMap="DocumentResultMap">
        SELECT d.*, 
               cu.REAL_NAME AS CREATE_USER_NAME,
               cd.DEPT_NAME AS CREATE_DEPT_NAME,
               ch.REAL_NAME AS CURRENT_HANDLER_NAME,
               dt.TYPE_NAME AS DOC_TYPE_NAME
        FROM DOC_DOCUMENT d
        LEFT JOIN SYS_USER cu ON d.CREATE_USER_ID = cu.USER_ID
        LEFT JOIN SYS_DEPARTMENT cd ON d.CREATE_DEPT_ID = cd.DEPT_ID
        LEFT JOIN SYS_USER ch ON d.CURRENT_HANDLER = ch.USER_ID
        LEFT JOIN SYS_DOC_TYPE dt ON d.DOC_TYPE = dt.TYPE_CODE
        WHERE d.DEADLINE IS NOT NULL
        AND d.DEADLINE &lt;= DATEADD(DAY, #{days}, SYSDATE)
        AND d.DOC_STATUS = 'PROCESSING'
        ORDER BY d.DEADLINE ASC
    </select>

    <!-- 更新公文状态和当前处理人 -->
    <update id="updateDocumentStatus">
        UPDATE DOC_DOCUMENT
        SET DOC_STATUS = #{docStatus},
            CURRENT_HANDLER = #{currentHandler},
            CURRENT_STEP = #{currentStep},
            UPDATE_TIME = SYSDATE
        WHERE DOC_ID = #{docId}
    </update>

    <!-- 根据条件搜索公文 -->
    <select id="searchDocuments" resultMap="DocumentResultMap">
        SELECT d.*, 
               cu.REAL_NAME AS CREATE_USER_NAME,
               cd.DEPT_NAME AS CREATE_DEPT_NAME,
               ch.REAL_NAME AS CURRENT_HANDLER_NAME,
               dt.TYPE_NAME AS DOC_TYPE_NAME
        FROM DOC_DOCUMENT d
        LEFT JOIN SYS_USER cu ON d.CREATE_USER_ID = cu.USER_ID
        LEFT JOIN SYS_DEPARTMENT cd ON d.CREATE_DEPT_ID = cd.DEPT_ID
        LEFT JOIN SYS_USER ch ON d.CURRENT_HANDLER = ch.USER_ID
        LEFT JOIN SYS_DOC_TYPE dt ON d.DOC_TYPE = dt.TYPE_CODE
        <where>
            <if test="keyword != null and keyword != ''">
                AND (d.DOC_TITLE LIKE CONCAT('%', #{keyword}, '%') 
                     OR d.DOC_CONTENT LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="docType != null and docType != ''">
                AND d.DOC_TYPE = #{docType}
            </if>
            <if test="createDeptId != null and createDeptId != ''">
                AND d.CREATE_DEPT_ID = #{createDeptId}
            </if>
            <if test="startTime != null">
                AND d.CREATE_TIME >= #{startTime}
            </if>
            <if test="endTime != null">
                AND d.CREATE_TIME &lt;= #{endTime}
            </if>
        </where>
        ORDER BY d.CREATE_TIME DESC
    </select>

</mapper>
