package com.jinli.gongwen.vo.system;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 流程配置视图对象
 */
@Data
@ApiModel("流程配置视图对象")
public class FlowConfigVO {

    @ApiModelProperty("配置ID")
    private String configId;

    @ApiModelProperty("流程名称")
    private String flowName;

    @ApiModelProperty("流程描述")
    private String flowDescription;

    @ApiModelProperty("流程步骤")
    private String flowSteps;

    @ApiModelProperty("是否启用")
    private Boolean isEnabled;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("创建时间")
    private String createTime;

    @ApiModelProperty("更新时间")
    private String updateTime;

    @ApiModelProperty("创建用户")
    private String createUserName;

    @ApiModelProperty("更新用户")
    private String updateUserName;
}
