package com.jinli.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinli.dto.RoleQueryDTO;
import com.jinli.entity.SysRole;
import com.jinli.gongwen.mapper.SysRoleMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.ZoneId;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;

/**
 * 角色服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-30
 */
@Service("sysRoleService")
public class SysRoleServiceSimple {

    private static final Logger log = LoggerFactory.getLogger(SysRoleServiceSimple.class);

    @Autowired
    private SysRoleMapper roleMapper;

    /**
     * 分页查询角色列表
     */
    public Map<String, Object> selectRolePage(RoleQueryDTO queryDTO) {
        log.info("分页查询角色列表，查询条件：{}", queryDTO);
        
        try {
            // 验证分页参数
            if (queryDTO.getPageNum() == null || queryDTO.getPageNum() < 1) {
                queryDTO.setPageNum(1);
            }
            if (queryDTO.getPageSize() == null || queryDTO.getPageSize() < 1) {
                queryDTO.setPageSize(10);
            }
            
            // 创建分页对象
            Page<com.jinli.gongwen.entity.SysRole> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
            
            // 使用现有的分页查询方法
            IPage<com.jinli.gongwen.entity.SysRole> pageResult = roleMapper.selectRolePage(
                page,
                queryDTO.getRoleName(), 
                queryDTO.getRoleKey(), 
                queryDTO.getStatus()
            );
            
            // 转换为新的实体类
            List<SysRole> roles = new ArrayList<>();
            for (com.jinli.gongwen.entity.SysRole gongwenRole : pageResult.getRecords()) {
                SysRole role = convertToNewRole(gongwenRole);
                roles.add(role);
            }
            
            // 获取总数
            Long total = pageResult.getTotal();
            
            Map<String, Object> result = new HashMap<>();
            result.put("list", roles);
            result.put("total", total != null ? total : 0);
            result.put("pageNum", queryDTO.getPageNum());
            result.put("pageSize", queryDTO.getPageSize());
            result.put("pages", (total + queryDTO.getPageSize() - 1) / queryDTO.getPageSize());
            
            return result;
            
        } catch (Exception e) {
            log.error("查询角色列表失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("list", new ArrayList<>());
            result.put("total", 0);
            result.put("pageNum", queryDTO.getPageNum());
            result.put("pageSize", queryDTO.getPageSize());
            result.put("pages", 0);
            return result;
        }
    }

    /**
     * 转换角色实体
     */
    private SysRole convertToNewRole(com.jinli.gongwen.entity.SysRole gongwenRole) {
        SysRole role = new SysRole();
        role.setRoleId(gongwenRole.getRoleId());
        role.setRoleName(gongwenRole.getRoleName());
        role.setRoleKey(gongwenRole.getRoleKey());
        role.setRoleDesc(gongwenRole.getRemark()); // 使用remark作为描述
        role.setStatus(gongwenRole.getStatus());
        role.setSortOrder(gongwenRole.getRoleSort()); // 使用roleSort

        // 转换Date到LocalDateTime
        if (gongwenRole.getCreateTime() != null) {
            role.setCreateTime(gongwenRole.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
        }
        if (gongwenRole.getUpdateTime() != null) {
            role.setUpdateTime(gongwenRole.getUpdateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
        }

        role.setRemark(gongwenRole.getRemark());

        return role;
    }

    /**
     * 根据角色ID查询角色详情
     */
    public SysRole selectRoleById(String roleId) {
        log.info("根据ID查询角色详情，角色ID：{}", roleId);
        
        try {
            com.jinli.gongwen.entity.SysRole gongwenRole = roleMapper.selectRoleDetailById(roleId);
            if (gongwenRole != null) {
                return convertToNewRole(gongwenRole);
            }
            return null;
        } catch (Exception e) {
            log.error("查询角色详情失败", e);
            return null;
        }
    }

    /**
     * 更新角色状态
     */
    public boolean updateRoleStatus(String roleId, String status) {
        log.info("更新角色状态，角色ID：{}，状态：{}", roleId, status);
        
        try {
            // 先查询角色
            com.jinli.gongwen.entity.SysRole gongwenRole = roleMapper.selectById(roleId);
            if (gongwenRole != null) {
                gongwenRole.setStatus(status);
                int result = roleMapper.updateById(gongwenRole);
                return result > 0;
            }
            return false;
        } catch (Exception e) {
            log.error("更新角色状态失败", e);
            return false;
        }
    }

    /**
     * 删除角色
     */
    public boolean deleteRole(String roleId) {
        log.info("删除角色，角色ID：{}", roleId);
        
        try {
            int result = roleMapper.deleteById(roleId);
            return result > 0;
        } catch (Exception e) {
            log.error("删除角色失败", e);
            return false;
        }
    }

    /**
     * 新增角色
     */
    public boolean insertRole(SysRole role) {
        log.info("新增角色，角色信息：{}", role);
        
        try {
            // 转换为原有实体
            com.jinli.gongwen.entity.SysRole gongwenRole = convertToGongwenRole(role);
            int result = roleMapper.insert(gongwenRole);
            return result > 0;
        } catch (Exception e) {
            log.error("新增角色失败", e);
            return false;
        }
    }

    /**
     * 更新角色
     */
    public boolean updateRole(SysRole role) {
        log.info("更新角色，角色信息：{}", role);
        
        try {
            // 转换为原有实体
            com.jinli.gongwen.entity.SysRole gongwenRole = convertToGongwenRole(role);
            int result = roleMapper.updateById(gongwenRole);
            return result > 0;
        } catch (Exception e) {
            log.error("更新角色失败", e);
            return false;
        }
    }

    /**
     * 转换为原有角色实体
     */
    private com.jinli.gongwen.entity.SysRole convertToGongwenRole(SysRole role) {
        com.jinli.gongwen.entity.SysRole gongwenRole = new com.jinli.gongwen.entity.SysRole();
        gongwenRole.setRoleId(role.getRoleId());
        gongwenRole.setRoleName(role.getRoleName());
        gongwenRole.setRoleKey(role.getRoleKey());
        gongwenRole.setStatus(role.getStatus());
        gongwenRole.setRoleSort(role.getSortOrder()); // 使用roleSort
        gongwenRole.setRemark(role.getRemark());

        // 转换LocalDateTime到Date
        if (role.getCreateTime() != null) {
            gongwenRole.setCreateTime(java.util.Date.from(role.getCreateTime().atZone(ZoneId.systemDefault()).toInstant()));
        }
        if (role.getUpdateTime() != null) {
            gongwenRole.setUpdateTime(java.util.Date.from(role.getUpdateTime().atZone(ZoneId.systemDefault()).toInstant()));
        }

        return gongwenRole;
    }

    /**
     * 检查角色名称是否存在
     */
    public boolean checkRoleNameExists(String roleName, String excludeRoleId) {
        try {
            Integer count = roleMapper.checkRoleNameUnique(roleName, excludeRoleId);
            return count != null && count > 0;
        } catch (Exception e) {
            log.error("检查角色名称失败", e);
            return false;
        }
    }

    /**
     * 检查角色标识是否存在
     */
    public boolean checkRoleKeyExists(String roleKey, String excludeRoleId) {
        try {
            Integer count = roleMapper.checkRoleKeyUnique(roleKey, excludeRoleId);
            return count != null && count > 0;
        } catch (Exception e) {
            log.error("检查角色标识失败", e);
            return false;
        }
    }
}
