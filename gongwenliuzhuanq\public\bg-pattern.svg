<?xml version="1.0" encoding="UTF-8"?>
<svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 几何图案背景 -->
  <defs>
    <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
      <circle cx="10" cy="10" r="1" fill="#ffffff" opacity="0.3"/>
    </pattern>
  </defs>
  
  <rect width="60" height="60" fill="url(#grid)"/>
  
  <!-- 装饰线条 -->
  <path d="M0 15 L60 15" stroke="#ffffff" stroke-width="0.5" opacity="0.2"/>
  <path d="M0 30 L60 30" stroke="#ffffff" stroke-width="0.5" opacity="0.2"/>
  <path d="M0 45 L60 45" stroke="#ffffff" stroke-width="0.5" opacity="0.2"/>
  <path d="M15 0 L15 60" stroke="#ffffff" stroke-width="0.5" opacity="0.2"/>
  <path d="M30 0 L30 60" stroke="#ffffff" stroke-width="0.5" opacity="0.2"/>
  <path d="M45 0 L45 60" stroke="#ffffff" stroke-width="0.5" opacity="0.2"/>
</svg>
