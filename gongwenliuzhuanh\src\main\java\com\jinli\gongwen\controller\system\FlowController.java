package com.jinli.gongwen.controller.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinli.gongwen.common.Result;
import com.jinli.gongwen.dto.system.FlowConfigDTO;
import com.jinli.gongwen.dto.system.FlowQueryDTO;
import com.jinli.gongwen.entity.system.FlowConfig;
import com.jinli.gongwen.service.system.FlowConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 流程配置管理控制器
 * 系统管理员专用接口
 */
@Api(tags = "流程配置管理")
@RestController
@RequestMapping("/api/system/flow")
@PreAuthorize("hasRole('ADMIN')")
public class FlowController {

    @Autowired
    private FlowConfigService flowConfigService;

    /**
     * 流程配置列表查询
     */
    @ApiOperation("流程配置列表查询")
    @GetMapping("/list")
    public Result<IPage<FlowConfig>> list(FlowQueryDTO queryDTO) {
        Page<FlowConfig> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());

        IPage<FlowConfig> result = flowConfigService.getFlowConfigList(page, queryDTO);
        return Result.success(result);
    }

    /**
     * 获取流程配置详情
     */
    @ApiOperation("获取流程配置详情")
    @GetMapping("/{configId}")
    public Result<FlowConfig> getFlowConfig(@PathVariable String configId) {
        FlowConfig flowConfig = flowConfigService.getFlowConfigById(configId);
        return Result.success(flowConfig);
    }

    /**
     * 新增流程配置
     */
    @ApiOperation("新增流程配置")
    @PostMapping
    public Result<Void> add(@Valid @RequestBody FlowConfigDTO flowConfigDTO) {
        flowConfigService.createFlowConfig(flowConfigDTO);
        return Result.success();
    }

    /**
     * 修改流程配置
     */
    @ApiOperation("修改流程配置")
    @PutMapping("/{configId}")
    public Result<Void> update(@PathVariable String configId, @Valid @RequestBody FlowConfigDTO flowConfigDTO) {
        flowConfigService.updateFlowConfig(configId, flowConfigDTO);
        return Result.success();
    }

    /**
     * 删除流程配置
     */
    @ApiOperation("删除流程配置")
    @DeleteMapping("/{configId}")
    public Result<Void> delete(@PathVariable String configId) {
        flowConfigService.deleteFlowConfig(configId);
        return Result.success();
    }

    /**
     * 获取所有启用的流程配置
     */
    @ApiOperation("获取所有启用的流程配置")
    @GetMapping("/enabled")
    public Result<List<FlowConfig>> getAllEnabledFlowConfigs() {
        List<FlowConfig> configs = flowConfigService.getAllEnabledFlowConfigs();
        return Result.success(configs);
    }
}
