package com.jinli.gongwen.entity.system;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 系统用户实体类
 */
@Data
@TableName("sys_user")
public class SysUser {

    @TableId(type = IdType.ASSIGN_ID)
    private String userId;

    private String username;

    private String password;

    private String realName;

    private String email;

    private String phone;

    private String deptId;

    private String deptName;

    private Boolean isEnabled;

    private String avatar;

    private String remark;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String createUserId;

    private String updateUserId;

    private LocalDateTime lastLoginTime;
}
