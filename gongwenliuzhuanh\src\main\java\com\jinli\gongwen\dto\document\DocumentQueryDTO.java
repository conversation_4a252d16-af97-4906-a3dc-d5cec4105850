package com.jinli.gongwen.dto.document;

import com.jinli.gongwen.common.BaseQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 公文查询数据传输对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("公文查询数据传输对象")
public class DocumentQueryDTO extends BaseQueryDTO {

    @ApiModelProperty("公文标题")
    private String docTitle;

    @ApiModelProperty("公文类型")
    private String docType;

    @ApiModelProperty("公文级别")
    private String docLevel;

    @ApiModelProperty("公文状态")
    private String docStatus;

    @ApiModelProperty("创建部门ID")
    private String createDeptId;

    @ApiModelProperty("创建用户ID")
    private String createUserId;

    @ApiModelProperty("审核结果")
    private String reviewResult;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("开始时间")
    private String startTime;

    @ApiModelProperty("结束时间")
    private String endTime;
}
