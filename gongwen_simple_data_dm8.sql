-- =====================================================
-- 河北金力集团公文流转系统简化测试数据
-- 数据库：达梦8 (DM8)
-- 说明：如果表不存在会报错，请忽略错误继续执行
-- =====================================================

-- ========================================
-- 1. 数据清理（忽略表不存在的错误）
-- ========================================
-- 注意：如果某些表不存在，会报错，请忽略错误继续执行

-- 清理公文相关数据
DELETE FROM DOC_RECEIVE_RECORD;
DELETE FROM DOC_ATTACHMENT;
DELETE FROM DOC_FLOW_RECORD;
DELETE FROM DOC_DOCUMENT;

-- 清理用户相关数据
DELETE FROM SYS_USER_ROLE;
DELETE FROM SYS_USER;

-- 清理基础数据
DELETE FROM SYS_ROLE;
DELETE FROM SYS_DEPARTMENT;
DELETE FROM SYS_DOC_TYPE;
DELETE FROM SYS_FLOW_CONFIG;
DELETE FROM SYS_CONFIG;

-- 清理日志（可能不存在）
DELETE FROM SYS_LOG;

COMMIT;

-- ========================================
-- 2. 部门数据（严格按需求文档）
-- ========================================
INSERT INTO SYS_DEPARTMENT (DEPT_ID, DEPT_NAME, DEPT_CODE, PARENT_ID, ORDER_NUM, LEADER, PHONE, EMAIL, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) VALUES
('DEPT001', '河北金力集团', 'JINLI_GROUP', NULL, 1, '张厂长', '0311-88888888', '<EMAIL>', '1', SYSDATE, SYSDATE, '集团总部'),
('DEPT002', '办公室', 'OFFICE', 'DEPT001', 2, '李主任', '0311-88888801', '<EMAIL>', '1', SYSDATE, SYSDATE, '负责公文流转管理'),
('DEPT003', '生产部', 'PRODUCTION', 'DEPT001', 3, '王经理', '0311-88888802', '<EMAIL>', '1', SYSDATE, SYSDATE, '负责主厂区、一分厂、二分厂、三分厂生产管理'),
('DEPT004', '销售部', 'SALES', 'DEPT001', 4, '赵经理', '0311-88888803', '<EMAIL>', '1', SYSDATE, SYSDATE, '负责销售管理'),
('DEPT005', '财务部', 'FINANCE', 'DEPT001', 5, '钱经理', '0311-88888804', '<EMAIL>', '1', SYSDATE, SYSDATE, '负责财务管理');

-- ========================================
-- 3. 角色数据
-- ========================================
INSERT INTO SYS_ROLE (ROLE_ID, ROLE_NAME, ROLE_KEY, ROLE_SORT, STATUS, CREATE_TIME, UPDATE_TIME, REMARK) VALUES
('ROLE001', '系统管理员', 'ADMIN', 1, '1', SYSDATE, SYSDATE, '用户管理、角色管理、公文流转流程管理'),
('ROLE002', '厂长', 'DIRECTOR', 2, '1', SYSDATE, SYSDATE, '审签公文、浏览已发所有公文、查询公文'),
('ROLE003', '生产副厂长', 'VICE_DIRECTOR_PROD', 3, '1', SYSDATE, SYSDATE, '审核公文、浏览已发所有公文、查询公文'),
('ROLE004', '销售副厂长', 'VICE_DIRECTOR_SALES', 4, '1', SYSDATE, SYSDATE, '审核公文、浏览已发所有公文、查询公文'),
('ROLE005', '财务副厂长', 'VICE_DIRECTOR_FINANCE', 5, '1', SYSDATE, SYSDATE, '审核公文、浏览已发所有公文、查询公文'),
('ROLE006', '办公室主任', 'OFFICE_DIRECTOR', 6, '1', SYSDATE, SYSDATE, '修改公文、公文流转、删除公文、公文发送'),
('ROLE007', '部门经理', 'DEPT_MANAGER', 7, '1', SYSDATE, SYSDATE, '公文拟制、签收公文、浏览公文'),
('ROLE008', '普通员工', 'EMPLOYEE', 8, '1', SYSDATE, SYSDATE, '公文拟制、签收公文、浏览公文');

-- ========================================
-- 4. 用户数据（精简至12个用户）
-- ========================================
INSERT INTO SYS_USER (USER_ID, USERNAME, PASSWORD, REAL_NAME, EMAIL, PHONE, DEPARTMENT_ID, STATUS, LOGIN_IP, LOGIN_DATE, CREATE_TIME, UPDATE_TIME, REMARK) VALUES
-- 管理层（5个）
('USER001', 'admin', '$2a$10$7JB720yubVSOfvVWbfXCOOlWpZXGlDYDdTOWkTRDDHQGpVw62VsMO', '系统管理员', '<EMAIL>', '13800000001', 'DEPT002', '1', '127.0.0.1', SYSDATE, SYSDATE, SYSDATE, '系统管理员账号，密码：123456'),
('USER002', 'director', '$2a$10$7JB720yubVSOfvVWbfXCOOlWpZXGlDYDdTOWkTRDDHQGpVw62VsMO', '张厂长', '<EMAIL>', '13800000002', 'DEPT001', '1', NULL, NULL, SYSDATE, SYSDATE, '厂长，密码：123456'),
('USER003', 'vice_prod', '$2a$10$7JB720yubVSOfvVWbfXCOOlWpZXGlDYDdTOWkTRDDHQGpVw62VsMO', '王副厂长', '<EMAIL>', '13800000003', 'DEPT003', '1', NULL, NULL, SYSDATE, SYSDATE, '生产副厂长，密码：123456'),
('USER004', 'vice_sales', '$2a$10$7JB720yubVSOfvVWbfXCOOlWpZXGlDYDdTOWkTRDDHQGpVw62VsMO', '赵副厂长', '<EMAIL>', '13800000004', 'DEPT004', '1', NULL, NULL, SYSDATE, SYSDATE, '销售副厂长，密码：123456'),
('USER005', 'vice_finance', '$2a$10$7JB720yubVSOfvVWbfXCOOlWpZXGlDYDdTOWkTRDDHQGpVw62VsMO', '钱副厂长', '<EMAIL>', '13800000005', 'DEPT005', '1', NULL, NULL, SYSDATE, SYSDATE, '财务副厂长，密码：123456'),
-- 办公室（1个）
('USER006', 'office', '$2a$10$7JB720yubVSOfvVWbfXCOOlWpZXGlDYDdTOWkTRDDHQGpVw62VsMO', '李主任', '<EMAIL>', '13800000006', 'DEPT002', '1', NULL, NULL, SYSDATE, SYSDATE, '办公室主任，密码：123456'),
-- 部门经理（3个）
('USER007', 'prod_manager', '$2a$10$7JB720yubVSOfvVWbfXCOOlWpZXGlDYDdTOWkTRDDHQGpVw62VsMO', '王经理', '<EMAIL>', '13800000007', 'DEPT003', '1', NULL, NULL, SYSDATE, SYSDATE, '生产部经理，密码：123456'),
('USER008', 'sales_manager', '$2a$10$7JB720yubVSOfvVWbfXCOOlWpZXGlDYDdTOWkTRDDHQGpVw62VsMO', '赵经理', '<EMAIL>', '13800000008', 'DEPT004', '1', NULL, NULL, SYSDATE, SYSDATE, '销售部经理，密码：123456'),
('USER009', 'finance_manager', '$2a$10$7JB720yubVSOfvVWbfXCOOlWpZXGlDYDdTOWkTRDDHQGpVw62VsMO', '钱经理', '<EMAIL>', '13800000009', 'DEPT005', '1', NULL, NULL, SYSDATE, SYSDATE, '财务部经理，密码：123456'),
-- 普通员工（3个）
('USER010', 'prod_employee', '$2a$10$7JB720yubVSOfvVWbfXCOOlWpZXGlDYDdTOWkTRDDHQGpVw62VsMO', '张三', '<EMAIL>', '13800000010', 'DEPT003', '1', NULL, NULL, SYSDATE, SYSDATE, '生产部员工，密码：123456'),
('USER011', 'sales_employee', '$2a$10$7JB720yubVSOfvVWbfXCOOlWpZXGlDYDdTOWkTRDDHQGpVw62VsMO', '李四', '<EMAIL>', '13800000011', 'DEPT004', '1', NULL, NULL, SYSDATE, SYSDATE, '销售部员工，密码：123456'),
('USER012', 'finance_employee', '$2a$10$7JB720yubVSOfvVWbfXCOOlWpZXGlDYDdTOWkTRDDHQGpVw62VsMO', '王五', '<EMAIL>', '13800000012', 'DEPT005', '1', NULL, NULL, SYSDATE, SYSDATE, '财务部员工，密码：123456');

-- ========================================
-- 5. 用户角色关联
-- ========================================
INSERT INTO SYS_USER_ROLE (USER_ID, ROLE_ID) VALUES
('USER001', 'ROLE001'),  -- admin -> 系统管理员
('USER002', 'ROLE002'),  -- director -> 厂长
('USER003', 'ROLE003'),  -- vice_prod -> 生产副厂长
('USER004', 'ROLE004'),  -- vice_sales -> 销售副厂长
('USER005', 'ROLE005'),  -- vice_finance -> 财务副厂长
('USER006', 'ROLE006'),  -- office -> 办公室主任
('USER007', 'ROLE007'),  -- prod_manager -> 部门经理
('USER008', 'ROLE007'),  -- sales_manager -> 部门经理
('USER009', 'ROLE007'),  -- finance_manager -> 部门经理
('USER010', 'ROLE008'),  -- prod_employee -> 普通员工
('USER011', 'ROLE008'),  -- sales_employee -> 普通员工
('USER012', 'ROLE008');  -- finance_employee -> 普通员工

-- ========================================
-- 6. 公文类型字典
-- ========================================
INSERT INTO SYS_DOC_TYPE (TYPE_ID, TYPE_NAME, TYPE_CODE, PARENT_ID, ORDER_NUM, STATUS, CREATE_TIME, REMARK) VALUES
('TYPE001', '通知', 'NOTICE', NULL, 1, '1', SYSDATE, '通知类公文'),
('TYPE002', '报告', 'REPORT', NULL, 2, '1', SYSDATE, '报告类公文'),
('TYPE003', '请示', 'REQUEST', NULL, 3, '1', SYSDATE, '请示类公文'),
('TYPE004', '决定', 'DECISION', NULL, 4, '1', SYSDATE, '决定类公文'),
('TYPE005', '意见', 'OPINION', NULL, 5, '1', SYSDATE, '意见类公文'),
('TYPE006', '函', 'LETTER', NULL, 6, '1', SYSDATE, '函件类公文'),
('TYPE007', '会议纪要', 'MINUTES', NULL, 7, '1', SYSDATE, '会议纪要类公文');

-- ========================================
-- 7. 系统配置
-- ========================================
INSERT INTO SYS_CONFIG (CONFIG_ID, CONFIG_NAME, CONFIG_KEY, CONFIG_VALUE, CONFIG_TYPE, CREATE_TIME, UPDATE_TIME, REMARK) VALUES
('CFG001', '系统名称', 'sys.name', '河北金力集团公文流转系统', 'Y', SYSDATE, SYSDATE, '系统名称配置'),
('CFG002', '系统版本', 'sys.version', '1.0.0', 'Y', SYSDATE, SYSDATE, '系统版本号'),
('CFG003', '文件上传路径', 'file.upload.path', '/uploads/', 'N', SYSDATE, SYSDATE, '文件上传路径'),
('CFG004', '文件最大大小', 'file.max.size', '10485760', 'N', SYSDATE, SYSDATE, '文件最大大小(字节)'),
('CFG005', '公文编号前缀', 'doc.number.prefix', 'JINLI', 'N', SYSDATE, SYSDATE, '公文编号前缀');

COMMIT;

-- ========================================
-- 验证数据
-- ========================================
SELECT '=== 数据验证 ===' AS 信息;
SELECT '部门数据：' AS 类型, COUNT(*) AS 数量 FROM SYS_DEPARTMENT;
SELECT '角色数据：' AS 类型, COUNT(*) AS 数量 FROM SYS_ROLE;
SELECT '用户数据：' AS 类型, COUNT(*) AS 数量 FROM SYS_USER;
SELECT '用户角色关联：' AS 类型, COUNT(*) AS 数量 FROM SYS_USER_ROLE;

-- 显示测试账号
SELECT '=== 测试账号（密码均为123456） ===' AS 信息;
SELECT u.USERNAME AS 用户名, u.REAL_NAME AS 姓名, r.ROLE_NAME AS 角色, d.DEPT_NAME AS 部门
FROM SYS_USER u
JOIN SYS_USER_ROLE ur ON u.USER_ID = ur.USER_ID
JOIN SYS_ROLE r ON ur.ROLE_ID = r.ROLE_ID
JOIN SYS_DEPARTMENT d ON u.DEPARTMENT_ID = d.DEPT_ID
ORDER BY r.ROLE_SORT, u.USERNAME;

SELECT '数据初始化完成！' AS 完成信息;
