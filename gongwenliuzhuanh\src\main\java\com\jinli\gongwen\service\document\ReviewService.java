package com.jinli.gongwen.service.document;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinli.gongwen.dto.document.DocumentReviewDTO;

import java.util.List;

/**
 * 审核服务接口
 */
public interface ReviewService {

    /**
     * 审核公文
     */
    void reviewDocument(DocumentReviewDTO reviewDTO);

    /**
     * 批量审核公文
     */
    void batchReviewDocuments(List<DocumentReviewDTO> reviewDTOList);

    /**
     * 获取审核历史
     */
    IPage<Object> getReviewHistory(Page<Object> page, String userId);

    /**
     * 退回公文
     */
    void returnDocument(String docId, String reason, String userId);
}
