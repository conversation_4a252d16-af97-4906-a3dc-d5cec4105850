package com.jinli.gongwen.vo.system;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 流程步骤视图对象
 */
@Data
@ApiModel("流程步骤视图对象")
public class FlowStepVO {

    @ApiModelProperty("步骤ID")
    private String stepId;

    @ApiModelProperty("步骤名称")
    private String stepName;

    @ApiModelProperty("步骤描述")
    private String stepDescription;

    @ApiModelProperty("步骤顺序")
    private Integer stepOrder;

    @ApiModelProperty("处理角色")
    private String handlerRole;

    @ApiModelProperty("是否必须")
    private Boolean isRequired;

    @ApiModelProperty("超时时间（小时）")
    private Integer timeoutHours;
}
