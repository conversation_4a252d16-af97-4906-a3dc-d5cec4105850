@echo off
echo === 测试CORS修复 ===

echo 1. 停止现有应用...
taskkill /f /im java.exe 2>nul
timeout /t 3 /nobreak >nul

echo 2. 启动应用...
cd /d "D:\Program Files (x86)\Javaxiangmu\gongwenliuzhuan\gongwenliuzhuanh"
start "Backend" mvn spring-boot:run

echo 3. 等待应用启动...
echo 请等待30秒让应用完全启动...
timeout /t 30 /nobreak >nul

echo 4. 测试API接口...
echo 测试统计接口:
curl -X GET "http://localhost:8080/api/document/statistics?userId=USER001" -H "Origin: http://localhost:3000"

echo.
echo 测试待处理接口:
curl -X GET "http://localhost:8080/api/document/pending?pageNum=1&pageSize=5&userId=USER001" -H "Origin: http://localhost:3000"

echo.
echo === 测试完成 ===
echo 如果没有CORS错误，说明修复成功！
pause
