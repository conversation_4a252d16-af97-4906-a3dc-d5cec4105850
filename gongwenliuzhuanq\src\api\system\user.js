import request from '@/utils/request'

// 获取用户列表
export function getUserList(params) {
  return request({
    url: '/api/system/user/list',
    method: 'get',
    params
  })
}

// 获取用户详情
export function getUserDetail(userId) {
  return request({
    url: `/api/system/user/${userId}`,
    method: 'get'
  })
}

// 新增用户
export function createUser(data) {
  return request({
    url: '/api/system/user',
    method: 'post',
    data
  })
}

// 修改用户
export function updateUser(userId, data) {
  return request({
    url: `/api/system/user/${userId}`,
    method: 'put',
    data
  })
}

// 删除用户
export function deleteUser(userId) {
  return request({
    url: `/api/system/user/${userId}`,
    method: 'delete'
  })
}

// 启用/停用用户
export function changeUserStatus(userId, status) {
  return request({
    url: `/api/system/user/${userId}/status`,
    method: 'put',
    params: { status }
  })
}

// 重置用户密码
export function resetPassword(userId) {
  return request({
    url: `/api/system/user/${userId}/resetPassword`,
    method: 'put'
  })
}

// 分配用户角色
export function assignUserRoles(userId, roleIds) {
  return request({
    url: `/api/system/user/${userId}/roles`,
    method: 'put',
    data: roleIds
  })
}

// 获取用户角色列表
export function getUserRoles(userId) {
  return request({
    url: `/api/system/user/${userId}/roles`,
    method: 'get'
  })
}
