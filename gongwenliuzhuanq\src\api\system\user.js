import request from '@/utils/request'

// 获取用户列表
export function getUserList(params) {
  return request({
    url: '/system/user/list',
    method: 'get',
    params
  })
}

// 获取用户详情
export function getUserDetail(userId) {
  return request({
    url: `/system/user/${userId}`,
    method: 'get'
  })
}

// 新增用户
export function createUser(data) {
  return request({
    url: '/system/user',
    method: 'post',
    data
  })
}

// 修改用户
export function updateUser(data) {
  return request({
    url: '/system/user',
    method: 'put',
    data
  })
}

// 删除用户
export function deleteUser(userId) {
  return request({
    url: `/system/user/${userId}`,
    method: 'delete'
  })
}

// 启用/停用用户
export function changeUserStatus(userId, status) {
  return request({
    url: `/system/user/${userId}/status`,
    method: 'put',
    params: { status }
  })
}

// 重置用户密码
export function resetPassword(userId, password) {
  return request({
    url: `/system/user/${userId}/password`,
    method: 'put',
    data: password
  })
}

// 分配用户角色
export function assignUserRoles(userId, roleIds) {
  return request({
    url: `/system/user/${userId}/roles`,
    method: 'put',
    data: roleIds
  })
}

// 获取用户角色列表
export function getUserRoles(userId) {
  return request({
    url: `/system/user/${userId}/roles`,
    method: 'get'
  })
}
