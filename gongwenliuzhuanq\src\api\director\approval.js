import request from '@/utils/request'

// 审签公文
export function approveDocument(docId, data) {
  return request({
    url: `/api/director/approve/${docId}`,
    method: 'post',
    data
  })
}

// 批量审签公文
export function batchApproveDocuments(data) {
  return request({
    url: '/api/director/batchApprove',
    method: 'post',
    data
  })
}

// 获取公文详情
export function getDocumentDetail(docId) {
  return request({
    url: `/api/director/document/${docId}`,
    method: 'get'
  })
}

// 获取所有公文
export function getAllDocuments(params) {
  return request({
    url: '/api/director/allDocuments',
    method: 'get',
    params
  })
}

// 获取审签历史记录
export function getApprovalHistory(params) {
  return request({
    url: '/api/director/approvalHistory',
    method: 'get',
    params
  })
}

// 退回公文
export function returnDocument(docId, reason) {
  return request({
    url: `/api/director/return/${docId}`,
    method: 'post',
    params: { reason }
  })
}
