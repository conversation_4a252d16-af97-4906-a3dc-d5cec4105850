package com.jinli.gongwen.controller.dept;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinli.gongwen.common.Result;
import com.jinli.gongwen.dto.document.DocumentDraftDTO;
import com.jinli.gongwen.dto.document.DocumentQueryDTO;
import com.jinli.gongwen.dto.document.DocumentReceiveDTO;
import com.jinli.gongwen.entity.document.DocDocument;
import com.jinli.gongwen.service.document.DocumentService;
import com.jinli.gongwen.service.document.ReceiveService;
import com.jinli.gongwen.vo.dept.DeptDashboardVO;
import com.jinli.gongwen.vo.document.DocumentVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 部门经理控制器
 * 负责公文拟制、签收、浏览功能
 */
@Api(tags = "部门经理功能")
@RestController
@RequestMapping("/api/dept/manager")
@PreAuthorize("hasRole('DEPT_MANAGER')")
public class DeptManagerController {

    @Autowired
    private DocumentService documentService;

    @Autowired
    private ReceiveService receiveService;

    /**
     * 部门经理仪表板
     */
    @ApiOperation("部门经理仪表板")
    @GetMapping("/dashboard")
    public Result<DeptDashboardVO> getDashboard() {
        DeptDashboardVO dashboard = documentService.getDeptManagerDashboard(getCurrentUserId(), getCurrentDeptId());
        return Result.success(dashboard);
    }

    /**
     * 获取待签收公文列表
     */
    @ApiOperation("获取待签收公文列表")
    @GetMapping("/pendingReceive")
    public Result<IPage<DocumentVO>> getPendingReceiveDocuments(DocumentQueryDTO queryDTO) {
        Page<DocDocument> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        QueryWrapper<DocDocument> queryWrapper = new QueryWrapper<>();
        
        // 查询发送给当前部门且未签收的公文
        queryWrapper.eq("doc_status", "OFFICE_SEND");
        queryWrapper.eq("target_dept_id", getCurrentDeptId());
        queryWrapper.eq("receive_status", "PENDING");
        
        // 构建查询条件
        if (queryDTO.getDocTitle() != null && !queryDTO.getDocTitle().trim().isEmpty()) {
            queryWrapper.like("doc_title", queryDTO.getDocTitle().trim());
        }
        if (queryDTO.getDocType() != null && !queryDTO.getDocType().trim().isEmpty()) {
            queryWrapper.eq("doc_type", queryDTO.getDocType());
        }
        if (queryDTO.getDocLevel() != null && !queryDTO.getDocLevel().trim().isEmpty()) {
            queryWrapper.eq("doc_level", queryDTO.getDocLevel());
        }
        
        queryWrapper.orderByDesc("create_time");
        
        IPage<DocumentVO> result = documentService.getDocumentList(page, queryWrapper);
        return Result.success(result);
    }

    /**
     * 获取本部门公文列表（已签收的）
     */
    @ApiOperation("获取本部门公文列表")
    @GetMapping("/documents")
    public Result<IPage<DocumentVO>> getDeptDocuments(DocumentQueryDTO queryDTO) {
        Page<DocDocument> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        QueryWrapper<DocDocument> queryWrapper = new QueryWrapper<>();
        
        // 查询本部门相关的公文（创建的或签收的）
        queryWrapper.and(wrapper -> 
            wrapper.eq("create_dept_id", getCurrentDeptId())
                   .or()
                   .eq("receive_dept_id", getCurrentDeptId())
        );
        
        // 构建查询条件
        if (queryDTO.getDocTitle() != null && !queryDTO.getDocTitle().trim().isEmpty()) {
            queryWrapper.like("doc_title", queryDTO.getDocTitle().trim());
        }
        if (queryDTO.getDocType() != null && !queryDTO.getDocType().trim().isEmpty()) {
            queryWrapper.eq("doc_type", queryDTO.getDocType());
        }
        if (queryDTO.getDocStatus() != null && !queryDTO.getDocStatus().trim().isEmpty()) {
            queryWrapper.eq("doc_status", queryDTO.getDocStatus());
        }
        
        queryWrapper.orderByDesc("create_time");
        
        IPage<DocumentVO> result = documentService.getDocumentList(page, queryWrapper);
        return Result.success(result);
    }

    /**
     * 创建公文草稿
     */
    @ApiOperation("创建公文草稿")
    @PostMapping("/draft")
    public Result<String> createDraft(@Valid @RequestBody DocumentDraftDTO draftDTO) {
        draftDTO.setCreateUserId(getCurrentUserId());
        draftDTO.setCreateDeptId(getCurrentDeptId());
        String docId = documentService.createDraft(draftDTO);
        return Result.success(docId);
    }

    /**
     * 修改公文草稿
     */
    @ApiOperation("修改公文草稿")
    @PutMapping("/draft/{docId}")
    public Result<Void> updateDraft(@PathVariable String docId, @Valid @RequestBody DocumentDraftDTO draftDTO) {
        draftDTO.setDocId(docId);
        documentService.updateDraft(draftDTO);
        return Result.success();
    }

    /**
     * 提交公文到办公室
     */
    @ApiOperation("提交公文到办公室")
    @PostMapping("/submit/{docId}")
    public Result<Void> submitDocument(@PathVariable String docId, @RequestParam(required = false) String submitRemark) {
        documentService.submitToOffice(docId, submitRemark);
        return Result.success();
    }

    /**
     * 签收公文
     */
    @ApiOperation("签收公文")
    @PostMapping("/receive/{docId}")
    public Result<Void> receiveDocument(@PathVariable String docId, @Valid @RequestBody DocumentReceiveDTO receiveDTO) {
        receiveDTO.setDocId(docId);
        receiveDTO.setReceiveUserId(getCurrentUserId());
        receiveDTO.setReceiveDeptId(getCurrentDeptId());
        receiveService.receiveDocument(receiveDTO);
        return Result.success();
    }

    /**
     * 批量签收公文
     */
    @ApiOperation("批量签收公文")
    @PostMapping("/batchReceive")
    public Result<Void> batchReceiveDocuments(@RequestBody List<String> docIds) {
        receiveService.batchReceiveDocuments(docIds, getCurrentUserId(), getCurrentDeptId());
        return Result.success();
    }

    /**
     * 获取公文详情
     */
    @ApiOperation("获取公文详情")
    @GetMapping("/document/{docId}")
    public Result<DocumentVO> getDocument(@PathVariable String docId) {
        DocumentVO documentVO = documentService.getDocumentById(docId);
        return Result.success(documentVO);
    }

    /**
     * 删除草稿
     */
    @ApiOperation("删除草稿")
    @DeleteMapping("/draft/{docId}")
    public Result<Void> deleteDraft(@PathVariable String docId) {
        documentService.deleteDraft(docId, getCurrentUserId());
        return Result.success();
    }

    /**
     * 获取公文模板列表
     */
    @ApiOperation("获取公文模板列表")
    @GetMapping("/templates")
    public Result<List<Object>> getDocumentTemplates() {
        List<Object> templates = documentService.getDocumentTemplates(getCurrentDeptId());
        return Result.success(templates);
    }

    /**
     * 获取部门统计信息
     */
    @ApiOperation("获取部门统计信息")
    @GetMapping("/statistics")
    public Result<Object> getDeptStatistics() {
        Object statistics = documentService.getDeptStatistics(getCurrentDeptId());
        return Result.success(statistics);
    }

    /**
     * 获取签收记录
     */
    @ApiOperation("获取签收记录")
    @GetMapping("/receiveRecords")
    public Result<IPage<Object>> getReceiveRecords(DocumentQueryDTO queryDTO) {
        Page<Object> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        IPage<Object> result = receiveService.getReceiveRecords(page, getCurrentDeptId());
        return Result.success(result);
    }

    /**
     * 获取当前用户ID（从安全上下文中获取）
     */
    private String getCurrentUserId() {
        // 从Spring Security上下文中获取当前用户ID
        // 这里简化处理，实际应该从SecurityContextHolder中获取
        return "USER007"; // 示例：生产部经理
    }

    /**
     * 获取当前部门ID（从安全上下文中获取）
     */
    private String getCurrentDeptId() {
        // 从Spring Security上下文中获取当前用户的部门ID
        // 这里简化处理，实际应该从SecurityContextHolder中获取
        return "DEPT003"; // 示例：生产部
    }
}
