<template>
  <div class="received-documents">
    <div class="page-header">
      <h1 class="page-title">已签收公文</h1>
      <div class="page-actions">
        <el-button type="primary" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="docId" label="公文编号" width="120" />
        <el-table-column prop="docTitle" label="公文标题" min-width="200" show-overflow-tooltip />
        <el-table-column prop="docType" label="公文类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getDocTypeColor(row.docType)" size="small">
              {{ getDocTypeName(row.docType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sender" label="发送人" width="100" />
        <el-table-column prop="receiveTime" label="签收时间" width="160">
          <template #default="{ row }">
            {{ formatTime(row.receiveTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="info" size="small" @click="handleView(row)">查看</el-button>
            <el-button type="primary" size="small" @click="handleReply(row)">回复</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getDocumentPage } from '@/api/document'
import dayjs from 'dayjs'
import { Refresh } from '@element-plus/icons-vue'

const loading = ref(false)
const tableData = ref([])

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

const formatTime = (timestamp) => {
  return timestamp ? dayjs(timestamp).format('YYYY-MM-DD HH:mm') : '-'
}

const getDocTypeName = (type) => {
  const typeMap = {
    'NOTICE': '通知',
    'REQUEST': '请示',
    'REPORT': '报告',
    'OPINION': '意见'
  }
  return typeMap[type] || type
}

const getDocTypeColor = (type) => {
  const colorMap = {
    'NOTICE': 'primary',
    'REQUEST': 'warning',
    'REPORT': 'success',
    'OPINION': 'info'
  }
  return colorMap[type] || 'info'
}

const loadData = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: pagination.current,
      pageSize: pagination.size,
      status: 'RECEIVED'
    }
    
    const response = await getDocumentPage(params)
    if (response.code === 200) {
      const receivedData = (response.data.records || []).map(item => ({
        ...item,
        sender: ['张部长', '李主任', '王科长'][Math.floor(Math.random() * 3)],
        receiveTime: Date.now() - Math.random() * 10 * 24 * 60 * 60 * 1000
      }))
      
      tableData.value = receivedData
      pagination.total = response.data.total || 0
    }
  } catch (error) {
    console.error('加载已签收公文失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  loadData()
}

const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  loadData()
}

const handleCurrentChange = (current) => {
  pagination.current = current
  loadData()
}

const handleView = (row) => {
  ElMessage.info(`查看公文: ${row.docTitle}`)
}

const handleReply = (row) => {
  ElMessage.info(`回复公文: ${row.docTitle}`)
}

onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.received-documents {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .page-title {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .table-card {
    .pagination-container {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }
  }
}
</style>
