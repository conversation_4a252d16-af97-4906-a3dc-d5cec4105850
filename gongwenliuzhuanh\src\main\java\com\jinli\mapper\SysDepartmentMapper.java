package com.jinli.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jinli.dto.DepartmentQueryDTO;
import com.jinli.entity.SysDepartment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 部门Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-30
 */
@Mapper
public interface SysDepartmentMapper extends BaseMapper<SysDepartment> {

    /**
     * 查询部门列表
     * 
     * @param queryDTO 查询条件
     * @return 部门列表
     */
    List<SysDepartment> selectDepartmentList(@Param("query") DepartmentQueryDTO queryDTO);

    /**
     * 根据部门ID查询部门详情
     * 
     * @param deptId 部门ID
     * @return 部门信息
     */
    SysDepartment selectDepartmentById(@Param("deptId") String deptId);

    /**
     * 查询子部门列表
     * 
     * @param parentId 父部门ID
     * @return 子部门列表
     */
    List<SysDepartment> selectChildrenByParentId(@Param("parentId") String parentId);

    /**
     * 查询部门下的用户数量
     * 
     * @param deptId 部门ID
     * @return 用户数量
     */
    Integer selectUserCountByDeptId(@Param("deptId") String deptId);

    /**
     * 检查部门编码是否存在
     * 
     * @param deptCode 部门编码
     * @param excludeDeptId 排除的部门ID
     * @return 存在的数量
     */
    Integer checkDeptCodeExists(@Param("deptCode") String deptCode, @Param("excludeDeptId") String excludeDeptId);

    /**
     * 检查部门名称是否存在（同一父部门下）
     * 
     * @param deptName 部门名称
     * @param parentId 父部门ID
     * @param excludeDeptId 排除的部门ID
     * @return 存在的数量
     */
    Integer checkDeptNameExists(@Param("deptName") String deptName, @Param("parentId") String parentId, @Param("excludeDeptId") String excludeDeptId);

    /**
     * 查询所有祖先部门
     * 
     * @param deptId 部门ID
     * @return 祖先部门列表
     */
    List<SysDepartment> selectAncestorDepartments(@Param("deptId") String deptId);

    /**
     * 查询所有后代部门
     * 
     * @param deptId 部门ID
     * @return 后代部门列表
     */
    List<SysDepartment> selectDescendantDepartments(@Param("deptId") String deptId);

    /**
     * 更新部门状态
     * 
     * @param deptId 部门ID
     * @param status 状态
     * @return 更新数量
     */
    Integer updateDepartmentStatus(@Param("deptId") String deptId, @Param("status") String status);

    /**
     * 批量更新子部门的父ID
     * 
     * @param oldParentId 旧父部门ID
     * @param newParentId 新父部门ID
     * @return 更新数量
     */
    Integer updateChildrenParentId(@Param("oldParentId") String oldParentId, @Param("newParentId") String newParentId);

    /**
     * 查询部门树形结构（用于下拉选择）
     * 
     * @param excludeDeptId 排除的部门ID
     * @return 部门树
     */
    List<SysDepartment> selectDepartmentTree(@Param("excludeDeptId") String excludeDeptId);

    /**
     * 查询根部门列表
     * 
     * @return 根部门列表
     */
    List<SysDepartment> selectRootDepartments();

    /**
     * 查询部门路径
     * 
     * @param deptId 部门ID
     * @return 部门路径
     */
    String selectDepartmentPath(@Param("deptId") String deptId);

    /**
     * 查询部门层级
     * 
     * @param deptId 部门ID
     * @return 部门层级
     */
    Integer selectDepartmentLevel(@Param("deptId") String deptId);

    /**
     * 查询同级部门的最大排序号
     * 
     * @param parentId 父部门ID
     * @return 最大排序号
     */
    Integer selectMaxSortByParentId(@Param("parentId") String parentId);

    /**
     * 批量删除部门
     * 
     * @param deptIds 部门ID列表
     * @return 删除数量
     */
    Integer batchDeleteDepartments(@Param("deptIds") List<String> deptIds);

    /**
     * 查询启用的部门列表
     * 
     * @return 启用的部门列表
     */
    List<SysDepartment> selectEnabledDepartments();

    /**
     * 查询部门统计信息
     * 
     * @return 统计信息
     */
    List<SysDepartment> selectDepartmentStatistics();
}
