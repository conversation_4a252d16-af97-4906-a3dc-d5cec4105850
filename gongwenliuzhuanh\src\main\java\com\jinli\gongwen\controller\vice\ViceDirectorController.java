package com.jinli.gongwen.controller.vice;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinli.gongwen.common.Result;
import com.jinli.gongwen.dto.document.DocumentQueryDTO;
import com.jinli.gongwen.dto.document.DocumentReviewDTO;
import com.jinli.gongwen.entity.document.DocDocument;
import com.jinli.gongwen.service.document.DocumentService;
import com.jinli.gongwen.service.document.ReviewService;
import com.jinli.gongwen.vo.document.DocumentVO;
import com.jinli.gongwen.vo.vice.ViceDashboardVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;

/**
 * 副厂长控制器
 * 负责公文审核功能
 */
@Api(tags = "副厂长功能")
@RestController
@RequestMapping("/api/vice")
@PreAuthorize("hasRole('VICE_DIRECTOR')")
public class ViceDirectorController {

    @Autowired
    private DocumentService documentService;

    @Autowired
    private ReviewService reviewService;

    /**
     * 副厂长仪表板
     */
    @ApiOperation("副厂长仪表板")
    @GetMapping("/dashboard")
    public Result<ViceDashboardVO> getDashboard() {
        ViceDashboardVO dashboard = documentService.getViceDirectorDashboard(getCurrentUserId(), getManagedDeptIds());
        return Result.success(dashboard);
    }

    /**
     * 获取待审核公文列表（按分管领域）
     */
    @ApiOperation("获取待审核公文列表")
    @GetMapping("/pendingReview")
    public Result<IPage<DocumentVO>> getPendingReviewDocuments(DocumentQueryDTO queryDTO) {
        Page<DocDocument> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        QueryWrapper<DocDocument> queryWrapper = new QueryWrapper<>();
        
        // 查询待副厂长审核的公文，且属于当前副厂长分管的部门
        queryWrapper.eq("doc_status", "VICE_REVIEW");
        queryWrapper.eq("current_handler", getCurrentUserId());
        queryWrapper.in("create_dept_id", getManagedDeptIds());
        
        // 构建查询条件
        if (queryDTO.getDocTitle() != null && !queryDTO.getDocTitle().trim().isEmpty()) {
            queryWrapper.like("doc_title", queryDTO.getDocTitle().trim());
        }
        if (queryDTO.getDocType() != null && !queryDTO.getDocType().trim().isEmpty()) {
            queryWrapper.eq("doc_type", queryDTO.getDocType());
        }
        if (queryDTO.getDocLevel() != null && !queryDTO.getDocLevel().trim().isEmpty()) {
            queryWrapper.eq("doc_level", queryDTO.getDocLevel());
        }
        if (queryDTO.getCreateDeptId() != null && !queryDTO.getCreateDeptId().trim().isEmpty()) {
            queryWrapper.eq("create_dept_id", queryDTO.getCreateDeptId());
        }
        
        queryWrapper.orderByDesc("create_time");
        
        IPage<DocumentVO> result = documentService.getDocumentList(page, queryWrapper);
        return Result.success(result);
    }

    /**
     * 获取已审核公文列表
     */
    @ApiOperation("获取已审核公文列表")
    @GetMapping("/reviewed")
    public Result<IPage<DocumentVO>> getReviewedDocuments(DocumentQueryDTO queryDTO) {
        Page<DocDocument> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        QueryWrapper<DocDocument> queryWrapper = new QueryWrapper<>();
        
        // 查询当前副厂长已审核的公文
        queryWrapper.exists("SELECT 1 FROM doc_flow_record dfr WHERE dfr.doc_id = doc_document.doc_id " +
                           "AND dfr.from_user_id = '" + getCurrentUserId() + "' " +
                           "AND dfr.action IN ('APPROVE', 'REJECT')");
        
        // 构建查询条件
        if (queryDTO.getDocTitle() != null && !queryDTO.getDocTitle().trim().isEmpty()) {
            queryWrapper.like("doc_title", queryDTO.getDocTitle().trim());
        }
        if (queryDTO.getDocType() != null && !queryDTO.getDocType().trim().isEmpty()) {
            queryWrapper.eq("doc_type", queryDTO.getDocType());
        }
        if (queryDTO.getReviewResult() != null && !queryDTO.getReviewResult().trim().isEmpty()) {
            queryWrapper.eq("review_result", queryDTO.getReviewResult());
        }
        
        queryWrapper.orderByDesc("update_time");
        
        IPage<DocumentVO> result = documentService.getDocumentList(page, queryWrapper);
        return Result.success(result);
    }

    /**
     * 审核公文
     */
    @ApiOperation("审核公文")
    @PostMapping("/review/{docId}")
    public Result<Void> reviewDocument(@PathVariable String docId, @Valid @RequestBody DocumentReviewDTO reviewDTO) {
        reviewDTO.setDocId(docId);
        reviewDTO.setReviewUserId(getCurrentUserId());
        reviewService.reviewDocument(reviewDTO);
        return Result.success();
    }

    /**
     * 获取公文详情
     */
    @ApiOperation("获取公文详情")
    @GetMapping("/document/{docId}")
    public Result<DocumentVO> getDocument(@PathVariable String docId) {
        DocumentVO documentVO = documentService.getDocumentById(docId);
        return Result.success(documentVO);
    }

    /**
     * 批量审核公文
     */
    @ApiOperation("批量审核公文")
    @PostMapping("/batchReview")
    public Result<Void> batchReviewDocuments(@RequestBody List<DocumentReviewDTO> reviewDTOList) {
        for (DocumentReviewDTO reviewDTO : reviewDTOList) {
            reviewDTO.setReviewUserId(getCurrentUserId());
        }
        reviewService.batchReviewDocuments(reviewDTOList);
        return Result.success();
    }

    /**
     * 获取分管部门的公文统计
     */
    @ApiOperation("获取分管部门的公文统计")
    @GetMapping("/deptStatistics")
    public Result<Object> getDeptStatistics() {
        Object statistics = documentService.getViceDeptStatistics(getCurrentUserId(), getManagedDeptIds());
        return Result.success(statistics);
    }

    /**
     * 获取审核历史记录
     */
    @ApiOperation("获取审核历史记录")
    @GetMapping("/reviewHistory")
    public Result<IPage<Object>> getReviewHistory(DocumentQueryDTO queryDTO) {
        Page<Object> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        IPage<Object> result = reviewService.getReviewHistory(page, getCurrentUserId());
        return Result.success(result);
    }

    /**
     * 获取可查看的所有已发公文
     */
    @ApiOperation("获取可查看的所有已发公文")
    @GetMapping("/allDocuments")
    public Result<IPage<DocumentVO>> getAllDocuments(DocumentQueryDTO queryDTO) {
        Page<DocDocument> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        QueryWrapper<DocDocument> queryWrapper = new QueryWrapper<>();
        
        // 副厂长可以查看所有已发布的公文
        queryWrapper.in("doc_status", "COMPLETED", "DEPT_RECEIVE");
        
        // 构建查询条件
        if (queryDTO.getDocTitle() != null && !queryDTO.getDocTitle().trim().isEmpty()) {
            queryWrapper.like("doc_title", queryDTO.getDocTitle().trim());
        }
        if (queryDTO.getDocType() != null && !queryDTO.getDocType().trim().isEmpty()) {
            queryWrapper.eq("doc_type", queryDTO.getDocType());
        }
        if (queryDTO.getCreateDeptId() != null && !queryDTO.getCreateDeptId().trim().isEmpty()) {
            queryWrapper.eq("create_dept_id", queryDTO.getCreateDeptId());
        }
        if (queryDTO.getStartTime() != null && !queryDTO.getStartTime().trim().isEmpty()) {
            queryWrapper.ge("create_time", queryDTO.getStartTime());
        }
        if (queryDTO.getEndTime() != null && !queryDTO.getEndTime().trim().isEmpty()) {
            queryWrapper.le("create_time", queryDTO.getEndTime());
        }
        
        queryWrapper.orderByDesc("create_time");
        
        IPage<DocumentVO> result = documentService.getDocumentList(page, queryWrapper);
        return Result.success(result);
    }

    /**
     * 退回公文
     */
    @ApiOperation("退回公文")
    @PostMapping("/return/{docId}")
    public Result<Void> returnDocument(@PathVariable String docId, @RequestParam String reason) {
        reviewService.returnDocument(docId, reason, getCurrentUserId());
        return Result.success();
    }

    /**
     * 获取当前用户ID（从安全上下文中获取）
     */
    private String getCurrentUserId() {
        // 从Spring Security上下文中获取当前用户ID
        // 这里简化处理，实际应该从SecurityContextHolder中获取
        return "USER003"; // 示例：生产副厂长
    }

    /**
     * 获取当前副厂长分管的部门ID列表
     */
    private List<String> getManagedDeptIds() {
        // 根据当前用户获取分管的部门列表
        // 这里简化处理，实际应该从数据库中查询
        String userId = getCurrentUserId();
        if ("USER003".equals(userId)) {
            return Arrays.asList("DEPT003"); // 生产副厂长管理生产部
        } else if ("USER004".equals(userId)) {
            return Arrays.asList("DEPT004"); // 销售副厂长管理销售部
        } else if ("USER005".equals(userId)) {
            return Arrays.asList("DEPT005"); // 财务副厂长管理财务部
        }
        return Arrays.asList();
    }
}
