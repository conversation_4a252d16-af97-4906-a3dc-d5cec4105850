<template>
  <div class="system-monitor">
    <!-- 系统状态概览 -->
    <el-row :gutter="20" class="status-row">
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon cpu">
              <el-icon><Cpu /></el-icon>
            </div>
            <div class="status-info">
              <div class="status-value">{{ systemStatus.cpuUsage }}%</div>
              <div class="status-label">CPU使用率</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon memory">
              <el-icon><Monitor /></el-icon>
            </div>
            <div class="status-info">
              <div class="status-value">{{ systemStatus.memoryUsage }}%</div>
              <div class="status-label">内存使用率</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon disk">
              <el-icon><FolderOpened /></el-icon>
            </div>
            <div class="status-info">
              <div class="status-value">{{ systemStatus.diskUsage }}%</div>
              <div class="status-label">磁盘使用率</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon online">
              <el-icon><User /></el-icon>
            </div>
            <div class="status-info">
              <div class="status-value">{{ systemStatus.onlineUsers }}</div>
              <div class="status-label">在线用户</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 系统信息 -->
    <el-row :gutter="20" class="info-row">
      <el-col :span="12">
        <el-card class="content-card">
          <template #header>
            <span>系统信息</span>
            <el-button type="primary" size="small" @click="refreshSystemInfo">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </template>
          
          <el-descriptions :column="1" border>
            <el-descriptions-item label="系统名称">{{ systemInfo.name }}</el-descriptions-item>
            <el-descriptions-item label="系统版本">{{ systemInfo.version }}</el-descriptions-item>
            <el-descriptions-item label="运行时间">{{ systemInfo.uptime }}</el-descriptions-item>
            <el-descriptions-item label="Java版本">{{ systemInfo.javaVersion }}</el-descriptions-item>
            <el-descriptions-item label="数据库">{{ systemInfo.database }}</el-descriptions-item>
            <el-descriptions-item label="服务器IP">{{ systemInfo.serverIp }}</el-descriptions-item>
            <el-descriptions-item label="启动时间">{{ systemInfo.startTime }}</el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card class="content-card">
          <template #header>
            <span>在线用户</span>
          </template>
          
          <el-table :data="onlineUserList" height="300">
            <el-table-column prop="username" label="用户名" width="120" />
            <el-table-column prop="realName" label="姓名" width="100" />
            <el-table-column prop="department" label="部门" />
            <el-table-column prop="loginTime" label="登录时间" width="150" />
            <el-table-column prop="lastActivity" label="最后活动" width="150" />
            <el-table-column label="操作" width="80">
              <template #default="{ row }">
                <el-button 
                  type="danger" 
                  size="small" 
                  @click="forceLogout(row)"
                  v-if="row.username !== 'admin'"
                >
                  踢出
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- 系统日志监控 -->
    <el-row :gutter="20" class="log-row">
      <el-col :span="24">
        <el-card class="content-card">
          <template #header>
            <div class="card-header">
              <span>实时日志监控</span>
              <div>
                <el-button @click="clearLogs" type="warning" size="small">
                  <el-icon><Delete /></el-icon>
                  清空日志
                </el-button>
                <el-button @click="toggleAutoRefresh" :type="autoRefresh ? 'success' : 'info'" size="small">
                  <el-icon><Timer /></el-icon>
                  {{ autoRefresh ? '停止' : '开始' }}自动刷新
                </el-button>
              </div>
            </div>
          </template>
          
          <div class="log-container">
            <div 
              v-for="(log, index) in realtimeLogs" 
              :key="index" 
              :class="['log-item', log.level.toLowerCase()]"
            >
              <span class="log-time">{{ log.time }}</span>
              <span class="log-level">{{ log.level }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据库连接监控 -->
    <el-row :gutter="20" class="db-row">
      <el-col :span="24">
        <el-card class="content-card">
          <template #header>
            <span>数据库连接监控</span>
          </template>
          
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="db-stat">
                <div class="db-stat-value">{{ dbStatus.activeConnections }}</div>
                <div class="db-stat-label">活跃连接</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="db-stat">
                <div class="db-stat-value">{{ dbStatus.maxConnections }}</div>
                <div class="db-stat-label">最大连接</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="db-stat">
                <div class="db-stat-value">{{ dbStatus.connectionPool }}</div>
                <div class="db-stat-label">连接池大小</div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const autoRefresh = ref(false)
let refreshTimer = null

// 系统状态
const systemStatus = reactive({
  cpuUsage: 45,
  memoryUsage: 68,
  diskUsage: 32,
  onlineUsers: 8
})

// 系统信息
const systemInfo = reactive({
  name: '金利科技公文流转系统',
  version: 'v1.0.0',
  uptime: '3天 12小时 45分钟',
  javaVersion: 'OpenJDK 11.0.2',
  database: '达梦数据库 v8.0',
  serverIp: '*************',
  startTime: '2025-01-28 09:30:00'
})

// 在线用户列表
const onlineUserList = ref([
  {
    username: 'admin',
    realName: '系统管理员',
    department: '信息中心',
    loginTime: '2025-01-30 08:30:00',
    lastActivity: '2025-01-30 14:25:00'
  },
  {
    username: 'factory_director',
    realName: '张厂长',
    department: '厂长办公室',
    loginTime: '2025-01-30 09:00:00',
    lastActivity: '2025-01-30 14:20:00'
  },
  {
    username: 'office_director',
    realName: '孙主任',
    department: '办公室',
    loginTime: '2025-01-30 08:45:00',
    lastActivity: '2025-01-30 14:18:00'
  }
])

// 实时日志
const realtimeLogs = ref([
  { time: '14:25:30', level: 'INFO', message: '用户[张厂长]登录系统' },
  { time: '14:24:15', level: 'INFO', message: '公文[金利技发〔2025〕001号]审批完成' },
  { time: '14:23:45', level: 'WARN', message: '用户[李副厂长]登录失败，密码错误' },
  { time: '14:22:30', level: 'INFO', message: '系统定时任务执行完成' },
  { time: '14:21:20', level: 'ERROR', message: '邮件发送失败，SMTP连接超时' }
])

// 数据库状态
const dbStatus = reactive({
  activeConnections: 12,
  maxConnections: 100,
  connectionPool: 20
})

// 方法
const refreshSystemInfo = async () => {
  try {
    // 这里应该调用API获取系统信息
    // const response = await getSystemInfo()
    // Object.assign(systemInfo, response.data)
    
    ElMessage.success('系统信息刷新成功')
  } catch (error) {
    ElMessage.error('系统信息刷新失败')
  }
}

const forceLogout = async (user) => {
  try {
    await ElMessageBox.confirm(`确定要强制用户 ${user.realName} 下线吗？`, '确认操作', {
      type: 'warning'
    })
    
    // 这里应该调用API强制用户下线
    // await forceUserLogout(user.username)
    
    // 从列表中移除用户
    const index = onlineUserList.value.findIndex(u => u.username === user.username)
    if (index > -1) {
      onlineUserList.value.splice(index, 1)
    }
    
    ElMessage.success('用户已强制下线')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

const clearLogs = async () => {
  try {
    await ElMessageBox.confirm('确定要清空实时日志吗？', '确认操作', {
      type: 'warning'
    })
    
    realtimeLogs.value = []
    ElMessage.success('日志已清空')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value
  
  if (autoRefresh.value) {
    refreshTimer = setInterval(() => {
      // 模拟实时数据更新
      systemStatus.cpuUsage = Math.floor(Math.random() * 100)
      systemStatus.memoryUsage = Math.floor(Math.random() * 100)
      systemStatus.onlineUsers = Math.floor(Math.random() * 20) + 1
      
      // 添加新日志
      const newLog = {
        time: new Date().toLocaleTimeString(),
        level: ['INFO', 'WARN', 'ERROR'][Math.floor(Math.random() * 3)],
        message: '系统监控数据更新'
      }
      realtimeLogs.value.unshift(newLog)
      
      // 保持日志数量不超过50条
      if (realtimeLogs.value.length > 50) {
        realtimeLogs.value = realtimeLogs.value.slice(0, 50)
      }
    }, 5000)
  } else {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
  }
}

// 生命周期
onMounted(() => {
  refreshSystemInfo()
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style scoped>
.system-monitor {
  padding: 20px;
}

.status-row {
  margin-bottom: 20px;
}

.status-card {
  height: 120px;
}

.status-item {
  display: flex;
  align-items: center;
  height: 100%;
}

.status-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.status-icon.cpu { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.status-icon.memory { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.status-icon.disk { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.status-icon.online { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

.status-info {
  flex: 1;
}

.status-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.status-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.info-row, .log-row, .db-row {
  margin-bottom: 20px;
}

.content-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.log-container {
  height: 300px;
  overflow-y: auto;
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
}

.log-item {
  display: flex;
  margin-bottom: 8px;
  padding: 5px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-item.info { background: #e8f4fd; }
.log-item.warn { background: #fdf6ec; }
.log-item.error { background: #fef0f0; }

.log-time {
  color: #909399;
  margin-right: 10px;
  min-width: 80px;
}

.log-level {
  font-weight: bold;
  margin-right: 10px;
  min-width: 50px;
}

.log-level:contains('INFO') { color: #409eff; }
.log-level:contains('WARN') { color: #e6a23c; }
.log-level:contains('ERROR') { color: #f56c6c; }

.log-message {
  flex: 1;
}

.db-stat {
  text-align: center;
  padding: 20px;
}

.db-stat-value {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
}

.db-stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 10px;
}
</style>
