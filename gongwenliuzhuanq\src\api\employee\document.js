import request from '@/utils/request'

// 协助创建简单公文草稿
export function assistCreateDraft(data) {
  return request({
    url: '/api/employee/assistDraft',
    method: 'post',
    data
  })
}

// 签收公文
export function receiveDocument(docId, data) {
  return request({
    url: `/api/employee/receive/${docId}`,
    method: 'post',
    data
  })
}

// 获取公文详情
export function getDocumentDetail(docId) {
  return request({
    url: `/api/employee/document/${docId}`,
    method: 'get'
  })
}

// 获取公文通知
export function getNotifications(params) {
  return request({
    url: '/api/employee/notifications',
    method: 'get',
    params
  })
}

// 标记通知为已读
export function markNotificationAsRead(notificationId) {
  return request({
    url: `/api/employee/notification/${notificationId}/read`,
    method: 'put'
  })
}

// 获取简化的公文模板
export function getSimpleTemplates() {
  return request({
    url: '/api/employee/simpleTemplates',
    method: 'get'
  })
}

// 提交协助意见
export function submitAssistance(docId, assistance) {
  return request({
    url: `/api/employee/submitAssistance/${docId}`,
    method: 'post',
    params: { assistance }
  })
}
