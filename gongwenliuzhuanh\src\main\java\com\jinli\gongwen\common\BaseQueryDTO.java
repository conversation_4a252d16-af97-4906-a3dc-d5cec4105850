package com.jinli.gongwen.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 基础查询数据传输对象
 */
@Data
@ApiModel("基础查询数据传输对象")
public class BaseQueryDTO {

    @ApiModelProperty(value = "页码", example = "1")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "每页大小", example = "10")
    private Integer pageSize = 10;

    @ApiModelProperty("排序字段")
    private String orderBy;

    @ApiModelProperty("排序方向（asc/desc）")
    private String orderDirection = "desc";
}
