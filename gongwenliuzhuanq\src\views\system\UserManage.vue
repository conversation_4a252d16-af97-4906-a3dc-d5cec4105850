<template>
  <div class="user-manage">
    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="queryForm" inline class="search-form">
        <el-form-item label="用户名">
          <el-input
            v-model="queryForm.username"
            placeholder="请输入用户名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="真实姓名">
          <el-input
            v-model="queryForm.realName"
            placeholder="请输入真实姓名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="部门">
          <el-select
            v-model="queryForm.departmentId"
            placeholder="请选择部门"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="dept in departmentList"
              :key="dept.deptId"
              :label="dept.deptName"
              :value="dept.deptId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="queryForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="正常" value="1" />
            <el-option label="停用" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <i class="el-icon-search"></i> 查询
          </el-button>
          <el-button @click="resetQuery">
            <i class="el-icon-refresh"></i> 重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作工具栏 -->
    <el-card class="toolbar-card">
      <div class="toolbar">
        <div class="toolbar-left">
          <el-button type="primary" @click="handleAdd">
            <i class="el-icon-plus"></i> 新增用户
          </el-button>
          <el-button
            type="danger"
            :disabled="!multipleSelection.length"
            @click="handleBatchDelete"
          >
            <i class="el-icon-delete"></i> 批量删除
          </el-button>
        </div>
        <div class="toolbar-right">
          <el-button @click="handleRefresh">
            <i class="el-icon-refresh"></i> 刷新
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 用户表格 -->
    <el-card class="table-card">
      <el-table
        :data="userList"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="username" label="用户名" width="120" />
        <el-table-column prop="realName" label="真实姓名" width="120" />
        <el-table-column prop="departmentName" label="部门" width="150" />
        <el-table-column prop="roleNames" label="角色" min-width="200" />
        <el-table-column prop="phone" label="手机号" width="130" />
        <el-table-column prop="email" label="邮箱" min-width="180" />
        <el-table-column prop="status" label="状态" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === '1' ? 'success' : 'danger'">
              {{ scope.row.status === '1' ? '正常' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160" />
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleView(scope.row)">
              查看
            </el-button>
            <el-button size="mini" type="primary" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-dropdown @command="handleCommand" trigger="click">
              <el-button size="mini">
                更多<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item :command="{action: 'resetPassword', row: scope.row}">
                  重置密码
                </el-dropdown-item>
                <el-dropdown-item :command="{action: 'assignRole', row: scope.row}">
                  分配角色
                </el-dropdown-item>
                <el-dropdown-item
                  :command="{action: 'changeStatus', row: scope.row}"
                  :divided="true"
                >
                  {{ scope.row.status === '1' ? '停用' : '启用' }}
                </el-dropdown-item>
                <el-dropdown-item
                  :command="{action: 'delete', row: scope.row}"
                  style="color: #f56c6c"
                >
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="queryForm.pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="queryForm.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        />
      </div>
    </el-card>

    <!-- 用户表单对话框 -->
    <UserForm
      :visible.sync="formVisible"
      :form-data="formData"
      :is-edit="isEdit"
      @success="handleFormSuccess"
    />

    <!-- 角色分配对话框 -->
    <RoleAssign
      :visible.sync="roleAssignVisible"
      :user-id="currentUserId"
      @success="handleRoleAssignSuccess"
    />
  </div>
</template>

<script>
import { getUserList, deleteUser, changeUserStatus, resetPassword } from '@/api/system/user'
import { getDepartmentList } from '@/api/system/department'
import UserForm from './components/UserForm'
import RoleAssign from './components/RoleAssign'

export default {
  name: 'UserManage',
  components: {
    UserForm,
    RoleAssign
  },
  data() {
    return {
      loading: false,
      userList: [],
      departmentList: [],
      total: 0,
      multipleSelection: [],
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        username: '',
        realName: '',
        departmentId: '',
        status: ''
      },
      formVisible: false,
      formData: {},
      isEdit: false,
      roleAssignVisible: false,
      currentUserId: ''
    }
  },
  created() {
    this.getList()
    this.getDepartmentList()
  },
  methods: {
    // 获取用户列表
    async getList() {
      this.loading = true
      try {
        const response = await getUserList(this.queryForm)
        this.userList = response.data.records
        this.total = response.data.total
      } catch (error) {
        this.$message.error('获取用户列表失败')
      } finally {
        this.loading = false
      }
    },

    // 获取部门列表
    async getDepartmentList() {
      try {
        const response = await getDepartmentList()
        this.departmentList = response.data
      } catch (error) {
        console.error('获取部门列表失败', error)
      }
    },

    // 查询
    handleQuery() {
      this.queryForm.pageNum = 1
      this.getList()
    },

    // 重置查询
    resetQuery() {
      this.queryForm = {
        pageNum: 1,
        pageSize: 10,
        username: '',
        realName: '',
        departmentId: '',
        status: ''
      }
      this.getList()
    },

    // 刷新
    handleRefresh() {
      this.getList()
    },

    // 新增用户
    handleAdd() {
      this.formData = {}
      this.isEdit = false
      this.formVisible = true
    },

    // 编辑用户
    handleEdit(row) {
      this.formData = { ...row }
      this.isEdit = true
      this.formVisible = true
    },

    // 查看用户
    handleView(row) {
      this.formData = { ...row }
      this.isEdit = false
      this.formVisible = true
    },

    // 表单成功回调
    handleFormSuccess() {
      this.formVisible = false
      this.getList()
    },

    // 多选变化
    handleSelectionChange(selection) {
      this.multipleSelection = selection
    },

    // 分页大小变化
    handleSizeChange(size) {
      this.queryForm.pageSize = size
      this.getList()
    },

    // 当前页变化
    handleCurrentChange(page) {
      this.queryForm.pageNum = page
      this.getList()
    },

    // 下拉菜单命令处理
    async handleCommand(command) {
      const { action, row } = command
      switch (action) {
        case 'resetPassword':
          await this.handleResetPassword(row)
          break
        case 'assignRole':
          this.handleAssignRole(row)
          break
        case 'changeStatus':
          await this.handleChangeStatus(row)
          break
        case 'delete':
          await this.handleDelete(row)
          break
      }
    },

    // 重置密码
    async handleResetPassword(row) {
      try {
        await this.$confirm('确认重置该用户密码为123456？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await resetPassword(row.userId)
        this.$message.success('密码重置成功')
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('密码重置失败')
        }
      }
    },

    // 分配角色
    handleAssignRole(row) {
      this.currentUserId = row.userId
      this.roleAssignVisible = true
    },

    // 角色分配成功回调
    handleRoleAssignSuccess() {
      this.roleAssignVisible = false
      this.getList()
    },

    // 改变状态
    async handleChangeStatus(row) {
      const status = row.status === '1' ? '0' : '1'
      const statusText = status === '1' ? '启用' : '停用'
      try {
        await this.$confirm(`确认${statusText}该用户？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await changeUserStatus(row.userId, status)
        this.$message.success(`${statusText}成功`)
        this.getList()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error(`${statusText}失败`)
        }
      }
    },

    // 删除用户
    async handleDelete(row) {
      try {
        await this.$confirm('确认删除该用户？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await deleteUser(row.userId)
        this.$message.success('删除成功')
        this.getList()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败')
        }
      }
    },

    // 批量删除
    async handleBatchDelete() {
      if (!this.multipleSelection.length) {
        this.$message.warning('请选择要删除的用户')
        return
      }
      try {
        await this.$confirm(`确认删除选中的${this.multipleSelection.length}个用户？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        // 批量删除逻辑
        for (const user of this.multipleSelection) {
          await deleteUser(user.userId)
        }
        this.$message.success('批量删除成功')
        this.getList()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('批量删除失败')
        }
      }
    }
  }
}
</script>

<style scoped>
.user-manage {
  padding: 20px;
}

.search-card,
.toolbar-card,
.table-card {
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 0;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .user-manage {
    padding: 10px;
  }
  
  .search-form .el-form-item {
    margin-bottom: 10px;
  }
}

@media (max-width: 800px) {
  .toolbar {
    flex-direction: column;
    gap: 10px;
  }
  
  .toolbar-left,
  .toolbar-right {
    width: 100%;
  }
}
</style>
