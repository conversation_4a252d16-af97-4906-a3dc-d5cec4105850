import request from '@/utils/request'

// 创建公文草稿
export function createDraft(data) {
  return request({
    url: '/api/dept/manager/draft',
    method: 'post',
    data
  })
}

// 修改公文草稿
export function updateDraft(docId, data) {
  return request({
    url: `/api/dept/manager/draft/${docId}`,
    method: 'put',
    data
  })
}

// 提交公文到办公室
export function submitDocument(docId, submitRemark) {
  return request({
    url: `/api/dept/manager/submit/${docId}`,
    method: 'post',
    params: { submitRemark }
  })
}

// 签收公文
export function receiveDocument(docId, data) {
  return request({
    url: `/api/dept/manager/receive/${docId}`,
    method: 'post',
    data
  })
}

// 批量签收公文
export function batchReceiveDocuments(docIds) {
  return request({
    url: '/api/dept/manager/batchReceive',
    method: 'post',
    data: docIds
  })
}

// 获取公文详情
export function getDocumentDetail(docId) {
  return request({
    url: `/api/dept/manager/document/${docId}`,
    method: 'get'
  })
}

// 删除草稿
export function deleteDraft(docId) {
  return request({
    url: `/api/dept/manager/draft/${docId}`,
    method: 'delete'
  })
}

// 获取公文模板列表
export function getDocumentTemplates() {
  return request({
    url: '/api/dept/manager/templates',
    method: 'get'
  })
}

// 获取签收记录
export function getReceiveRecords(params) {
  return request({
    url: '/api/dept/manager/receiveRecords',
    method: 'get',
    params
  })
}
