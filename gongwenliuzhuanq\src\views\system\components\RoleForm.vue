<template>
  <el-dialog
    :title="isEdit ? '编辑角色' : '新增角色'"
    :visible.sync="dialogVisible"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="roleForm"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="角色名称" prop="roleName">
        <el-input
          v-model="form.roleName"
          placeholder="请输入角色名称"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="角色标识" prop="roleKey">
        <el-input
          v-model="form.roleKey"
          placeholder="请输入角色标识"
          maxlength="50"
          show-word-limit
          :disabled="isEdit"
        />
        <div class="form-tip">角色标识用于系统内部识别，创建后不可修改</div>
      </el-form-item>
      
      <el-form-item label="显示顺序" prop="roleSort">
        <el-input-number
          v-model="form.roleSort"
          :min="0"
          :max="999"
          controls-position="right"
          style="width: 200px"
        />
      </el-form-item>
      
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio label="1">正常</el-radio>
          <el-radio label="0">停用</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading">
        {{ isEdit ? '更新' : '创建' }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { createRole, updateRole, getRoleById } from '@/api/system/role'

export default {
  name: 'RoleForm',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    formData: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      form: {
        roleName: '',
        roleKey: '',
        roleSort: 0,
        status: '1',
        remark: ''
      },
      rules: {
        roleName: [
          { required: true, message: '请输入角色名称', trigger: 'blur' },
          { min: 2, max: 50, message: '角色名称长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        roleKey: [
          { required: true, message: '请输入角色标识', trigger: 'blur' },
          { min: 2, max: 50, message: '角色标识长度在 2 到 50 个字符', trigger: 'blur' },
          { pattern: /^[A-Z_]+$/, message: '角色标识只能包含大写字母和下划线', trigger: 'blur' }
        ],
        roleSort: [
          { required: true, message: '请输入显示顺序', trigger: 'blur' },
          { type: 'number', min: 0, max: 999, message: '显示顺序必须在 0 到 999 之间', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm()
      }
    }
  },
  methods: {
    // 初始化表单
    initForm() {
      this.$nextTick(() => {
        this.$refs.roleForm?.clearValidate()
      })
      
      if (this.isEdit && this.formData.roleId) {
        // 编辑模式，复制数据
        this.form = {
          roleId: this.formData.roleId,
          roleName: this.formData.roleName || '',
          roleKey: this.formData.roleKey || '',
          roleSort: this.formData.roleSort || 0,
          status: this.formData.status || '1',
          remark: this.formData.remark || ''
        }
      } else {
        // 新增模式，重置表单
        this.form = {
          roleName: '',
          roleKey: '',
          roleSort: 0,
          status: '1',
          remark: ''
        }
      }
    },
    
    // 提交表单
    async handleSubmit() {
      try {
        await this.$refs.roleForm.validate()
        this.loading = true
        
        if (this.isEdit) {
          await updateRole(this.form.roleId, this.form)
          this.$message.success('角色更新成功')
        } else {
          await createRole(this.form)
          this.$message.success('角色创建成功')
        }
        
        this.$emit('success')
        this.handleClose()
      } catch (error) {
        if (error !== false) { // 不是表单验证错误
          this.$message.error(this.isEdit ? '角色更新失败' : '角色创建失败')
        }
      } finally {
        this.loading = false
      }
    },
    
    // 关闭对话框
    handleClose() {
      this.dialogVisible = false
      this.$refs.roleForm?.resetFields()
    }
  }
}
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.dialog-footer {
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .el-dialog {
    width: 95% !important;
    margin: 5vh auto !important;
  }
}
</style>
