package com.jinli.gongwen.dto.document;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 公文流转数据传输对象
 */
@Data
@ApiModel("公文流转数据传输对象")
public class DocumentFlowDTO {

    @ApiModelProperty("公文ID")
    private String docId;

    @ApiModelProperty(value = "流转动作", required = true)
    @NotBlank(message = "流转动作不能为空")
    private String action; // SUBMIT, APPROVE, REJECT, TRANSFER, SEND

    @ApiModelProperty("目标用户ID")
    private String toUserId;

    @ApiModelProperty("目标部门ID")
    private String toDeptId;

    @ApiModelProperty("流转步骤")
    private String flowStep;

    @ApiModelProperty("处理意见")
    @Size(max = 500, message = "处理意见长度不能超过500个字符")
    private String opinion;

    @ApiModelProperty("流转说明")
    @Size(max = 500, message = "流转说明长度不能超过500个字符")
    private String flowRemark;

    @ApiModelProperty("是否同意")
    private Boolean isApproved;

    @ApiModelProperty("紧急程度")
    private String urgency; // NORMAL, URGENT, VERY_URGENT
}
