-- 系统日志表
CREATE TABLE IF NOT EXISTS `sys_log` (
    `log_id` varchar(64) NOT NULL COMMENT '日志ID',
    `username` varchar(50) DEFAULT NULL COMMENT '操作用户名',
    `real_name` varchar(50) DEFAULT NULL COMMENT '用户真实姓名',
    `module` varchar(50) DEFAULT NULL COMMENT '操作模块',
    `operation_type` varchar(50) DEFAULT NULL COMMENT '操作类型',
    `description` varchar(500) DEFAULT NULL COMMENT '操作描述',
    `request_url` varchar(500) DEFAULT NULL COMMENT '请求URL',
    `request_method` varchar(10) DEFAULT NULL COMMENT '请求方法',
    `request_params` text COMMENT '请求参数',
    `response_result` text COMMENT '响应结果',
    `ip` varchar(50) DEFAULT NULL COMMENT '操作IP地址',
    `user_agent` varchar(1000) DEFAULT NULL COMMENT '用户代理',
    `status` varchar(20) DEFAULT NULL COMMENT '操作状态（SUCCESS-成功，FAIL-失败）',
    `error_msg` text COMMENT '错误信息',
    `response_time` bigint DEFAULT NULL COMMENT '响应时间（毫秒）',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`log_id`),
    KEY `idx_username` (`username`),
    KEY `idx_module` (`module`),
    KEY `idx_operation_type` (`operation_type`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_ip` (`ip`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统日志表';

-- 插入测试数据
INSERT INTO `sys_log` (`log_id`, `username`, `real_name`, `module`, `operation_type`, `description`, `request_url`, `request_method`, `request_params`, `ip`, `user_agent`, `status`, `create_time`) VALUES
('LOG001', 'admin', '系统管理员', 'USER', 'LOGIN', '用户登录系统', '/auth/login', 'POST', '{"username":"admin"}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'SUCCESS', NOW() - INTERVAL 1 HOUR),
('LOG002', 'admin', '系统管理员', 'DOCUMENT', 'SELECT', '查询公文列表', '/system/document/list', 'GET', '{"pageNum":1,"pageSize":10}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'SUCCESS', NOW() - INTERVAL 2 HOUR),
('LOG003', 'zhangsan', '张三', 'DOCUMENT', 'CREATE', '创建新公文', '/system/document', 'POST', '{"title":"测试公文","content":"测试内容"}', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'SUCCESS', NOW() - INTERVAL 3 HOUR),
('LOG004', 'lisi', '李四', 'USER', 'UPDATE', '修改用户信息', '/system/user', 'PUT', '{"userId":"123","realName":"李四"}', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'FAIL', NOW() - INTERVAL 4 HOUR),
('LOG005', 'wangwu', '王五', 'DEPT', 'CREATE', '新增部门', '/system/department', 'POST', '{"deptName":"测试部门","deptCode":"TEST_DEPT"}', '192.168.1.102', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'SUCCESS', NOW() - INTERVAL 5 HOUR),
('LOG006', 'admin', '系统管理员', 'ROLE', 'UPDATE', '修改角色权限', '/system/role', 'PUT', '{"roleId":"456","permissions":["user:list","user:add"]}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'SUCCESS', NOW() - INTERVAL 6 HOUR),
('LOG007', 'zhangsan', '张三', 'DOCUMENT', 'DELETE', '删除公文', '/system/document/789', 'DELETE', '{}', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'SUCCESS', NOW() - INTERVAL 7 HOUR),
('LOG008', 'lisi', '李四', 'SYSTEM', 'UPDATE', '修改系统配置', '/system/config', 'PUT', '{"configKey":"system.title","configValue":"公文流转系统"}', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'SUCCESS', NOW() - INTERVAL 8 HOUR),
('LOG009', 'wangwu', '王五', 'USER', 'LOGIN', '用户登录系统', '/auth/login', 'POST', '{"username":"wangwu"}', '192.168.1.102', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'SUCCESS', NOW() - INTERVAL 9 HOUR),
('LOG010', 'admin', '系统管理员', 'DOCUMENT', 'SELECT', '导出公文列表', '/system/document/export', 'GET', '{"startTime":"2025-01-01","endTime":"2025-01-31"}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'SUCCESS', NOW() - INTERVAL 10 HOUR);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS `idx_sys_log_composite` ON `sys_log` (`username`, `module`, `operation_type`, `status`, `create_time`);
CREATE INDEX IF NOT EXISTS `idx_sys_log_time_range` ON `sys_log` (`create_time`, `status`);

-- 创建视图用于统计查询
CREATE OR REPLACE VIEW `v_sys_log_statistics` AS
SELECT 
    DATE(create_time) as log_date,
    module,
    operation_type,
    status,
    COUNT(*) as count,
    COUNT(DISTINCT username) as user_count,
    AVG(response_time) as avg_response_time
FROM sys_log 
WHERE create_time >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY DATE(create_time), module, operation_type, status
ORDER BY log_date DESC, count DESC;
