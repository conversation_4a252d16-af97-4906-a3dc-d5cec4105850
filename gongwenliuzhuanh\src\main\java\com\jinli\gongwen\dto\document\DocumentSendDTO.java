package com.jinli.gongwen.dto.document;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 公文发送数据传输对象
 */
@Data
@ApiModel("公文发送数据传输对象")
public class DocumentSendDTO {

    @ApiModelProperty("公文ID")
    private String docId;

    @ApiModelProperty(value = "接收部门ID列表", required = true)
    @NotEmpty(message = "接收部门不能为空")
    private List<String> receiveDeptIds;

    @ApiModelProperty("接收用户ID列表")
    private List<String> receiveUserIds;

    @ApiModelProperty("发送类型")
    private String sendType; // DEPT, USER, ALL

    @ApiModelProperty("发送说明")
    @Size(max = 500, message = "发送说明长度不能超过500个字符")
    private String sendRemark;

    @ApiModelProperty("是否需要签收")
    private Boolean requireReceipt = true;

    @ApiModelProperty("截止时间")
    private String deadline;

    @ApiModelProperty("优先级")
    private String priority; // LOW, NORMAL, HIGH, URGENT
}
