package com.jinli.gongwen.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jinli.gongwen.entity.DocDocument;

import java.util.Date;
import java.util.List;

/**
 * 公文信息表 服务类
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
public interface DocDocumentService extends IService<DocDocument> {

    /**
     * 分页查询公文列表
     * 
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param docTitle 公文标题（模糊查询）
     * @param docType 公文类型
     * @param docStatus 公文状态
     * @param createUserId 创建人ID
     * @param createDeptId 创建部门ID
     * @param currentHandler 当前处理人ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 公文分页列表
     */
    IPage<DocDocument> getDocumentPage(Integer pageNum, Integer pageSize, String docTitle, 
                                       String docType, String docStatus, String createUserId, 
                                       String createDeptId, String currentHandler, 
                                       Date startTime, Date endTime);

    /**
     * 根据公文ID查询公文详细信息
     * 
     * @param docId 公文ID
     * @return 公文详细信息
     */
    DocDocument getDocumentDetailById(String docId);

    /**
     * 创建公文
     * 
     * @param document 公文信息
     * @return 是否成功
     */
    boolean createDocument(DocDocument document);

    /**
     * 更新公文
     * 
     * @param document 公文信息
     * @return 是否成功
     */
    boolean updateDocument(DocDocument document);

    /**
     * 删除公文
     * 
     * @param docId 公文ID
     * @return 是否成功
     */
    boolean deleteDocument(String docId);

    /**
     * 批量删除公文
     * 
     * @param docIds 公文ID列表
     * @return 是否成功
     */
    boolean deleteDocuments(List<String> docIds);

    /**
     * 提交公文（进入流转流程）
     * 
     * @param docId 公文ID
     * @param userId 提交人ID
     * @return 是否成功
     */
    boolean submitDocument(String docId, String userId);

    /**
     * 审核公文
     * 
     * @param docId 公文ID
     * @param userId 审核人ID
     * @param action 操作类型（APPROVE/REJECT）
     * @param opinion 审核意见
     * @return 是否成功
     */
    boolean reviewDocument(String docId, String userId, String action, String opinion);

    /**
     * 发送公文
     * 
     * @param docId 公文ID
     * @param userId 发送人ID
     * @param targetDeptIds 目标部门ID列表
     * @return 是否成功
     */
    boolean sendDocument(String docId, String userId, List<String> targetDeptIds);

    /**
     * 签收公文
     * 
     * @param docId 公文ID
     * @param userId 签收人ID
     * @return 是否成功
     */
    boolean receiveDocument(String docId, String userId);

    /**
     * 查询用户待处理的公文列表
     * 
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param userId 用户ID
     * @return 待处理公文列表
     */
    IPage<DocDocument> getPendingDocuments(Integer pageNum, Integer pageSize, String userId);

    /**
     * 查询用户已处理的公文列表
     * 
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param userId 用户ID
     * @return 已处理公文列表
     */
    IPage<DocDocument> getProcessedDocuments(Integer pageNum, Integer pageSize, String userId);

    /**
     * 查询部门相关的公文列表
     * 
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param departmentId 部门ID
     * @param docStatus 公文状态
     * @return 部门公文列表
     */
    IPage<DocDocument> getDepartmentDocuments(Integer pageNum, Integer pageSize, 
                                              String departmentId, String docStatus);

    /**
     * 查询已发送的公文列表（供领导查看）
     * 
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 已发送公文列表
     */
    IPage<DocDocument> getSentDocuments(Integer pageNum, Integer pageSize, 
                                        Date startTime, Date endTime);

    /**
     * 检查公文编号是否唯一
     * 
     * @param docNumber 公文编号
     * @param docId 公文ID（排除自己）
     * @return 是否唯一
     */
    boolean checkDocNumberUnique(String docNumber, String docId);

    /**
     * 统计用户待处理公文数量
     * 
     * @param userId 用户ID
     * @return 待处理数量
     */
    int countPendingDocuments(String userId);

    /**
     * 统计部门公文数量
     * 
     * @param departmentId 部门ID
     * @param docStatus 公文状态
     * @return 公文数量
     */
    int countDepartmentDocuments(String departmentId, String docStatus);

    /**
     * 查询即将到期的公文列表
     * 
     * @param days 天数
     * @return 即将到期的公文列表
     */
    List<DocDocument> getExpiringDocuments(int days);

    /**
     * 根据条件搜索公文
     * 
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param keyword 关键词（标题、内容）
     * @param docType 公文类型
     * @param createDeptId 创建部门ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 搜索结果
     */
    IPage<DocDocument> searchDocuments(Integer pageNum, Integer pageSize, String keyword, 
                                       String docType, String createDeptId, 
                                       Date startTime, Date endTime);

    /**
     * 获取公文统计信息
     * 
     * @param userId 用户ID（可选，为空则统计全部）
     * @param departmentId 部门ID（可选，为空则统计全部）
     * @return 统计信息
     */
    Object getDocumentStatistics(String userId, String departmentId);

    /**
     * 生成公文编号
     * 
     * @param docType 公文类型
     * @return 公文编号
     */
    String generateDocNumber(String docType);
}
