# 河北金力集团公文流转系统配置文件
spring.application.name=gongwen-liuzhuan

# 服务器配置
server.port=8080
server.servlet.context-path=/api

# 达梦数据库配置
spring.datasource.driver-class-name=dm.jdbc.driver.DmDriver
spring.datasource.url=jdbc:dm://localhost:5236?schema=gongwen
spring.datasource.username=SYSDBA
spring.datasource.password=051001Hzy

# 连接池配置
spring.datasource.hikari.minimum-idle=1
spring.datasource.hikari.maximum-pool-size=5
spring.datasource.hikari.connection-timeout=60000
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.max-lifetime=900000
spring.datasource.hikari.validation-timeout=5000
spring.datasource.hikari.leak-detection-threshold=60000
spring.datasource.hikari.test-while-idle=true
spring.datasource.hikari.test-on-borrow=true
spring.datasource.hikari.validation-query=SELECT 1

# MyBatis Plus配置
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis-plus.mapper-locations=classpath:mapper/*.xml
mybatis-plus.global-config.db-config.id-type=assign_uuid
mybatis-plus.global-config.db-config.logic-delete-field=deleted
mybatis-plus.global-config.db-config.logic-delete-value=1
mybatis-plus.global-config.db-config.logic-not-delete-value=0

# JWT配置 - 使用512位安全密钥 (HS512算法)
jwt.secret=EXmPDS+MxkrFNocawAisrW7wx2XFxtNULsWt6F8vt9cMlyONblzlmlk4IU0sd4PYsjoR6EBin5lZ2IYJ1WE+vw==
jwt.expiration=86400000

# 文件上传配置
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=50MB
file.upload-path=/data/uploads/
file.access-path=/uploads/**

# Jackson配置
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8
spring.jackson.serialization.fail-on-empty-beans=false

# 日志配置
logging.level.com.jinli.gongwen=debug
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# Swagger配置
springfox.documentation.swagger-ui.enabled=true

# 修复Swagger与Spring Boot 2.7兼容性问题
spring.mvc.pathmatch.matching-strategy=ant_path_matcher
spring.main.allow-circular-references=true
