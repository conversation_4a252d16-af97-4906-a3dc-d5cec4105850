package com.jinli.gongwen.controller;

import com.jinli.gongwen.common.Result;
import com.jinli.gongwen.service.IUserWorkspaceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 用户工作台控制器
 * 为分厂厂长和部门主任提供统一的工作台功能
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
@Api(tags = "用户工作台管理")
@RestController
@RequestMapping("/user/workspace")
@CrossOrigin(origins = "*", allowCredentials = "false")
public class UserWorkspaceController {

    @Autowired
    private IUserWorkspaceService userWorkspaceService;

    /**
     * 获取用户工作台统计数据
     */
    @ApiOperation("获取用户工作台统计数据")
    @GetMapping("/statistics")
    @PreAuthorize("hasAnyRole('FACTORY_MANAGER', 'SALES_DIRECTOR', 'FINANCE_DIRECTOR')")
    public Result<Map<String, Object>> getUserStatistics(HttpServletRequest request) {
        String userId = getCurrentUserId(request);
        Map<String, Object> statistics = userWorkspaceService.getUserStatistics(userId);
        return Result.success(statistics);
    }

    /**
     * 获取用户最近公文
     */
    @ApiOperation("获取用户最近公文")
    @GetMapping("/recent-documents")
    @PreAuthorize("hasAnyRole('FACTORY_MANAGER', 'SALES_DIRECTOR', 'FINANCE_DIRECTOR')")
    public Result<Map<String, Object>> getRecentDocuments(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "5") Integer pageSize,
            HttpServletRequest request) {
        String userId = getCurrentUserId(request);
        Map<String, Object> result = userWorkspaceService.getRecentDocuments(userId, pageNum, pageSize);
        return Result.success(result);
    }

    /**
     * 获取用户待处理公文列表
     */
    @ApiOperation("获取用户待处理公文列表")
    @GetMapping("/pending-documents")
    @PreAuthorize("hasAnyRole('FACTORY_MANAGER', 'SALES_DIRECTOR', 'FINANCE_DIRECTOR')")
    public Result<Map<String, Object>> getPendingDocuments(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String docTitle,
            @RequestParam(required = false) String docType,
            @RequestParam(required = false) String docLevel,
            HttpServletRequest request) {
        String userId = getCurrentUserId(request);
        Map<String, Object> result = userWorkspaceService.getPendingDocuments(
                userId, pageNum, pageSize, docTitle, docType, docLevel);
        return Result.success(result);
    }

    /**
     * 获取用户已处理公文列表
     */
    @ApiOperation("获取用户已处理公文列表")
    @GetMapping("/processed-documents")
    @PreAuthorize("hasAnyRole('FACTORY_MANAGER', 'SALES_DIRECTOR', 'FINANCE_DIRECTOR')")
    public Result<Map<String, Object>> getProcessedDocuments(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String docTitle,
            @RequestParam(required = false) String docType,
            @RequestParam(required = false) String docStatus,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            HttpServletRequest request) {
        String userId = getCurrentUserId(request);
        Map<String, Object> result = userWorkspaceService.getProcessedDocuments(
                userId, pageNum, pageSize, docTitle, docType, docStatus, startDate, endDate);
        return Result.success(result);
    }

    /**
     * 获取用户草稿列表
     */
    @ApiOperation("获取用户草稿列表")
    @GetMapping("/draft-documents")
    @PreAuthorize("hasAnyRole('FACTORY_MANAGER', 'SALES_DIRECTOR', 'FINANCE_DIRECTOR')")
    public Result<Map<String, Object>> getDraftDocuments(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            HttpServletRequest request) {
        String userId = getCurrentUserId(request);
        Map<String, Object> result = userWorkspaceService.getDraftDocuments(userId, pageNum, pageSize);
        return Result.success(result);
    }

    /**
     * 获取公文详情
     */
    @ApiOperation("获取公文详情")
    @GetMapping("/document/{docId}")
    @PreAuthorize("hasAnyRole('FACTORY_MANAGER', 'SALES_DIRECTOR', 'FINANCE_DIRECTOR')")
    public Result<Map<String, Object>> getDocumentDetail(
            @PathVariable String docId,
            HttpServletRequest request) {
        String userId = getCurrentUserId(request);
        Map<String, Object> result = userWorkspaceService.getDocumentDetail(docId, userId);
        return Result.success(result);
    }

    /**
     * 获取公文流转记录
     */
    @ApiOperation("获取公文流转记录")
    @GetMapping("/document/{docId}/flow-history")
    @PreAuthorize("hasAnyRole('FACTORY_MANAGER', 'SALES_DIRECTOR', 'FINANCE_DIRECTOR')")
    public Result<Map<String, Object>> getDocumentFlowHistory(@PathVariable String docId) {
        Map<String, Object> result = userWorkspaceService.getDocumentFlowHistory(docId);
        return Result.success(result);
    }

    /**
     * 创建公文草稿
     */
    @ApiOperation("创建公文草稿")
    @PostMapping("/document/draft")
    @PreAuthorize("hasAnyRole('FACTORY_MANAGER', 'SALES_DIRECTOR', 'FINANCE_DIRECTOR')")
    public Result<String> createDocumentDraft(
            @RequestBody Map<String, Object> documentData,
            HttpServletRequest request) {
        String userId = getCurrentUserId(request);
        String docId = userWorkspaceService.createDocumentDraft(documentData, userId);
        return Result.success(docId, "草稿保存成功");
    }

    /**
     * 更新公文草稿
     */
    @ApiOperation("更新公文草稿")
    @PutMapping("/document/draft/{docId}")
    @PreAuthorize("hasAnyRole('FACTORY_MANAGER', 'SALES_DIRECTOR', 'FINANCE_DIRECTOR')")
    public Result<String> updateDocumentDraft(
            @PathVariable String docId,
            @RequestBody Map<String, Object> documentData,
            HttpServletRequest request) {
        String userId = getCurrentUserId(request);
        userWorkspaceService.updateDocumentDraft(docId, documentData, userId);
        return Result.success("草稿更新成功");
    }

    /**
     * 提交公文
     */
    @ApiOperation("提交公文")
    @PostMapping("/document/submit")
    @PreAuthorize("hasAnyRole('FACTORY_MANAGER', 'SALES_DIRECTOR', 'FINANCE_DIRECTOR')")
    public Result<String> submitDocument(
            @RequestBody Map<String, Object> documentData,
            HttpServletRequest request) {
        String userId = getCurrentUserId(request);
        String docId = userWorkspaceService.submitDocument(documentData, userId);
        return Result.success(docId, "公文提交成功");
    }

    /**
     * 提交草稿
     */
    @ApiOperation("提交草稿")
    @PostMapping("/document/draft/{docId}/submit")
    @PreAuthorize("hasAnyRole('FACTORY_MANAGER', 'SALES_DIRECTOR', 'FINANCE_DIRECTOR')")
    public Result<String> submitDraft(
            @PathVariable String docId,
            HttpServletRequest request) {
        String userId = getCurrentUserId(request);
        userWorkspaceService.submitDraft(docId, userId);
        return Result.success("草稿提交成功");
    }

    /**
     * 删除草稿
     */
    @ApiOperation("删除草稿")
    @DeleteMapping("/document/draft/{docId}")
    @PreAuthorize("hasAnyRole('FACTORY_MANAGER', 'SALES_DIRECTOR', 'FINANCE_DIRECTOR')")
    public Result<String> deleteDraft(
            @PathVariable String docId,
            HttpServletRequest request) {
        String userId = getCurrentUserId(request);
        userWorkspaceService.deleteDraft(docId, userId);
        return Result.success("草稿删除成功");
    }

    /**
     * 处理公文（审批/退回/转发）
     */
    @ApiOperation("处理公文")
    @PostMapping("/document/{docId}/process")
    @PreAuthorize("hasAnyRole('FACTORY_MANAGER', 'SALES_DIRECTOR', 'FINANCE_DIRECTOR')")
    public Result<String> processDocument(
            @PathVariable String docId,
            @RequestBody Map<String, Object> processData,
            HttpServletRequest request) {
        String userId = getCurrentUserId(request);
        userWorkspaceService.processDocument(docId, processData, userId);
        return Result.success("公文处理成功");
    }

    /**
     * 签收公文
     */
    @ApiOperation("签收公文")
    @PostMapping("/document/{docId}/receive")
    @PreAuthorize("hasAnyRole('FACTORY_MANAGER', 'SALES_DIRECTOR', 'FINANCE_DIRECTOR')")
    public Result<String> receiveDocument(
            @PathVariable String docId,
            HttpServletRequest request) {
        String userId = getCurrentUserId(request);
        userWorkspaceService.receiveDocument(docId, userId);
        return Result.success("公文签收成功");
    }

    /**
     * 获取当前用户ID
     */
    private String getCurrentUserId(HttpServletRequest request) {
        // 从请求中获取当前用户ID，这里需要根据实际的认证机制实现
        // 可能从JWT token、session或其他方式获取
        // 临时返回测试用户ID
        return "USER017"; // 分厂厂长测试用户
    }
}
