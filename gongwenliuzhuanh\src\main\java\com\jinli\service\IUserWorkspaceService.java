package com.jinli.service;

import java.util.Map;

/**
 * 用户工作台服务接口
 * 为分厂厂长和部门主任提供统一的工作台功能
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
public interface IUserWorkspaceService {

    /**
     * 获取用户工作台统计数据
     *
     * @param userId 用户ID
     * @return 统计数据
     */
    Map<String, Object> getUserStatistics(String userId);

    /**
     * 获取用户最近公文
     *
     * @param userId   用户ID
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return 最近公文列表
     */
    Map<String, Object> getRecentDocuments(String userId, Integer pageNum, Integer pageSize);

    /**
     * 获取用户待处理公文列表
     *
     * @param userId   用户ID
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @param docTitle 公文标题
     * @param docType  公文类型
     * @param docLevel 紧急程度
     * @return 待处理公文列表
     */
    Map<String, Object> getPendingDocuments(String userId, Integer pageNum, Integer pageSize,
                                          String docTitle, String docType, String docLevel);

    /**
     * 获取用户已处理公文列表
     *
     * @param userId    用户ID
     * @param pageNum   页码
     * @param pageSize  每页大小
     * @param docTitle  公文标题
     * @param docType   公文类型
     * @param docStatus 公文状态
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 已处理公文列表
     */
    Map<String, Object> getProcessedDocuments(String userId, Integer pageNum, Integer pageSize,
                                            String docTitle, String docType, String docStatus,
                                            String startDate, String endDate);

    /**
     * 获取用户草稿列表
     *
     * @param userId   用户ID
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return 草稿列表
     */
    Map<String, Object> getDraftDocuments(String userId, Integer pageNum, Integer pageSize);

    /**
     * 获取公文详情
     *
     * @param docId  公文ID
     * @param userId 用户ID
     * @return 公文详情
     */
    Map<String, Object> getDocumentDetail(String docId, String userId);

    /**
     * 获取公文流转记录
     *
     * @param docId 公文ID
     * @return 流转记录
     */
    Map<String, Object> getDocumentFlowHistory(String docId);

    /**
     * 创建公文草稿
     *
     * @param documentData 公文数据
     * @param userId       用户ID
     * @return 公文ID
     */
    String createDocumentDraft(Map<String, Object> documentData, String userId);

    /**
     * 更新公文草稿
     *
     * @param docId        公文ID
     * @param documentData 公文数据
     * @param userId       用户ID
     */
    void updateDocumentDraft(String docId, Map<String, Object> documentData, String userId);

    /**
     * 提交公文
     *
     * @param documentData 公文数据
     * @param userId       用户ID
     * @return 公文ID
     */
    String submitDocument(Map<String, Object> documentData, String userId);

    /**
     * 提交草稿
     *
     * @param docId  公文ID
     * @param userId 用户ID
     */
    void submitDraft(String docId, String userId);

    /**
     * 删除草稿
     *
     * @param docId  公文ID
     * @param userId 用户ID
     */
    void deleteDraft(String docId, String userId);

    /**
     * 处理公文（审批/退回/转发）
     *
     * @param docId       公文ID
     * @param processData 处理数据
     * @param userId      用户ID
     */
    void processDocument(String docId, Map<String, Object> processData, String userId);

    /**
     * 签收公文
     *
     * @param docId  公文ID
     * @param userId 用户ID
     */
    void receiveDocument(String docId, String userId);

    /**
     * 获取用户可转发的人员列表
     *
     * @param userId 用户ID
     * @return 人员列表
     */
    Map<String, Object> getTransferableUsers(String userId);

    /**
     * 获取公文类型列表
     *
     * @return 公文类型列表
     */
    Map<String, Object> getDocumentTypes();

    /**
     * 下载公文
     *
     * @param docId  公文ID
     * @param userId 用户ID
     * @return 文件信息
     */
    Map<String, Object> downloadDocument(String docId, String userId);

    /**
     * 下载附件
     *
     * @param attachmentId 附件ID
     * @param userId       用户ID
     * @return 文件信息
     */
    Map<String, Object> downloadAttachment(String attachmentId, String userId);

    /**
     * 导出公文列表
     *
     * @param userId     用户ID
     * @param filterData 筛选条件
     * @return 导出文件信息
     */
    Map<String, Object> exportDocuments(String userId, Map<String, Object> filterData);

    /**
     * 获取公文统计信息
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    Map<String, Object> getDocumentStatistics(String userId);
}
