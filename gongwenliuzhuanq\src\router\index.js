import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { routePermissionGuard, getDefaultHomePath } from '@/utils/permission'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置NProgress
NProgress.configure({ showSpinner: false })

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { title: '登录', requiresAuth: false }
  },
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/layout/index.vue'),
    redirect: '/dashboard',
    meta: { requiresAuth: true },
    children: [
      {
        path: '/dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: { title: '工作台', icon: 'House' }
      },
      {
        path: '/document',
        name: 'Document',
        meta: { title: '公文管理', icon: 'Document' },
        children: [
          {
            path: '/document/create',
            name: 'DocumentCreate',
            component: () => import('@/views/document/Create.vue'),
            meta: { title: '拟制公文', icon: 'EditPen' }
          },
          {
            path: '/document/pending',
            name: 'DocumentPending',
            component: () => import('@/views/document/Pending.vue'),
            meta: { title: '待处理公文', icon: 'Clock' }
          },
          {
            path: '/document/processed',
            name: 'DocumentProcessed',
            component: () => import('@/views/document/Processed.vue'),
            meta: { title: '已处理公文', icon: 'Check' }
          },
          {
            path: '/document/sent',
            name: 'DocumentSent',
            component: () => import('@/views/document/Sent.vue'),
            meta: { title: '已发送公文', icon: 'Promotion' }
          },
          {
            path: '/document/received',
            name: 'DocumentReceived',
            component: () => import('@/views/document/Received.vue'),
            meta: { title: '已签收公文', icon: 'Finished' }
          },
          {
            path: '/document/search',
            name: 'DocumentSearch',
            component: () => import('@/views/document/Search.vue'),
            meta: { title: '公文查询', icon: 'Search' }
          }
        ]
      },
      {
        path: '/system',
        name: 'System',
        meta: { title: '系统管理', icon: 'Setting', roles: ['ADMIN'] },
        children: [
          {
            path: '/system/user',
            name: 'SystemUser',
            component: () => import('@/views/system/User.vue'),
            meta: { title: '用户管理', icon: 'User', roles: ['ADMIN'] }
          },
          {
            path: '/system/role',
            name: 'SystemRole',
            component: () => import('@/views/system/Role.vue'),
            meta: { title: '角色管理', icon: 'UserFilled', roles: ['ADMIN'] }
          },
          {
            path: '/system/department',
            name: 'SystemDepartment',
            component: () => import('@/views/system/Department.vue'),
            meta: { title: '部门管理', icon: 'OfficeBuilding', roles: ['ADMIN'] }
          },
          {
            path: '/system/log',
            name: 'SystemLog',
            component: () => import('@/views/system/Log.vue'),
            meta: { title: '系统日志', icon: 'List', roles: ['ADMIN'] }
          },
          {
            path: '/system/config',
            name: 'SystemConfig',
            component: () => import('@/views/system/Config.vue'),
            meta: { title: '系统配置', icon: 'Tools', roles: ['ADMIN'] }
          },
          {
            path: '/system/monitor',
            name: 'SystemMonitor',
            component: () => import('@/views/system/Monitor.vue'),
            meta: { title: '系统监控', icon: 'Monitor', roles: ['ADMIN'] }
          }
        ]
      },
      {
        path: '/profile',
        name: 'Profile',
        component: () => import('@/views/Profile.vue'),
        meta: { title: '个人中心', icon: 'Avatar' }
      }
    ]
  },
  {
    path: '/document/detail/:id',
    name: 'DocumentDetail',
    component: () => import('@/views/document/Detail.vue'),
    meta: { title: '公文详情', requiresAuth: true }
  },
  {
    path: '/404',
    name: '404',
    component: () => import('@/views/404.vue'),
    meta: { title: '页面不存在' }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  NProgress.start()
  
  const userStore = useUserStore()
  const token = userStore.token
  
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - 河北金力集团公文流转系统` : '河北金力集团公文流转系统'
  
  if (to.path === '/login') {
    // 如果已登录，跳转到对应的默认首页
    if (token && userStore.userInfo) {
      const defaultPath = getDefaultHomePath(userStore.userInfo.roleKey)
      next(defaultPath)
    } else {
      next()
    }
  } else {
    // 需要登录的页面
    if (to.meta.requiresAuth !== false) {
      if (!token) {
        next('/login')
        return
      }

      // 检查用户信息
      if (!userStore.userInfo) {
        try {
          await userStore.getUserInfo()
        } catch (error) {
          userStore.logout()
          next('/login')
          return
        }
      }

      // 简化的权限检查
      const roleKey = userStore.userInfo?.roleKey

      // 系统管理员权限检查
      if (roleKey === 'ADMIN') {
        // 系统管理员不能访问业务功能
        if (to.path.startsWith('/dashboard') || to.path.startsWith('/document') || to.path.startsWith('/statistics')) {
          next('/system/user')
          return
        }
      }

      next()
    } else {
      next()
    }
  }
})

router.afterEach(() => {
  NProgress.done()
})

export default router
