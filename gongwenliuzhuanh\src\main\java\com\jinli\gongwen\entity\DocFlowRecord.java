package com.jinli.gongwen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 公文流转记录表
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
@ApiModel(description = "公文流转记录")
@TableName("DOC_FLOW_RECORD")
public class DocFlowRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("记录ID")
    @TableId(value = "RECORD_ID", type = IdType.ASSIGN_UUID)
    private String recordId;

    @ApiModelProperty("公文ID")
    @TableField("DOC_ID")
    private String docId;

    @ApiModelProperty("发送人ID")
    @TableField("FROM_USER_ID")
    private String fromUserId;

    @ApiModelProperty("接收人ID")
    @TableField("TO_USER_ID")
    private String toUserId;

    @ApiModelProperty("发送部门ID")
    @TableField("FROM_DEPT_ID")
    private String fromDeptId;

    @ApiModelProperty("接收部门ID")
    @TableField("TO_DEPT_ID")
    private String toDeptId;

    @ApiModelProperty("流程步骤")
    @TableField("FLOW_STEP")
    private String flowStep;

    @ApiModelProperty("操作类型")
    @TableField("ACTION")
    private String action;

    @ApiModelProperty("处理意见")
    @TableField("OPINION")
    private String opinion;

    @ApiModelProperty("处理时间")
    @TableField("PROCESS_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime processTime;

    @ApiModelProperty("备注")
    @TableField("REMARK")
    private String remark;

    // 非数据库字段
    @ApiModelProperty("发送人姓名")
    @TableField(exist = false)
    private String fromUserName;

    @ApiModelProperty("接收人姓名")
    @TableField(exist = false)
    private String toUserName;

    @ApiModelProperty("发送部门名称")
    @TableField(exist = false)
    private String fromDeptName;

    @ApiModelProperty("接收部门名称")
    @TableField(exist = false)
    private String toDeptName;

    // 构造方法
    public DocFlowRecord() {}

    // Getter和Setter方法
    public String getRecordId() {
        return recordId;
    }

    public void setRecordId(String recordId) {
        this.recordId = recordId;
    }

    public String getDocId() {
        return docId;
    }

    public void setDocId(String docId) {
        this.docId = docId;
    }

    public String getFromUserId() {
        return fromUserId;
    }

    public void setFromUserId(String fromUserId) {
        this.fromUserId = fromUserId;
    }

    public String getToUserId() {
        return toUserId;
    }

    public void setToUserId(String toUserId) {
        this.toUserId = toUserId;
    }

    public String getFromDeptId() {
        return fromDeptId;
    }

    public void setFromDeptId(String fromDeptId) {
        this.fromDeptId = fromDeptId;
    }

    public String getToDeptId() {
        return toDeptId;
    }

    public void setToDeptId(String toDeptId) {
        this.toDeptId = toDeptId;
    }

    public String getFlowStep() {
        return flowStep;
    }

    public void setFlowStep(String flowStep) {
        this.flowStep = flowStep;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getOpinion() {
        return opinion;
    }

    public void setOpinion(String opinion) {
        this.opinion = opinion;
    }

    public LocalDateTime getProcessTime() {
        return processTime;
    }

    public void setProcessTime(LocalDateTime processTime) {
        this.processTime = processTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getFromUserName() {
        return fromUserName;
    }

    public void setFromUserName(String fromUserName) {
        this.fromUserName = fromUserName;
    }

    public String getToUserName() {
        return toUserName;
    }

    public void setToUserName(String toUserName) {
        this.toUserName = toUserName;
    }

    public String getFromDeptName() {
        return fromDeptName;
    }

    public void setFromDeptName(String fromDeptName) {
        this.fromDeptName = fromDeptName;
    }

    public String getToDeptName() {
        return toDeptName;
    }

    public void setToDeptName(String toDeptName) {
        this.toDeptName = toDeptName;
    }

    @Override
    public String toString() {
        return "DocFlowRecord{" +
                "recordId='" + recordId + '\'' +
                ", docId='" + docId + '\'' +
                ", fromUserId='" + fromUserId + '\'' +
                ", toUserId='" + toUserId + '\'' +
                ", fromDeptId='" + fromDeptId + '\'' +
                ", toDeptId='" + toDeptId + '\'' +
                ", flowStep='" + flowStep + '\'' +
                ", action='" + action + '\'' +
                ", opinion='" + opinion + '\'' +
                ", processTime=" + processTime +
                ", remark='" + remark + '\'' +
                '}';
    }
}
