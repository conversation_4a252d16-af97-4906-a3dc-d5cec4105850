package com.jinli.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 用户查询DTO
 * 
 * <AUTHOR>
 * @since 2025-01-30
 */
@Data
public class UserQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户名（模糊查询）
     */
    private String username;

    /**
     * 真实姓名（模糊查询）
     */
    private String realName;

    /**
     * 手机号（模糊查询）
     */
    private String phone;

    /**
     * 邮箱（模糊查询）
     */
    private String email;

    /**
     * 部门ID
     */
    private String departmentId;

    /**
     * 用户状态（1正常 0停用）
     */
    private String status;

    /**
     * 角色ID
     */
    private String roleId;

    /**
     * 开始时间（创建时间范围查询）
     */
    private String startTime;

    /**
     * 结束时间（创建时间范围查询）
     */
    private String endTime;

    /**
     * 排序字段
     */
    private String orderBy = "CREATE_TIME DESC";

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 页大小
     */
    private Integer pageSize = 10;

    /**
     * 是否包含角色信息
     */
    private Boolean includeRole = true;

    /**
     * 是否包含部门信息
     */
    private Boolean includeDept = true;

    /**
     * 排除的用户ID（用于编辑时排除自己）
     */
    private String excludeUserId;

    /**
     * 检查是否有查询条件
     */
    public boolean hasQueryCondition() {
        return (username != null && !username.trim().isEmpty()) ||
               (realName != null && !realName.trim().isEmpty()) ||
               (phone != null && !phone.trim().isEmpty()) ||
               (email != null && !email.trim().isEmpty()) ||
               (departmentId != null && !departmentId.trim().isEmpty()) ||
               (status != null && !status.trim().isEmpty()) ||
               (roleId != null && !roleId.trim().isEmpty());
    }

    /**
     * 获取处理后的用户名（去除空格）
     */
    public String getTrimmedUsername() {
        return username != null ? username.trim() : null;
    }

    /**
     * 获取处理后的真实姓名（去除空格）
     */
    public String getTrimmedRealName() {
        return realName != null ? realName.trim() : null;
    }

    /**
     * 获取处理后的手机号（去除空格）
     */
    public String getTrimmedPhone() {
        return phone != null ? phone.trim() : null;
    }

    /**
     * 获取处理后的邮箱（去除空格并转小写）
     */
    public String getTrimmedEmail() {
        return email != null ? email.trim().toLowerCase() : null;
    }

    /**
     * 检查是否有时间范围查询
     */
    public boolean hasTimeRange() {
        return (startTime != null && !startTime.trim().isEmpty()) ||
               (endTime != null && !endTime.trim().isEmpty());
    }

    /**
     * 获取分页偏移量
     */
    public Integer getOffset() {
        return (pageNum - 1) * pageSize;
    }

    /**
     * 验证分页参数
     */
    public void validatePageParams() {
        if (pageNum == null || pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }
        if (pageSize > 100) {
            pageSize = 100; // 限制最大页大小
        }
    }
}
