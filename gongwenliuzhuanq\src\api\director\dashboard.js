import request from '@/utils/request'

// 获取厂长仪表板数据
export function getDirectorDashboard() {
  return request({
    url: '/api/director/dashboard',
    method: 'get'
  })
}

// 获取待审签公文列表
export function getPendingApprovalDocuments(params) {
  return request({
    url: '/api/director/pendingApproval',
    method: 'get',
    params
  })
}

// 获取已审签公文列表
export function getApprovedDocuments(params) {
  return request({
    url: '/api/director/approved',
    method: 'get',
    params
  })
}

// 获取审签统计信息
export function getApprovalStatistics() {
  return request({
    url: '/api/director/statistics',
    method: 'get'
  })
}

// 获取全公司公文流转概览
export function getCompanyOverview() {
  return request({
    url: '/api/director/companyOverview',
    method: 'get'
  })
}

// 获取决策支持数据
export function getDecisionSupportData() {
  return request({
    url: '/api/director/decisionSupport',
    method: 'get'
  })
}
