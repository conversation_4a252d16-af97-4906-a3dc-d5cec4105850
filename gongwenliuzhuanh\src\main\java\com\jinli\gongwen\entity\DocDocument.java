package com.jinli.gongwen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * 公文信息表
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
@ApiModel(description = "公文信息")
@TableName("DOC_DOCUMENT")
public class DocDocument implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("公文ID")
    @TableId(value = "DOC_ID", type = IdType.ASSIGN_UUID)
    private String docId;

    @ApiModelProperty("公文标题")
    @TableField("DOC_TITLE")
    private String docTitle;

    @ApiModelProperty("公文编号")
    @TableField("DOC_NUMBER")
    private String docNumber;

    @ApiModelProperty("公文类型")
    @TableField("DOC_TYPE")
    private String docType;

    @ApiModelProperty("紧急程度")
    @TableField("DOC_LEVEL")
    private String docLevel;

    @ApiModelProperty("公文内容")
    @TableField("DOC_CONTENT")
    private String docContent;

    @ApiModelProperty("公文状态")
    @TableField("DOC_STATUS")
    private String docStatus;

    @ApiModelProperty("创建人ID")
    @TableField("CREATE_USER_ID")
    private String createUserId;

    @ApiModelProperty("创建部门ID")
    @TableField("CREATE_DEPT_ID")
    private String createDeptId;

    @ApiModelProperty("创建时间")
    @TableField("CREATE_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty("更新时间")
    @TableField("UPDATE_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty("当前处理人ID")
    @TableField("CURRENT_HANDLER")
    private String currentHandler;

    @ApiModelProperty("当前流程步骤")
    @TableField("CURRENT_STEP")
    private String currentStep;

    @ApiModelProperty("办理期限")
    @TableField("DEADLINE")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deadline;

    @ApiModelProperty("备注")
    @TableField("REMARK")
    private String remark;

    // 非数据库字段
    @ApiModelProperty("创建人姓名")
    @TableField(exist = false)
    private String createUserName;

    @ApiModelProperty("创建部门名称")
    @TableField(exist = false)
    private String createDeptName;

    @ApiModelProperty("当前处理人姓名")
    @TableField(exist = false)
    private String currentHandlerName;

    @ApiModelProperty("公文类型名称")
    @TableField(exist = false)
    private String docTypeName;

    @ApiModelProperty("状态文本")
    @TableField(exist = false)
    private String statusText;

    // 构造方法
    public DocDocument() {}

    public DocDocument(String docId, String docTitle, String docType) {
        this.docId = docId;
        this.docTitle = docTitle;
        this.docType = docType;
    }

    // Getter和Setter方法
    public String getDocId() {
        return docId;
    }

    public void setDocId(String docId) {
        this.docId = docId;
    }

    public String getDocTitle() {
        return docTitle;
    }

    public void setDocTitle(String docTitle) {
        this.docTitle = docTitle;
    }

    public String getDocNumber() {
        return docNumber;
    }

    public void setDocNumber(String docNumber) {
        this.docNumber = docNumber;
    }

    public String getDocType() {
        return docType;
    }

    public void setDocType(String docType) {
        this.docType = docType;
    }

    public String getDocLevel() {
        return docLevel;
    }

    public void setDocLevel(String docLevel) {
        this.docLevel = docLevel;
    }

    public String getDocContent() {
        return docContent;
    }

    public void setDocContent(String docContent) {
        this.docContent = docContent;
    }

    public String getDocStatus() {
        return docStatus;
    }

    public void setDocStatus(String docStatus) {
        this.docStatus = docStatus;
    }

    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    public String getCreateDeptId() {
        return createDeptId;
    }

    public void setCreateDeptId(String createDeptId) {
        this.createDeptId = createDeptId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCurrentHandler() {
        return currentHandler;
    }

    public void setCurrentHandler(String currentHandler) {
        this.currentHandler = currentHandler;
    }

    public String getCurrentStep() {
        return currentStep;
    }

    public void setCurrentStep(String currentStep) {
        this.currentStep = currentStep;
    }

    public Date getDeadline() {
        return deadline;
    }

    public void setDeadline(Date deadline) {
        this.deadline = deadline;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getCreateDeptName() {
        return createDeptName;
    }

    public void setCreateDeptName(String createDeptName) {
        this.createDeptName = createDeptName;
    }

    public String getCurrentHandlerName() {
        return currentHandlerName;
    }

    public void setCurrentHandlerName(String currentHandlerName) {
        this.currentHandlerName = currentHandlerName;
    }

    public String getDocTypeName() {
        return docTypeName;
    }

    public void setDocTypeName(String docTypeName) {
        this.docTypeName = docTypeName;
    }

    public String getStatusText() {
        return statusText;
    }

    public void setStatusText(String statusText) {
        this.statusText = statusText;
    }

    @Override
    public String toString() {
        return "DocDocument{" +
                "docId='" + docId + '\'' +
                ", docTitle='" + docTitle + '\'' +
                ", docNumber='" + docNumber + '\'' +
                ", docType='" + docType + '\'' +
                ", docStatus='" + docStatus + '\'' +
                ", createUserId='" + createUserId + '\'' +
                ", createTime=" + createTime +
                '}';
    }
}
