package com.jinli.controller;

import com.jinli.common.Result;
import com.jinli.dto.DepartmentQueryDTO;
import com.jinli.entity.SysDepartment;
import com.jinli.service.ISysDepartmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 部门管理控制器
 * 
 * <AUTHOR>
 * @since 2025-01-30
 */
@Slf4j
@RestController
@RequestMapping("/system/department")
public class SysDepartmentController {

    @Autowired
    private ISysDepartmentService departmentService;

    /**
     * 查询部门列表
     */
    @GetMapping("/list")
    @PreAuthorize("hasAuthority('system:department:list')")
    public Result<List<SysDepartment>> list(DepartmentQueryDTO queryDTO) {
        log.info("查询部门列表，查询条件：{}", queryDTO);
        
        try {
            List<SysDepartment> departments = departmentService.selectDepartmentList(queryDTO);
            return Result.success(departments);
        } catch (Exception e) {
            log.error("查询部门列表失败", e);
            return Result.error("查询部门列表失败：" + e.getMessage());
        }
    }

    /**
     * 查询部门树形结构
     */
    @GetMapping("/tree")
    @PreAuthorize("hasAuthority('system:department:list')")
    public Result<List<SysDepartment>> tree(DepartmentQueryDTO queryDTO) {
        log.info("查询部门树形结构，查询条件：{}", queryDTO);
        
        try {
            List<SysDepartment> departmentTree = departmentService.selectDepartmentTree(queryDTO);
            return Result.success(departmentTree);
        } catch (Exception e) {
            log.error("查询部门树形结构失败", e);
            return Result.error("查询部门树形结构失败：" + e.getMessage());
        }
    }

    /**
     * 根据部门ID查询部门详情
     */
    @GetMapping("/{deptId}")
    @PreAuthorize("hasAuthority('system:department:query')")
    public Result<SysDepartment> getInfo(@PathVariable String deptId) {
        log.info("查询部门详情，部门ID：{}", deptId);
        
        try {
            if (!StringUtils.hasText(deptId)) {
                return Result.error("部门ID不能为空");
            }
            
            SysDepartment department = departmentService.getDepartmentFullInfo(deptId);
            if (department == null) {
                return Result.error("部门不存在");
            }
            
            return Result.success(department);
        } catch (Exception e) {
            log.error("查询部门详情失败", e);
            return Result.error("查询部门详情失败：" + e.getMessage());
        }
    }

    /**
     * 新增部门
     */
    @PostMapping
    @PreAuthorize("hasAuthority('system:department:add')")
    public Result<Void> add(@Validated @RequestBody SysDepartment department) {
        log.info("新增部门，部门信息：{}", department);
        
        try {
            boolean success = departmentService.insertDepartment(department);
            if (success) {
                return Result.success("新增部门成功");
            } else {
                return Result.error("新增部门失败");
            }
        } catch (Exception e) {
            log.error("新增部门失败", e);
            return Result.error("新增部门失败：" + e.getMessage());
        }
    }

    /**
     * 修改部门
     */
    @PutMapping
    @PreAuthorize("hasAuthority('system:department:edit')")
    public Result<Void> edit(@Validated @RequestBody SysDepartment department) {
        log.info("修改部门，部门信息：{}", department);
        
        try {
            if (!StringUtils.hasText(department.getDeptId())) {
                return Result.error("部门ID不能为空");
            }
            
            boolean success = departmentService.updateDepartment(department);
            if (success) {
                return Result.success("修改部门成功");
            } else {
                return Result.error("修改部门失败");
            }
        } catch (Exception e) {
            log.error("修改部门失败", e);
            return Result.error("修改部门失败：" + e.getMessage());
        }
    }

    /**
     * 删除部门
     */
    @DeleteMapping("/{deptId}")
    @PreAuthorize("hasAuthority('system:department:remove')")
    public Result<Void> remove(@PathVariable String deptId) {
        log.info("删除部门，部门ID：{}", deptId);
        
        try {
            if (!StringUtils.hasText(deptId)) {
                return Result.error("部门ID不能为空");
            }
            
            boolean success = departmentService.deleteDepartment(deptId);
            if (success) {
                return Result.success("删除部门成功");
            } else {
                return Result.error("删除部门失败");
            }
        } catch (Exception e) {
            log.error("删除部门失败", e);
            return Result.error("删除部门失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除部门
     */
    @DeleteMapping("/batch")
    @PreAuthorize("hasAuthority('system:department:remove')")
    public Result<Void> batchRemove(@RequestBody List<String> deptIds) {
        log.info("批量删除部门，部门ID列表：{}", deptIds);
        
        try {
            if (deptIds == null || deptIds.isEmpty()) {
                return Result.error("部门ID列表不能为空");
            }
            
            boolean success = departmentService.batchDeleteDepartments(deptIds);
            if (success) {
                return Result.success("批量删除部门成功");
            } else {
                return Result.error("批量删除部门失败");
            }
        } catch (Exception e) {
            log.error("批量删除部门失败", e);
            return Result.error("批量删除部门失败：" + e.getMessage());
        }
    }

    /**
     * 更新部门状态
     */
    @PutMapping("/{deptId}/status")
    @PreAuthorize("hasAuthority('system:department:edit')")
    public Result<Void> updateStatus(@PathVariable String deptId, @RequestBody String status) {
        log.info("更新部门状态，部门ID：{}，状态：{}", deptId, status);
        
        try {
            if (!StringUtils.hasText(deptId)) {
                return Result.error("部门ID不能为空");
            }
            
            if (!StringUtils.hasText(status)) {
                return Result.error("状态不能为空");
            }
            
            boolean success = departmentService.updateDepartmentStatus(deptId, status);
            if (success) {
                return Result.success("更新部门状态成功");
            } else {
                return Result.error("更新部门状态失败");
            }
        } catch (Exception e) {
            log.error("更新部门状态失败", e);
            return Result.error("更新部门状态失败：" + e.getMessage());
        }
    }

    /**
     * 检查部门编码是否存在
     */
    @GetMapping("/check-code")
    @PreAuthorize("hasAuthority('system:department:query')")
    public Result<Boolean> checkDeptCode(@RequestParam String deptCode, 
                                        @RequestParam(required = false) String deptId) {
        log.info("检查部门编码是否存在，部门编码：{}，排除部门ID：{}", deptCode, deptId);
        
        try {
            if (!StringUtils.hasText(deptCode)) {
                return Result.error("部门编码不能为空");
            }
            
            boolean exists = departmentService.checkDeptCodeExists(deptCode, deptId);
            return Result.success(exists);
        } catch (Exception e) {
            log.error("检查部门编码失败", e);
            return Result.error("检查部门编码失败：" + e.getMessage());
        }
    }

    /**
     * 查询部门下的用户数量
     */
    @GetMapping("/{deptId}/user-count")
    @PreAuthorize("hasAuthority('system:department:query')")
    public Result<Integer> getUserCount(@PathVariable String deptId) {
        log.info("查询部门下的用户数量，部门ID：{}", deptId);
        
        try {
            if (!StringUtils.hasText(deptId)) {
                return Result.error("部门ID不能为空");
            }
            
            Integer userCount = departmentService.selectUserCountByDeptId(deptId);
            return Result.success(userCount);
        } catch (Exception e) {
            log.error("查询部门用户数量失败", e);
            return Result.error("查询部门用户数量失败：" + e.getMessage());
        }
    }

    /**
     * 查询子部门列表
     */
    @GetMapping("/{parentId}/children")
    @PreAuthorize("hasAuthority('system:department:list')")
    public Result<List<SysDepartment>> getChildren(@PathVariable String parentId) {
        log.info("查询子部门列表，父部门ID：{}", parentId);
        
        try {
            if (!StringUtils.hasText(parentId)) {
                return Result.error("父部门ID不能为空");
            }
            
            List<SysDepartment> children = departmentService.selectChildrenByParentId(parentId);
            return Result.success(children);
        } catch (Exception e) {
            log.error("查询子部门列表失败", e);
            return Result.error("查询子部门列表失败：" + e.getMessage());
        }
    }

    /**
     * 查询部门树形结构（用于下拉选择）
     */
    @GetMapping("/tree-select")
    @PreAuthorize("hasAuthority('system:department:list')")
    public Result<List<SysDepartment>> treeSelect(@RequestParam(required = false) String excludeDeptId) {
        log.info("查询部门树形结构（用于下拉选择），排除部门ID：{}", excludeDeptId);
        
        try {
            List<SysDepartment> departmentTree = departmentService.selectDepartmentTreeForSelect(excludeDeptId);
            return Result.success(departmentTree);
        } catch (Exception e) {
            log.error("查询部门树形结构失败", e);
            return Result.error("查询部门树形结构失败：" + e.getMessage());
        }
    }

    /**
     * 查询根部门列表
     */
    @GetMapping("/roots")
    @PreAuthorize("hasAuthority('system:department:list')")
    public Result<List<SysDepartment>> getRoots() {
        log.info("查询根部门列表");
        
        try {
            List<SysDepartment> rootDepartments = departmentService.selectRootDepartments();
            return Result.success(rootDepartments);
        } catch (Exception e) {
            log.error("查询根部门列表失败", e);
            return Result.error("查询根部门列表失败：" + e.getMessage());
        }
    }

    /**
     * 查询部门统计信息
     */
    @GetMapping("/statistics")
    @PreAuthorize("hasAuthority('system:department:list')")
    public Result<List<SysDepartment>> getStatistics() {
        log.info("查询部门统计信息");
        
        try {
            List<SysDepartment> statistics = departmentService.selectDepartmentStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("查询部门统计信息失败", e);
            return Result.error("查询部门统计信息失败：" + e.getMessage());
        }
    }
}
