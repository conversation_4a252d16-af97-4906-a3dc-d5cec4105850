package com.jinli.controller;

import com.jinli.common.Result;
import com.jinli.dto.DepartmentQueryDTO;
import com.jinli.entity.SysDepartment;
import com.jinli.service.ISysDepartmentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 部门管理控制器
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
@RestController
@RequestMapping("/system/department")
public class SysDepartmentController {

    private static final Logger log = LoggerFactory.getLogger(SysDepartmentController.class);

    @Autowired
    private ISysDepartmentService departmentService;

    /**
     * 测试接口
     */
    @GetMapping("/test")
    public Result<String> test() {
        log.info("测试接口被调用");
        return Result.success("部门API测试成功！");
    }

    /**
     * 查询部门列表
     */
    @GetMapping("/list")
    // @PreAuthorize("hasAuthority('system:department:list')") // 临时注释权限验证
    public Result<List<SysDepartment>> list(DepartmentQueryDTO queryDTO) {
        log.info("查询部门列表，查询条件：{}", queryDTO);
        
        try {
            List<SysDepartment> departments = departmentService.selectDepartmentList(queryDTO);
            return Result.success(departments);
        } catch (Exception e) {
            log.error("查询部门列表失败", e);
            return Result.error("查询部门列表失败：" + e.getMessage());
        }
    }

    /**
     * 查询部门树形结构
     */
    @GetMapping("/tree")
    // @PreAuthorize("hasAuthority('system:department:list')") // 临时注释权限验证
    public Result<List<SysDepartment>> tree(DepartmentQueryDTO queryDTO) {
        log.info("查询部门树形结构，查询条件：{}", queryDTO);
        
        try {
            List<SysDepartment> departmentTree = departmentService.selectDepartmentTree(queryDTO);
            return Result.success(departmentTree);
        } catch (Exception e) {
            log.error("查询部门树形结构失败", e);
            return Result.error("查询部门树形结构失败：" + e.getMessage());
        }
    }

    /**
     * 根据部门ID查询部门详情
     */
    @GetMapping("/{deptId}")
    public Result<SysDepartment> getInfo(@PathVariable String deptId) {
        log.info("查询部门详情，部门ID：{}", deptId);
        
        try {
            if (!StringUtils.hasText(deptId)) {
                return Result.error("部门ID不能为空");
            }
            
            SysDepartment department = departmentService.getDepartmentFullInfo(deptId);
            if (department == null) {
                return Result.error("部门不存在");
            }
            
            return Result.success(department);
        } catch (Exception e) {
            log.error("查询部门详情失败", e);
            return Result.error("查询部门详情失败：" + e.getMessage());
        }
    }

    /**
     * 新增部门
     */
    @PostMapping
    public Result<Void> add(@RequestBody SysDepartment department) {
        log.info("新增部门，部门信息：{}", department);
        
        try {
            boolean success = departmentService.insertDepartment(department);
            if (success) {
                return Result.success("新增部门成功");
            } else {
                return Result.error("新增部门失败");
            }
        } catch (Exception e) {
            log.error("新增部门失败", e);
            return Result.error("新增部门失败：" + e.getMessage());
        }
    }

    /**
     * 修改部门
     */
    @PutMapping
    public Result<Void> edit(@RequestBody SysDepartment department) {
        log.info("修改部门，部门信息：{}", department);
        
        try {
            if (!StringUtils.hasText(department.getDeptId())) {
                return Result.error("部门ID不能为空");
            }
            
            boolean success = departmentService.updateDepartment(department);
            if (success) {
                return Result.success("修改部门成功");
            } else {
                return Result.error("修改部门失败");
            }
        } catch (Exception e) {
            log.error("修改部门失败", e);
            return Result.error("修改部门失败：" + e.getMessage());
        }
    }

    /**
     * 删除部门
     */
    @DeleteMapping("/{deptId}")
    public Result<Void> remove(@PathVariable String deptId) {
        log.info("删除部门，部门ID：{}", deptId);
        
        try {
            if (!StringUtils.hasText(deptId)) {
                return Result.error("部门ID不能为空");
            }
            
            boolean success = departmentService.deleteDepartment(deptId);
            if (success) {
                return Result.success("删除部门成功");
            } else {
                return Result.error("删除部门失败");
            }
        } catch (Exception e) {
            log.error("删除部门失败", e);
            return Result.error("删除部门失败：" + e.getMessage());
        }
    }

    /**
     * 查询部门统计信息
     */
    @GetMapping("/statistics")
    public Result<List<SysDepartment>> getStatistics() {
        log.info("查询部门统计信息");
        
        try {
            List<SysDepartment> statistics = departmentService.selectDepartmentStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("查询部门统计信息失败", e);
            return Result.error("查询部门统计信息失败：" + e.getMessage());
        }
    }
}
