<template>
  <div class="document-editor">
    <!-- 工具栏 -->
    <el-card class="toolbar-card">
      <div class="toolbar">
        <div class="toolbar-left">
          <el-button @click="goBack">
            <i class="el-icon-arrow-left"></i> 返回
          </el-button>
          <el-divider direction="vertical"></el-divider>
          <span class="document-title">{{ documentData.docTitle || '公文编辑' }}</span>
        </div>
        <div class="toolbar-right">
          <el-button @click="previewDocument">
            <i class="el-icon-view"></i> 预览
          </el-button>
          <el-button type="primary" @click="saveDocument">
            <i class="el-icon-document"></i> 保存
          </el-button>
          <el-button type="success" @click="submitDocument">
            <i class="el-icon-s-promotion"></i> 提交流转
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 编辑区域 -->
    <el-row :gutter="20" class="editor-row">
      <!-- 左侧编辑器 -->
      <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="16">
        <el-card class="editor-card">
          <div slot="header" class="card-header">
            <span>公文内容编辑</span>
            <div class="header-actions">
              <el-button size="small" @click="formatDocument">
                <i class="el-icon-magic-stick"></i> 格式化
              </el-button>
              <el-button size="small" @click="insertTemplate">
                <i class="el-icon-document-copy"></i> 插入模板
              </el-button>
            </div>
          </div>
          
          <!-- 公文头部信息 -->
          <div class="document-header">
            <el-form :model="documentData" label-width="100px" size="small">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="公文标题">
                    <el-input v-model="documentData.docTitle" placeholder="请输入公文标题" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="公文编号">
                    <el-input v-model="documentData.docNumber" placeholder="自动生成或手动输入" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="公文类型">
                    <el-select v-model="documentData.docType" placeholder="请选择">
                      <el-option label="通知" value="NOTICE" />
                      <el-option label="报告" value="REPORT" />
                      <el-option label="请示" value="REQUEST" />
                      <el-option label="决定" value="DECISION" />
                      <el-option label="意见" value="OPINION" />
                      <el-option label="函" value="LETTER" />
                      <el-option label="会议纪要" value="MINUTES" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="紧急程度">
                    <el-select v-model="documentData.docLevel" placeholder="请选择">
                      <el-option label="普通" value="普通" />
                      <el-option label="紧急" value="紧急" />
                      <el-option label="特急" value="特急" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="截止时间">
                    <el-date-picker
                      v-model="documentData.deadline"
                      type="datetime"
                      placeholder="选择截止时间"
                      format="yyyy-MM-dd HH:mm"
                      value-format="yyyy-MM-dd HH:mm:ss"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>

          <!-- 富文本编辑器 -->
          <div class="editor-container">
            <quill-editor
              ref="quillEditor"
              v-model="documentData.docContent"
              :options="editorOptions"
              @change="onEditorChange"
            />
          </div>

          <!-- 编辑意见 -->
          <div class="edit-opinion">
            <el-form :model="editForm" label-width="100px" size="small">
              <el-form-item label="编辑意见">
                <el-input
                  v-model="editForm.editOpinion"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入编辑意见和格式调整说明"
                />
              </el-form-item>
            </el-form>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧信息面板 -->
      <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
        <!-- 公文信息 -->
        <el-card class="info-card">
          <div slot="header">公文信息</div>
          <div class="info-content">
            <div class="info-item">
              <span class="info-label">创建人：</span>
              <span class="info-value">{{ documentData.createUserName }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">创建部门：</span>
              <span class="info-value">{{ documentData.createDeptName }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">创建时间：</span>
              <span class="info-value">{{ documentData.createTime }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">当前状态：</span>
              <el-tag :type="getStatusType(documentData.docStatus)">
                {{ getStatusLabel(documentData.docStatus) }}
              </el-tag>
            </div>
            <div class="info-item">
              <span class="info-label">当前处理人：</span>
              <span class="info-value">{{ documentData.currentHandlerName }}</span>
            </div>
          </div>
        </el-card>

        <!-- 流转记录 -->
        <el-card class="flow-card">
          <div slot="header">流转记录</div>
          <div class="flow-timeline">
            <el-timeline>
              <el-timeline-item
                v-for="record in flowRecords"
                :key="record.recordId"
                :timestamp="record.processTime"
                placement="top"
              >
                <div class="timeline-content">
                  <div class="timeline-title">{{ record.action }}</div>
                  <div class="timeline-user">{{ record.fromUserName }} → {{ record.toUserName }}</div>
                  <div class="timeline-opinion" v-if="record.opinion">{{ record.opinion }}</div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-card>

        <!-- 附件管理 -->
        <el-card class="attachment-card">
          <div slot="header">
            <span>附件管理</span>
            <el-button type="text" @click="uploadAttachment">
              <i class="el-icon-plus"></i> 添加
            </el-button>
          </div>
          <div class="attachment-list">
            <div
              v-for="attachment in attachments"
              :key="attachment.attachmentId"
              class="attachment-item"
            >
              <i class="el-icon-document"></i>
              <span class="attachment-name">{{ attachment.fileName }}</span>
              <div class="attachment-actions">
                <el-button type="text" size="mini" @click="downloadAttachment(attachment)">
                  下载
                </el-button>
                <el-button type="text" size="mini" @click="deleteAttachment(attachment)">
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 预览对话框 -->
    <DocumentPreview
      :visible.sync="previewVisible"
      :document-data="documentData"
    />

    <!-- 流转对话框 -->
    <FlowDialog
      :visible.sync="flowVisible"
      :document-id="documentData.docId"
      @success="handleFlowSuccess"
    />
  </div>
</template>

<script>
import { quillEditor } from 'vue-quill-editor'
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'

import { getDocumentDetail, editDocument, getFlowRecords } from '@/api/office/document'
import DocumentPreview from './components/DocumentPreview'
import FlowDialog from './components/FlowDialog'

export default {
  name: 'DocumentEditor',
  components: {
    quillEditor,
    DocumentPreview,
    FlowDialog
  },
  data() {
    return {
      documentData: {
        docId: '',
        docTitle: '',
        docNumber: '',
        docType: '',
        docLevel: '',
        docContent: '',
        deadline: '',
        createUserName: '',
        createDeptName: '',
        createTime: '',
        docStatus: '',
        currentHandlerName: ''
      },
      editForm: {
        editOpinion: ''
      },
      flowRecords: [],
      attachments: [],
      previewVisible: false,
      flowVisible: false,
      editorOptions: {
        theme: 'snow',
        placeholder: '请输入公文内容...',
        modules: {
          toolbar: [
            ['bold', 'italic', 'underline', 'strike'],
            ['blockquote', 'code-block'],
            [{ 'header': 1 }, { 'header': 2 }],
            [{ 'list': 'ordered'}, { 'list': 'bullet' }],
            [{ 'script': 'sub'}, { 'script': 'super' }],
            [{ 'indent': '-1'}, { 'indent': '+1' }],
            [{ 'direction': 'rtl' }],
            [{ 'size': ['small', false, 'large', 'huge'] }],
            [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
            [{ 'color': [] }, { 'background': [] }],
            [{ 'font': [] }],
            [{ 'align': [] }],
            ['clean'],
            ['link', 'image']
          ]
        }
      }
    }
  },
  created() {
    this.initData()
  },
  methods: {
    // 初始化数据
    async initData() {
      const docId = this.$route.params.docId
      if (docId) {
        await this.getDocumentDetail(docId)
        await this.getFlowRecords(docId)
      }
    },

    // 获取公文详情
    async getDocumentDetail(docId) {
      try {
        const response = await getDocumentDetail(docId)
        this.documentData = response.data
      } catch (error) {
        this.$message.error('获取公文详情失败')
      }
    },

    // 获取流转记录
    async getFlowRecords(docId) {
      try {
        const response = await getFlowRecords(docId)
        this.flowRecords = response.data
      } catch (error) {
        console.error('获取流转记录失败', error)
      }
    },

    // 编辑器内容变化
    onEditorChange() {
      // 自动保存草稿
      this.autoSave()
    },

    // 自动保存
    autoSave() {
      // 防抖处理，避免频繁保存
      clearTimeout(this.autoSaveTimer)
      this.autoSaveTimer = setTimeout(() => {
        this.saveDocument(true)
      }, 5000)
    },

    // 保存公文
    async saveDocument(isAutoSave = false) {
      try {
        const editData = {
          ...this.documentData,
          ...this.editForm
        }
        await editDocument(this.documentData.docId, editData)
        if (!isAutoSave) {
          this.$message.success('保存成功')
        }
      } catch (error) {
        if (!isAutoSave) {
          this.$message.error('保存失败')
        }
      }
    },

    // 提交流转
    submitDocument() {
      this.flowVisible = true
    },

    // 流转成功回调
    handleFlowSuccess() {
      this.flowVisible = false
      this.$message.success('提交成功')
      this.goBack()
    },

    // 预览公文
    previewDocument() {
      this.previewVisible = true
    },

    // 格式化公文
    formatDocument() {
      // 实现公文格式化逻辑
      this.$message.success('格式化完成')
    },

    // 插入模板
    insertTemplate() {
      // 实现模板插入逻辑
      this.$message.info('模板功能开发中')
    },

    // 上传附件
    uploadAttachment() {
      // 实现附件上传逻辑
      this.$message.info('附件上传功能开发中')
    },

    // 下载附件
    downloadAttachment(attachment) {
      // 实现附件下载逻辑
      this.$message.info('正在下载附件')
    },

    // 删除附件
    deleteAttachment(attachment) {
      // 实现附件删除逻辑
      this.$confirm('确认删除该附件？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功')
      })
    },

    // 获取状态类型
    getStatusType(status) {
      const typeMap = {
        'DRAFT': 'info',
        'OFFICE_EDIT': 'warning',
        'VICE_REVIEW': 'primary',
        'DIRECTOR_APPROVE': 'primary',
        'OFFICE_SEND': 'warning',
        'DEPT_RECEIVE': 'success',
        'COMPLETED': 'success',
        'REJECTED': 'danger'
      }
      return typeMap[status] || 'info'
    },

    // 获取状态标签
    getStatusLabel(status) {
      const statusMap = {
        'DRAFT': '草稿',
        'OFFICE_EDIT': '办公室修改',
        'VICE_REVIEW': '副厂长审核',
        'DIRECTOR_APPROVE': '厂长审签',
        'OFFICE_SEND': '办公室发送',
        'DEPT_RECEIVE': '部门签收',
        'COMPLETED': '已完成',
        'REJECTED': '已退回'
      }
      return statusMap[status] || status
    },

    // 返回
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped>
.document-editor {
  padding: 20px;
}

.toolbar-card {
  margin-bottom: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.document-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.editor-row {
  margin-bottom: 20px;
}

.editor-card {
  min-height: 600px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.document-header {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.editor-container {
  min-height: 400px;
  margin-bottom: 20px;
}

.edit-opinion {
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}

.info-card,
.flow-card,
.attachment-card {
  margin-bottom: 20px;
}

.info-content {
  padding: 10px 0;
}

.info-item {
  display: flex;
  margin-bottom: 10px;
  align-items: center;
}

.info-label {
  width: 80px;
  color: #909399;
  font-size: 14px;
}

.info-value {
  flex: 1;
  color: #303133;
  font-size: 14px;
}

.flow-timeline {
  max-height: 300px;
  overflow-y: auto;
}

.timeline-content {
  padding: 5px 0;
}

.timeline-title {
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.timeline-user {
  color: #909399;
  font-size: 12px;
  margin-bottom: 5px;
}

.timeline-opinion {
  color: #606266;
  font-size: 12px;
  background-color: #f5f7fa;
  padding: 5px;
  border-radius: 3px;
}

.attachment-list {
  max-height: 200px;
  overflow-y: auto;
}

.attachment-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.attachment-name {
  flex: 1;
  margin-left: 8px;
  font-size: 14px;
  color: #303133;
}

.attachment-actions {
  display: flex;
  gap: 5px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .document-editor {
    padding: 10px;
  }
  
  .toolbar {
    flex-direction: column;
    gap: 10px;
  }
  
  .toolbar-left,
  .toolbar-right {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 800px) {
  .editor-container {
    min-height: 300px;
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .info-label {
    width: auto;
    margin-bottom: 5px;
  }
}
</style>
