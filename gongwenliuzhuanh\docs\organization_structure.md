# 金利科技有限公司组织架构与权限配置

## 📊 **组织架构总览表**

| 序号 | 职位名称 | 角色标识 | 姓名 | 部门 | 用户名 | 权限级别 |
|------|----------|----------|------|------|--------|----------|
| 1 | 厂长 | FACTORY_DIRECTOR | 张厂长 | 厂长办公室 | factory_director | 最高级 |
| 2 | 生产副厂长 | VICE_DIRECTOR_PROD | 李副厂长 | 生产管理部 | vice_director_prod | 高级 |
| 3 | 技术副厂长 | VICE_DIRECTOR_TECH | 王副厂长 | 技术管理部 | vice_director_tech | 高级 |
| 4 | 安全副厂长 | VICE_DIRECTOR_SAFETY | 赵副厂长 | 安全管理部 | vice_director_safety | 高级 |
| 5 | 销售主任 | SALES_DIRECTOR | 刘主任 | 销售部 | sales_director | 中级 |
| 6 | 财务主任 | FINANCE_DIRECTOR | 陈主任 | 财务部 | finance_director | 中级 |
| 7 | 办公室主任 | OFFICE_DIRECTOR | 孙主任 | 办公室 | office_director | 中级 |
| 8 | 一分厂厂长 | FACTORY_MANAGER | 周厂长 | 一分厂 | factory1_manager | 基础级 |
| 9 | 二分厂厂长 | FACTORY_MANAGER | 吴厂长 | 二分厂 | factory2_manager | 基础级 |
| 10 | 三分厂厂长 | FACTORY_MANAGER | 郑厂长 | 三分厂 | factory3_manager | 基础级 |
| 11 | 系统管理员 | ADMIN | 系统管理员 | 信息中心 | admin | 系统级 |

## 🏢 **部门层级结构**

```
金利科技有限公司 (DEPT001)
├── 厂长办公室 (DEPT010)
├── 生产管理部 (DEPT011)
│   ├── 一分厂 (DEPT017)
│   ├── 二分厂 (DEPT018)
│   └── 三分厂 (DEPT019)
├── 技术管理部 (DEPT012)
├── 安全管理部 (DEPT013)
├── 销售部 (DEPT014)
├── 财务部 (DEPT015)
└── 办公室 (DEPT016)
```

## 🔐 **权限矩阵表**

| 功能模块 | 厂长 | 副厂长 | 主任 | 分厂厂长 | 系统管理员 |
|----------|------|--------|------|----------|------------|
| **公文管理** |  |  |  |  |  |
| 拟制公文 | ✅ | ✅ | ✅ | ✅ | ❌ |
| 公文审批 | ✅ | ✅ | ❌ | ❌ | ❌ |
| 公文查看 | 全部 | 相关 | 部门 | 分厂 | ❌ |
| 公文发送 | ✅ | ✅ | ✅ | ✅ | ❌ |
| **统计报表** |  |  |  |  |  |
| 全公司报表 | ✅ | ❌ | ❌ | ❌ | ❌ |
| 部门报表 | ✅ | ✅ | ✅ | ❌ | ❌ |
| 分厂报表 | ✅ | ✅ | ❌ | ✅ | ❌ |
| **管理功能** |  |  |  |  |  |
| 部门管理 | ✅ | ❌ | ❌ | ❌ | ✅ |
| 用户管理 | ❌ | ❌ | ❌ | ❌ | ✅ |
| 角色管理 | ❌ | ❌ | ❌ | ❌ | ✅ |
| 系统日志 | ❌ | ❌ | ❌ | ❌ | ✅ |

## 📋 **公文流转权限详细说明**

### **1. 厂长权限**
- **审批权限**：所有公文的最终审批权
- **查看权限**：可查看全公司所有公文
- **管理权限**：可监督所有部门的公文处理情况
- **特殊权限**：紧急情况下可跨越流程直接处理

### **2. 副厂长权限**
- **审批权限**：专业领域相关公文的审批权
- **查看权限**：可查看相关部门和下级部门公文
- **协调权限**：可协调相关部门的公文流转
- **上报权限**：向厂长上报重要事项

### **3. 主任权限**
- **处理权限**：本部门公文的处理和初审权
- **查看权限**：可查看本部门相关公文
- **申请权限**：可向上级申请资源和支持
- **执行权限**：执行上级下达的指令

### **4. 分厂厂长权限**
- **管理权限**：分厂内部公文的管理权
- **报告权限**：定期向上级报告生产情况
- **申请权限**：可申请生产资源和技术支持
- **执行权限**：执行生产计划和指令

### **5. 系统管理员权限**
- **系统权限**：用户、角色、权限管理
- **维护权限**：系统维护和技术支持
- **监控权限**：系统日志和运行状态监控
- **配置权限**：系统参数和流程配置

## 🔄 **公文流转路径设计**

### **标准流转路径**
```
部门拟制 → 办公室审核 → 相关副厂长审批 → 厂长审签 → 办公室发送 → 相关部门签收
```

### **不同类型公文的流转路径**

#### **生产类公文**
```
分厂拟制 → 办公室审核 → 生产副厂长审批 → 厂长审签 → 办公室发送 → 相关部门签收
```

#### **技术类公文**
```
技术部门拟制 → 办公室审核 → 技术副厂长审批 → 厂长审签 → 办公室发送 → 相关部门签收
```

#### **安全类公文**
```
安全部门拟制 → 办公室审核 → 安全副厂长审批 → 厂长审签 → 办公室发送 → 相关部门签收
```

#### **财务类公文**
```
财务部门拟制 → 办公室审核 → 财务主任初审 → 厂长审签 → 办公室发送 → 相关部门签收
```

#### **销售类公文**
```
销售部门拟制 → 办公室审核 → 销售主任初审 → 厂长审签 → 办公室发送 → 相关部门签收
```

## 📞 **联系方式配置**

| 部门 | 负责人 | 电话 | 邮箱 |
|------|--------|------|------|
| 厂长办公室 | 张厂长 | 010-12345679 | <EMAIL> |
| 生产管理部 | 李副厂长 | 010-12345680 | <EMAIL> |
| 技术管理部 | 王副厂长 | 010-12345681 | <EMAIL> |
| 安全管理部 | 赵副厂长 | 010-12345682 | <EMAIL> |
| 销售部 | 刘主任 | 010-12345683 | <EMAIL> |
| 财务部 | 陈主任 | 010-12345684 | <EMAIL> |
| 办公室 | 孙主任 | 010-12345685 | <EMAIL> |
| 一分厂 | 周厂长 | 010-12345686 | <EMAIL> |
| 二分厂 | 吴厂长 | 010-12345687 | <EMAIL> |
| 三分厂 | 郑厂长 | 010-12345688 | <EMAIL> |

## 🎯 **实施建议**

### **第一阶段：基础数据导入**
1. 执行SQL脚本，创建新的组织架构
2. 导入用户数据和角色配置
3. 测试权限分配和菜单显示

### **第二阶段：功能模块适配**
1. 更新前端菜单配置
2. 调整权限验证逻辑
3. 测试各角色功能访问

### **第三阶段：流程优化**
1. 配置公文流转路径
2. 设置审批权限规则
3. 测试完整流转流程

### **第四阶段：系统测试**
1. 各角色功能测试
2. 权限边界测试
3. 流程完整性测试
4. 性能和安全测试
