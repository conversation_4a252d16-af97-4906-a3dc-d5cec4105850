package com.jinli.gongwen.vo.document;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 公文视图对象
 */
@Data
@ApiModel("公文视图对象")
public class DocumentVO {

    @ApiModelProperty("公文ID")
    private String docId;

    @ApiModelProperty("公文标题")
    private String docTitle;

    @ApiModelProperty("公文编号")
    private String docNumber;

    @ApiModelProperty("公文类型")
    private String docType;

    @ApiModelProperty("公文级别")
    private String docLevel;

    @ApiModelProperty("公文内容")
    private String docContent;

    @ApiModelProperty("公文状态")
    private String docStatus;

    @ApiModelProperty("创建用户ID")
    private String createUserId;

    @ApiModelProperty("创建用户名")
    private String createUserName;

    @ApiModelProperty("创建部门ID")
    private String createDeptId;

    @ApiModelProperty("创建部门名")
    private String createDeptName;

    @ApiModelProperty("当前处理人")
    private String currentHandler;

    @ApiModelProperty("当前处理人名")
    private String currentHandlerName;

    @ApiModelProperty("当前步骤")
    private String currentStep;

    @ApiModelProperty("创建时间")
    private String createTime;

    @ApiModelProperty("更新时间")
    private String updateTime;

    @ApiModelProperty("截止时间")
    private String deadline;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("目标部门ID")
    private String targetDeptId;

    @ApiModelProperty("签收状态")
    private String receiveStatus;

    @ApiModelProperty("审核结果")
    private String reviewResult;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("角色名称列表")
    private String roleNames;

    @ApiModelProperty("部门名称")
    private String departmentName;
}
