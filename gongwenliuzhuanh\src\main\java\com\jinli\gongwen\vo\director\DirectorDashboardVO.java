package com.jinli.gongwen.vo.director;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 厂长仪表板视图对象
 */
@Data
@ApiModel("厂长仪表板视图对象")
public class DirectorDashboardVO {

    @ApiModelProperty("待审签公文数量")
    private Integer pendingApprovalCount;

    @ApiModelProperty("今日审签数量")
    private Integer todayApprovedCount;

    @ApiModelProperty("本月审签数量")
    private Integer monthApprovedCount;

    @ApiModelProperty("全公司公文总数")
    private Integer totalDocumentCount;

    @ApiModelProperty("审签通过率")
    private Double approvalRate;

    @ApiModelProperty("平均审签时间（小时）")
    private Double avgApprovalTime;

    @ApiModelProperty("紧急公文数量")
    private Integer urgentDocumentCount;

    @ApiModelProperty("战略级决策数量")
    private Integer strategicDecisionCount;

    @ApiModelProperty("各部门公文统计")
    private Map<String, Integer> deptDocumentStats;

    @ApiModelProperty("公文类型统计")
    private Map<String, Integer> docTypeStats;

    @ApiModelProperty("公文状态分布")
    private Map<String, Integer> docStatusStats;

    @ApiModelProperty("审签结果统计")
    private Map<String, Integer> approvalResultStats;

    @ApiModelProperty("最近审签的公文列表")
    private List<RecentApprovalVO> recentApprovals;

    @ApiModelProperty("待审签公文列表")
    private List<PendingApprovalVO> pendingApprovals;

    @ApiModelProperty("本周审签趋势")
    private List<DailyApprovalVO> weeklyApprovalTrend;

    @ApiModelProperty("部门效率排名")
    private List<DeptPerformanceVO> deptPerformanceRanking;

    @ApiModelProperty("决策支持数据")
    private DecisionSupportVO decisionSupport;

    /**
     * 最近审签视图对象
     */
    @Data
    @ApiModel("最近审签视图对象")
    public static class RecentApprovalVO {
        @ApiModelProperty("公文ID")
        private String docId;

        @ApiModelProperty("公文标题")
        private String docTitle;

        @ApiModelProperty("公文类型")
        private String docType;

        @ApiModelProperty("创建部门")
        private String createDeptName;

        @ApiModelProperty("审签结果")
        private Boolean isApproved;

        @ApiModelProperty("审签时间")
        private String approvalTime;

        @ApiModelProperty("决策级别")
        private String decisionLevel;

        @ApiModelProperty("审签意见")
        private String approvalOpinion;
    }

    /**
     * 待审签公文视图对象
     */
    @Data
    @ApiModel("待审签公文视图对象")
    public static class PendingApprovalVO {
        @ApiModelProperty("公文ID")
        private String docId;

        @ApiModelProperty("公文标题")
        private String docTitle;

        @ApiModelProperty("公文类型")
        private String docType;

        @ApiModelProperty("紧急程度")
        private String docLevel;

        @ApiModelProperty("创建部门")
        private String createDeptName;

        @ApiModelProperty("创建时间")
        private String createTime;

        @ApiModelProperty("等待时间（小时）")
        private Integer waitingHours;

        @ApiModelProperty("预估决策级别")
        private String estimatedDecisionLevel;
    }

    /**
     * 每日审签统计视图对象
     */
    @Data
    @ApiModel("每日审签统计视图对象")
    public static class DailyApprovalVO {
        @ApiModelProperty("日期")
        private String date;

        @ApiModelProperty("审签数量")
        private Integer approvalCount;

        @ApiModelProperty("通过数量")
        private Integer approvedCount;

        @ApiModelProperty("退回数量")
        private Integer rejectedCount;

        @ApiModelProperty("战略决策数量")
        private Integer strategicCount;
    }

    /**
     * 部门绩效视图对象
     */
    @Data
    @ApiModel("部门绩效视图对象")
    public static class DeptPerformanceVO {
        @ApiModelProperty("部门ID")
        private String deptId;

        @ApiModelProperty("部门名称")
        private String deptName;

        @ApiModelProperty("公文数量")
        private Integer documentCount;

        @ApiModelProperty("平均流转时间")
        private Double avgFlowTime;

        @ApiModelProperty("通过率")
        private Double passRate;

        @ApiModelProperty("效率评分")
        private Double efficiencyScore;

        @ApiModelProperty("排名")
        private Integer ranking;
    }

    /**
     * 决策支持视图对象
     */
    @Data
    @ApiModel("决策支持视图对象")
    public static class DecisionSupportVO {
        @ApiModelProperty("本月重要决策数量")
        private Integer importantDecisionCount;

        @ApiModelProperty("待跟踪执行数量")
        private Integer trackingCount;

        @ApiModelProperty("预算影响总额")
        private Double totalBudgetImpact;

        @ApiModelProperty("高风险决策数量")
        private Integer highRiskCount;

        @ApiModelProperty("部门协调需求数量")
        private Integer coordinationNeedCount;

        @ApiModelProperty("效率提升建议")
        private List<String> efficiencyRecommendations;
    }
}
