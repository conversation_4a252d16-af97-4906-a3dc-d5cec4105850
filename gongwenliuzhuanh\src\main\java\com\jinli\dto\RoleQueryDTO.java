package com.jinli.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 角色查询DTO
 * 
 * <AUTHOR>
 * @since 2025-01-30
 */
@Data
public class RoleQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 角色名称（模糊查询）
     */
    private String roleName;

    /**
     * 角色标识（模糊查询）
     */
    private String roleKey;

    /**
     * 角色状态（1正常 0停用）
     */
    private String status;

    /**
     * 开始时间（创建时间范围查询）
     */
    private String startTime;

    /**
     * 结束时间（创建时间范围查询）
     */
    private String endTime;

    /**
     * 排序字段
     */
    private String orderBy = "SORT_ORDER ASC, CREATE_TIME DESC";

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 页大小
     */
    private Integer pageSize = 10;

    /**
     * 是否包含权限信息
     */
    private Boolean includePermissions = false;

    /**
     * 是否包含用户数量
     */
    private Boolean includeUserCount = false;

    /**
     * 排除的角色ID（用于编辑时排除自己）
     */
    private String excludeRoleId;

    /**
     * 检查是否有查询条件
     */
    public boolean hasQueryCondition() {
        return (roleName != null && !roleName.trim().isEmpty()) ||
               (roleKey != null && !roleKey.trim().isEmpty()) ||
               (status != null && !status.trim().isEmpty());
    }

    /**
     * 获取处理后的角色名称（去除空格）
     */
    public String getTrimmedRoleName() {
        return roleName != null ? roleName.trim() : null;
    }

    /**
     * 获取处理后的角色标识（去除空格并转大写）
     */
    public String getTrimmedRoleKey() {
        return roleKey != null ? roleKey.trim().toUpperCase() : null;
    }

    /**
     * 检查是否有时间范围查询
     */
    public boolean hasTimeRange() {
        return (startTime != null && !startTime.trim().isEmpty()) ||
               (endTime != null && !endTime.trim().isEmpty());
    }

    /**
     * 获取分页偏移量
     */
    public Integer getOffset() {
        return (pageNum - 1) * pageSize;
    }

    /**
     * 验证分页参数
     */
    public void validatePageParams() {
        if (pageNum == null || pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }
        if (pageSize > 100) {
            pageSize = 100; // 限制最大页大小
        }
    }

    // 手动添加getter/setter方法（以防Lombok不工作）
    public String getRoleName() { return roleName; }
    public void setRoleName(String roleName) { this.roleName = roleName; }
    
    public String getRoleKey() { return roleKey; }
    public void setRoleKey(String roleKey) { this.roleKey = roleKey; }
    
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    
    public String getStartTime() { return startTime; }
    public void setStartTime(String startTime) { this.startTime = startTime; }
    
    public String getEndTime() { return endTime; }
    public void setEndTime(String endTime) { this.endTime = endTime; }
    
    public String getOrderBy() { return orderBy; }
    public void setOrderBy(String orderBy) { this.orderBy = orderBy; }
    
    public Integer getPageNum() { return pageNum; }
    public void setPageNum(Integer pageNum) { this.pageNum = pageNum; }
    
    public Integer getPageSize() { return pageSize; }
    public void setPageSize(Integer pageSize) { this.pageSize = pageSize; }
    
    public Boolean getIncludePermissions() { return includePermissions; }
    public void setIncludePermissions(Boolean includePermissions) { this.includePermissions = includePermissions; }
    
    public Boolean getIncludeUserCount() { return includeUserCount; }
    public void setIncludeUserCount(Boolean includeUserCount) { this.includeUserCount = includeUserCount; }
    
    public String getExcludeRoleId() { return excludeRoleId; }
    public void setExcludeRoleId(String excludeRoleId) { this.excludeRoleId = excludeRoleId; }
}
