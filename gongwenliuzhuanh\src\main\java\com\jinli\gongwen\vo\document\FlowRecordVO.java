package com.jinli.gongwen.vo.document;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 流转记录视图对象
 */
@Data
@ApiModel("流转记录视图对象")
public class FlowRecordVO {

    @ApiModelProperty("记录ID")
    private String recordId;

    @ApiModelProperty("公文ID")
    private String docId;

    @ApiModelProperty("操作")
    private String action;

    @ApiModelProperty("来源用户ID")
    private String fromUserId;

    @ApiModelProperty("来源用户名")
    private String fromUserName;

    @ApiModelProperty("目标用户ID")
    private String toUserId;

    @ApiModelProperty("目标用户名")
    private String toUserName;

    @ApiModelProperty("处理意见")
    private String opinion;

    @ApiModelProperty("处理时间")
    private String processTime;

    @ApiModelProperty("流转步骤")
    private String flowStep;
}
