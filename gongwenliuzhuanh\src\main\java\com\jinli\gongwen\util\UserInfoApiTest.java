package com.jinli.gongwen.util;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * 用户信息API测试工具
 */
public class UserInfoApiTest {
    
    public static void main(String[] args) {
        testUserInfoApi();
    }
    
    public static void testUserInfoApi() {
        System.out.println("=== 测试用户信息API ===");
        
        // 测试不同的参数情况
        String[] testCases = {
            "http://localhost:8080/api/userinfo",  // 无参数
            "http://localhost:8080/api/userinfo?userId=",  // 空参数
            "http://localhost:8080/api/userinfo?userId=undefined",  // undefined参数
            "http://localhost:8080/api/userinfo?userId=USER001"  // 正确参数
        };
        
        for (String testUrl : testCases) {
            testSingleCase(testUrl);
        }
    }
    
    private static void testSingleCase(String urlStr) {
        try {
            System.out.println("\n--- 测试: " + urlStr + " ---");
            
            URL url = new URL(urlStr);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(5000);
            conn.setReadTimeout(5000);
            
            int responseCode = conn.getResponseCode();
            System.out.println("响应码: " + responseCode);
            
            if (responseCode == 200) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream()));
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();
                System.out.println("✓ 成功");
                System.out.println("响应: " + response.toString());
            } else {
                BufferedReader errorReader = new BufferedReader(new InputStreamReader(conn.getErrorStream()));
                StringBuilder errorResponse = new StringBuilder();
                String line;
                while ((line = errorReader.readLine()) != null) {
                    errorResponse.append(line);
                }
                errorReader.close();
                System.out.println("✗ 失败");
                System.out.println("错误: " + errorResponse.toString());
            }
            
        } catch (Exception e) {
            System.out.println("✗ 异常: " + e.getMessage());
        }
    }
}
