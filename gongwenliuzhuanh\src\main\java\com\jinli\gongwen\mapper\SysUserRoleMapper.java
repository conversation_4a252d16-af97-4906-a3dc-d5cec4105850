package com.jinli.gongwen.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jinli.gongwen.entity.SysUserRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户角色关联表 Mapper 接口
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
@Mapper
public interface SysUserRoleMapper extends BaseMapper<SysUserRole> {

    /**
     * 根据用户ID查询角色ID列表
     * 
     * @param userId 用户ID
     * @return 角色ID列表
     */
    List<String> selectRoleIdsByUserId(@Param("userId") String userId);

    /**
     * 根据角色ID查询用户ID列表
     * 
     * @param roleId 角色ID
     * @return 用户ID列表
     */
    List<String> selectUserIdsByRoleId(@Param("roleId") String roleId);

    /**
     * 批量插入用户角色关联
     * 
     * @param userRoles 用户角色关联列表
     * @return 影响行数
     */
    int batchInsert(@Param("userRoles") List<SysUserRole> userRoles);

    /**
     * 根据用户ID删除角色关联
     * 
     * @param userId 用户ID
     * @return 影响行数
     */
    int deleteByUserId(@Param("userId") String userId);

    /**
     * 根据角色ID删除用户关联
     * 
     * @param roleId 角色ID
     * @return 影响行数
     */
    int deleteByRoleId(@Param("roleId") String roleId);

    /**
     * 检查用户是否拥有指定角色
     * 
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 数量
     */
    int checkUserRole(@Param("userId") String userId, @Param("roleId") String roleId);
}
