package com.jinli.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinli.dto.DepartmentQueryDTO;
import com.jinli.entity.SysDepartment;
import com.jinli.mapper.SysDepartmentMapper;
import com.jinli.service.ISysDepartmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 部门服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-30
 */
@Slf4j
@Service
public class SysDepartmentServiceImpl extends ServiceImpl<SysDepartmentMapper, SysDepartment> implements ISysDepartmentService {

    @Autowired
    private SysDepartmentMapper departmentMapper;

    @Override
    public List<SysDepartment> selectDepartmentList(DepartmentQueryDTO queryDTO) {
        log.info("查询部门列表，查询条件：{}", queryDTO);
        
        List<SysDepartment> departments = departmentMapper.selectDepartmentList(queryDTO);
        
        if (queryDTO.getReturnTree()) {
            return buildDepartmentTree(departments);
        }
        
        return departments;
    }

    @Override
    public List<SysDepartment> selectDepartmentTree(DepartmentQueryDTO queryDTO) {
        log.info("查询部门树形结构，查询条件：{}", queryDTO);
        
        queryDTO.setReturnTree(true);
        List<SysDepartment> departments = departmentMapper.selectDepartmentList(queryDTO);
        
        return buildDepartmentTree(departments);
    }

    @Override
    public SysDepartment selectDepartmentById(String deptId) {
        log.info("根据ID查询部门详情，部门ID：{}", deptId);
        
        if (!StringUtils.hasText(deptId)) {
            return null;
        }
        
        return departmentMapper.selectDepartmentById(deptId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertDepartment(SysDepartment department) {
        log.info("新增部门，部门信息：{}", department);
        
        // 验证部门数据
        String validateResult = validateDepartment(department);
        if (StringUtils.hasText(validateResult)) {
            throw new RuntimeException(validateResult);
        }
        
        // 设置创建时间
        department.setCreateTime(LocalDateTime.now());
        department.setUpdateTime(LocalDateTime.now());
        
        // 设置排序号
        if (department.getSort() == null) {
            Integer maxSort = departmentMapper.selectMaxSortByParentId(department.getParentId());
            department.setSort(maxSort + 1);
        }
        
        // 设置默认状态
        if (!StringUtils.hasText(department.getStatus())) {
            department.setStatus("1");
        }
        
        int result = departmentMapper.insert(department);
        
        log.info("新增部门结果：{}", result > 0 ? "成功" : "失败");
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDepartment(SysDepartment department) {
        log.info("更新部门，部门信息：{}", department);
        
        // 验证部门数据
        String validateResult = validateDepartment(department);
        if (StringUtils.hasText(validateResult)) {
            throw new RuntimeException(validateResult);
        }
        
        // 设置更新时间
        department.setUpdateTime(LocalDateTime.now());
        
        int result = departmentMapper.updateById(department);
        
        log.info("更新部门结果：{}", result > 0 ? "成功" : "失败");
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDepartment(String deptId) {
        log.info("删除部门，部门ID：{}", deptId);
        
        if (!StringUtils.hasText(deptId)) {
            throw new RuntimeException("部门ID不能为空");
        }
        
        // 检查是否可以删除
        String checkResult = checkCanDeleteDepartment(deptId);
        if (StringUtils.hasText(checkResult)) {
            throw new RuntimeException(checkResult);
        }
        
        // 将子部门的父ID更新为当前部门的父ID
        SysDepartment department = departmentMapper.selectDepartmentById(deptId);
        if (department != null) {
            departmentMapper.updateChildrenParentId(deptId, department.getParentId());
        }
        
        int result = departmentMapper.deleteById(deptId);
        
        log.info("删除部门结果：{}", result > 0 ? "成功" : "失败");
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteDepartments(List<String> deptIds) {
        log.info("批量删除部门，部门ID列表：{}", deptIds);
        
        if (deptIds == null || deptIds.isEmpty()) {
            throw new RuntimeException("部门ID列表不能为空");
        }
        
        // 检查每个部门是否可以删除
        for (String deptId : deptIds) {
            String checkResult = checkCanDeleteDepartment(deptId);
            if (StringUtils.hasText(checkResult)) {
                throw new RuntimeException("部门[" + deptId + "]" + checkResult);
            }
        }
        
        int result = departmentMapper.batchDeleteDepartments(deptIds);
        
        log.info("批量删除部门结果：{}", result > 0 ? "成功" : "失败");
        return result > 0;
    }

    @Override
    public boolean updateDepartmentStatus(String deptId, String status) {
        log.info("更新部门状态，部门ID：{}，状态：{}", deptId, status);
        
        if (!StringUtils.hasText(deptId)) {
            throw new RuntimeException("部门ID不能为空");
        }
        
        if (!StringUtils.hasText(status)) {
            throw new RuntimeException("状态不能为空");
        }
        
        int result = departmentMapper.updateDepartmentStatus(deptId, status);
        
        log.info("更新部门状态结果：{}", result > 0 ? "成功" : "失败");
        return result > 0;
    }

    @Override
    public boolean checkDeptCodeExists(String deptCode, String excludeDeptId) {
        if (!StringUtils.hasText(deptCode)) {
            return false;
        }
        
        Integer count = departmentMapper.checkDeptCodeExists(deptCode, excludeDeptId);
        return count != null && count > 0;
    }

    @Override
    public boolean checkDeptNameExists(String deptName, String parentId, String excludeDeptId) {
        if (!StringUtils.hasText(deptName)) {
            return false;
        }
        
        Integer count = departmentMapper.checkDeptNameExists(deptName, parentId, excludeDeptId);
        return count != null && count > 0;
    }

    @Override
    public List<SysDepartment> selectChildrenByParentId(String parentId) {
        log.info("查询子部门列表，父部门ID：{}", parentId);
        
        return departmentMapper.selectChildrenByParentId(parentId);
    }

    @Override
    public Integer selectUserCountByDeptId(String deptId) {
        if (!StringUtils.hasText(deptId)) {
            return 0;
        }
        
        Integer count = departmentMapper.selectUserCountByDeptId(deptId);
        return count != null ? count : 0;
    }

    @Override
    public List<SysDepartment> selectAncestorDepartments(String deptId) {
        log.info("查询祖先部门，部门ID：{}", deptId);
        
        if (!StringUtils.hasText(deptId)) {
            return new ArrayList<>();
        }
        
        return departmentMapper.selectAncestorDepartments(deptId);
    }

    @Override
    public List<SysDepartment> selectDescendantDepartments(String deptId) {
        log.info("查询后代部门，部门ID：{}", deptId);
        
        if (!StringUtils.hasText(deptId)) {
            return new ArrayList<>();
        }
        
        return departmentMapper.selectDescendantDepartments(deptId);
    }

    @Override
    public List<SysDepartment> selectDepartmentTreeForSelect(String excludeDeptId) {
        log.info("查询部门树形结构（用于下拉选择），排除部门ID：{}", excludeDeptId);
        
        List<SysDepartment> departments = departmentMapper.selectDepartmentTree(excludeDeptId);
        return buildDepartmentTree(departments);
    }

    @Override
    public List<SysDepartment> selectRootDepartments() {
        log.info("查询根部门列表");
        
        return departmentMapper.selectRootDepartments();
    }

    @Override
    public String selectDepartmentPath(String deptId) {
        if (!StringUtils.hasText(deptId)) {
            return "";
        }
        
        String path = departmentMapper.selectDepartmentPath(deptId);
        return path != null ? path : "";
    }

    @Override
    public Integer selectDepartmentLevel(String deptId) {
        if (!StringUtils.hasText(deptId)) {
            return 0;
        }
        
        Integer level = departmentMapper.selectDepartmentLevel(deptId);
        return level != null ? level : 0;
    }

    @Override
    public List<SysDepartment> selectEnabledDepartments() {
        log.info("查询启用的部门列表");
        
        return departmentMapper.selectEnabledDepartments();
    }

    @Override
    public List<SysDepartment> selectDepartmentStatistics() {
        log.info("查询部门统计信息");
        
        return departmentMapper.selectDepartmentStatistics();
    }

    @Override
    public List<SysDepartment> buildDepartmentTree(List<SysDepartment> departments) {
        return buildDepartmentTree(departments, null);
    }

    @Override
    public List<SysDepartment> buildDepartmentTree(List<SysDepartment> departments, String rootId) {
        if (departments == null || departments.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 创建ID到部门的映射
        Map<String, SysDepartment> deptMap = departments.stream()
                .collect(Collectors.toMap(SysDepartment::getDeptId, dept -> dept));
        
        List<SysDepartment> rootDepartments = new ArrayList<>();
        
        for (SysDepartment department : departments) {
            String parentId = department.getParentId();
            
            // 如果指定了根节点ID，只处理该根节点的子树
            if (StringUtils.hasText(rootId)) {
                if (rootId.equals(department.getDeptId())) {
                    rootDepartments.add(department);
                } else if (StringUtils.hasText(parentId) && deptMap.containsKey(parentId)) {
                    deptMap.get(parentId).addChild(department);
                }
            } else {
                // 没有指定根节点，处理所有树
                if (!StringUtils.hasText(parentId) || "0".equals(parentId) || !deptMap.containsKey(parentId)) {
                    rootDepartments.add(department);
                } else {
                    deptMap.get(parentId).addChild(department);
                }
            }
        }
        
        // 对每个层级的部门进行排序
        sortDepartmentTree(rootDepartments);
        
        return rootDepartments;
    }

    /**
     * 对部门树进行排序
     */
    private void sortDepartmentTree(List<SysDepartment> departments) {
        if (departments == null || departments.isEmpty()) {
            return;
        }
        
        // 按排序号和创建时间排序
        departments.sort((d1, d2) -> {
            int sortCompare = Integer.compare(
                    d1.getSort() != null ? d1.getSort() : 0,
                    d2.getSort() != null ? d2.getSort() : 0
            );
            if (sortCompare != 0) {
                return sortCompare;
            }
            
            if (d1.getCreateTime() != null && d2.getCreateTime() != null) {
                return d1.getCreateTime().compareTo(d2.getCreateTime());
            }
            
            return 0;
        });
        
        // 递归排序子部门
        for (SysDepartment department : departments) {
            if (department.getChildren() != null && !department.getChildren().isEmpty()) {
                sortDepartmentTree(department.getChildren());
            }
        }
    }

    @Override
    public String validateDepartment(SysDepartment department) {
        if (department == null) {
            return "部门信息不能为空";
        }
        
        if (!StringUtils.hasText(department.getDeptName())) {
            return "部门名称不能为空";
        }
        
        if (!StringUtils.hasText(department.getDeptCode())) {
            return "部门编码不能为空";
        }
        
        // 检查部门编码是否重复
        if (checkDeptCodeExists(department.getDeptCode(), department.getDeptId())) {
            return "部门编码已存在";
        }
        
        // 检查同一父部门下部门名称是否重复
        if (checkDeptNameExists(department.getDeptName(), department.getParentId(), department.getDeptId())) {
            return "同一父部门下部门名称已存在";
        }
        
        // 检查父部门是否存在
        if (StringUtils.hasText(department.getParentId()) && !"0".equals(department.getParentId())) {
            SysDepartment parentDept = departmentMapper.selectDepartmentById(department.getParentId());
            if (parentDept == null) {
                return "父部门不存在";
            }
            
            // 检查是否设置自己为父部门
            if (department.getDeptId() != null && department.getDeptId().equals(department.getParentId())) {
                return "不能设置自己为父部门";
            }
        }
        
        return null;
    }

    @Override
    public String checkCanDeleteDepartment(String deptId) {
        if (!StringUtils.hasText(deptId)) {
            return "部门ID不能为空";
        }
        
        // 检查是否存在子部门
        List<SysDepartment> children = departmentMapper.selectChildrenByParentId(deptId);
        if (children != null && !children.isEmpty()) {
            return "存在子部门，不能删除";
        }
        
        // 检查是否存在用户
        Integer userCount = departmentMapper.selectUserCountByDeptId(deptId);
        if (userCount != null && userCount > 0) {
            return "部门下存在用户，不能删除";
        }
        
        return null;
    }

    @Override
    public SysDepartment getDepartmentFullInfo(String deptId) {
        log.info("获取部门完整信息，部门ID：{}", deptId);
        
        if (!StringUtils.hasText(deptId)) {
            return null;
        }
        
        SysDepartment department = departmentMapper.selectDepartmentById(deptId);
        if (department != null) {
            // 设置用户数量
            Integer userCount = departmentMapper.selectUserCountByDeptId(deptId);
            department.setUserCount(userCount != null ? userCount : 0);
            
            // 设置部门路径
            String deptPath = departmentMapper.selectDepartmentPath(deptId);
            department.setDeptPath(deptPath);
            
            // 设置部门层级
            Integer level = departmentMapper.selectDepartmentLevel(deptId);
            department.setLevel(level != null ? level : 0);
            
            // 设置是否有子部门
            List<SysDepartment> children = departmentMapper.selectChildrenByParentId(deptId);
            department.setHasChildren(children != null && !children.isEmpty());
        }
        
        return department;
    }
}
