package com.jinli.gongwen.controller.office;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinli.gongwen.common.Result;
import com.jinli.gongwen.dto.document.DocumentEditDTO;
import com.jinli.gongwen.dto.document.DocumentFlowDTO;
import com.jinli.gongwen.dto.document.DocumentQueryDTO;
import com.jinli.gongwen.dto.document.DocumentSendDTO;
import com.jinli.gongwen.entity.document.DocDocument;
import com.jinli.gongwen.service.document.DocumentService;
import com.jinli.gongwen.service.document.FlowService;
import com.jinli.gongwen.vo.document.DocumentVO;
import com.jinli.gongwen.vo.document.FlowRecordVO;
import com.jinli.gongwen.vo.office.OfficeDashboardVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 办公室主任控制器
 * 负责公文流转的核心枢纽功能
 */
@Api(tags = "办公室主任功能")
@RestController
@RequestMapping("/api/office")
@PreAuthorize("hasRole('OFFICE_DIRECTOR')")
public class OfficeController {

    @Autowired
    private DocumentService documentService;

    @Autowired
    private FlowService flowService;

    /**
     * 办公室主任仪表板
     */
    @ApiOperation("办公室主任仪表板")
    @GetMapping("/dashboard")
    public Result<OfficeDashboardVO> getDashboard() {
        OfficeDashboardVO dashboard = documentService.getOfficeDashboard();
        return Result.success(dashboard);
    }

    /**
     * 获取待处理公文列表
     */
    @ApiOperation("获取待处理公文列表")
    @GetMapping("/pending")
    public Result<IPage<DocumentVO>> getPendingDocuments(DocumentQueryDTO queryDTO) {
        Page<DocDocument> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        QueryWrapper<DocDocument> queryWrapper = new QueryWrapper<>();
        
        // 办公室待处理的公文状态
        queryWrapper.in("current_step", "OFFICE_EDIT", "OFFICE_SEND");
        queryWrapper.eq("current_handler", getCurrentUserId());
        
        // 构建查询条件
        if (queryDTO.getDocTitle() != null && !queryDTO.getDocTitle().trim().isEmpty()) {
            queryWrapper.like("doc_title", queryDTO.getDocTitle().trim());
        }
        if (queryDTO.getDocType() != null && !queryDTO.getDocType().trim().isEmpty()) {
            queryWrapper.eq("doc_type", queryDTO.getDocType());
        }
        if (queryDTO.getDocLevel() != null && !queryDTO.getDocLevel().trim().isEmpty()) {
            queryWrapper.eq("doc_level", queryDTO.getDocLevel());
        }
        
        queryWrapper.orderByDesc("create_time");
        
        IPage<DocumentVO> result = documentService.getDocumentList(page, queryWrapper);
        return Result.success(result);
    }

    /**
     * 获取公文详情（用于编辑）
     */
    @ApiOperation("获取公文详情")
    @GetMapping("/document/{docId}")
    public Result<DocumentVO> getDocument(@PathVariable String docId) {
        DocumentVO documentVO = documentService.getDocumentById(docId);
        return Result.success(documentVO);
    }

    /**
     * 修改公文内容
     */
    @ApiOperation("修改公文内容")
    @PutMapping("/document/{docId}/edit")
    public Result<Void> editDocument(@PathVariable String docId, @Valid @RequestBody DocumentEditDTO editDTO) {
        editDTO.setDocId(docId);
        documentService.editDocument(editDTO);
        return Result.success();
    }

    /**
     * 公文流转控制
     */
    @ApiOperation("公文流转控制")
    @PostMapping("/document/{docId}/flow")
    public Result<Void> flowDocument(@PathVariable String docId, @Valid @RequestBody DocumentFlowDTO flowDTO) {
        flowDTO.setDocId(docId);
        flowService.processFlow(flowDTO);
        return Result.success();
    }

    /**
     * 删除公文
     */
    @ApiOperation("删除公文")
    @DeleteMapping("/document/{docId}")
    public Result<Void> deleteDocument(@PathVariable String docId) {
        documentService.deleteDocument(docId);
        return Result.success();
    }

    /**
     * 发送公文到相关部门
     */
    @ApiOperation("发送公文到相关部门")
    @PostMapping("/document/{docId}/send")
    public Result<Void> sendDocument(@PathVariable String docId, @Valid @RequestBody DocumentSendDTO sendDTO) {
        sendDTO.setDocId(docId);
        documentService.sendDocument(sendDTO);
        return Result.success();
    }

    /**
     * 批量发送公文
     */
    @ApiOperation("批量发送公文")
    @PostMapping("/document/batchSend")
    public Result<Void> batchSendDocuments(@RequestBody List<DocumentSendDTO> sendDTOList) {
        documentService.batchSendDocuments(sendDTOList);
        return Result.success();
    }

    /**
     * 获取公文流转记录
     */
    @ApiOperation("获取公文流转记录")
    @GetMapping("/document/{docId}/flowRecords")
    public Result<List<FlowRecordVO>> getFlowRecords(@PathVariable String docId) {
        List<FlowRecordVO> flowRecords = flowService.getFlowRecords(docId);
        return Result.success(flowRecords);
    }

    /**
     * 获取流转状态概览
     */
    @ApiOperation("获取流转状态概览")
    @GetMapping("/flowStatus")
    public Result<List<DocumentVO>> getFlowStatusOverview() {
        List<DocumentVO> flowStatus = flowService.getFlowStatusOverview();
        return Result.success(flowStatus);
    }

    /**
     * 退回公文
     */
    @ApiOperation("退回公文")
    @PostMapping("/document/{docId}/return")
    public Result<Void> returnDocument(@PathVariable String docId, @RequestParam String reason) {
        flowService.returnDocument(docId, reason);
        return Result.success();
    }

    /**
     * 获取可发送的部门列表
     */
    @ApiOperation("获取可发送的部门列表")
    @GetMapping("/sendTargets")
    public Result<List<Object>> getSendTargets() {
        List<Object> targets = documentService.getSendTargets();
        return Result.success(targets);
    }

    /**
     * 获取发送记录
     */
    @ApiOperation("获取发送记录")
    @GetMapping("/sendRecords")
    public Result<IPage<Object>> getSendRecords(DocumentQueryDTO queryDTO) {
        Page<Object> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        IPage<Object> result = documentService.getSendRecords(page);
        return Result.success(result);
    }

    /**
     * 获取当前用户ID（从安全上下文中获取）
     */
    private String getCurrentUserId() {
        // 从Spring Security上下文中获取当前用户ID
        // 这里简化处理，实际应该从SecurityContextHolder中获取
        return "USER006"; // 办公室主任的用户ID
    }
}
