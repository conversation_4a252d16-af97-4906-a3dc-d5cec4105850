<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>登录测试</title>
  <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      margin: 0;
      background-color: #f5f5f5;
    }
    .login-container {
      background-color: white;
      padding: 20px;
      border-radius: 5px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      width: 300px;
    }
    h2 {
      text-align: center;
      color: #333;
    }
    .form-group {
      margin-bottom: 15px;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    input {
      width: 100%;
      padding: 8px;
      box-sizing: border-box;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    button {
      width: 100%;
      padding: 10px;
      background-color: #4F46E5;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    button:hover {
      background-color: #4338CA;
    }
    #result {
      margin-top: 20px;
      padding: 10px;
      border-radius: 4px;
      display: none;
    }
    .success {
      background-color: #D1FAE5;
      color: #065F46;
    }
    .error {
      background-color: #FEE2E2;
      color: #B91C1C;
    }
  </style>
</head>
<body>
  <div class="login-container">
    <h2>登录测试</h2>
    <div class="form-group">
      <label for="username">用户名</label>
      <input type="text" id="username" value="admin">
    </div>
    <div class="form-group">
      <label for="password">密码</label>
      <input type="password" id="password" value="123456">
    </div>
    <button onclick="login()">登录</button>
    <div id="result"></div>
  </div>

  <script>
    async function login() {
      const username = document.getElementById('username').value;
      const password = document.getElementById('password').value;
      const resultDiv = document.getElementById('result');
      
      resultDiv.style.display = 'block';
      resultDiv.innerHTML = '正在登录...';
      resultDiv.className = '';
      
      try {
        // 直接请求
        const response = await axios.post('/api/login', {
          username,
          password
        });
        
        console.log('登录响应:', response.data);
        
        if (response.data.code === 200) {
          resultDiv.innerHTML = '登录成功！Token: ' + response.data.data.token;
          resultDiv.className = 'success';
          
          // 存储token
          localStorage.setItem('token', response.data.data.token);
        } else {
          resultDiv.innerHTML = '登录失败: ' + response.data.message;
          resultDiv.className = 'error';
        }
      } catch (error) {
        console.error('登录错误:', error);
        resultDiv.innerHTML = '登录错误: ' + (error.message || '未知错误');
        resultDiv.className = 'error';
      }
    }
  </script>
</body>
</html>
