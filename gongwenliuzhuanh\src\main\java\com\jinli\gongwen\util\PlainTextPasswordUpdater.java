package com.jinli.gongwen.util;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Plain Text Password Updater
 * Updates all user passwords to plain text "123456"
 */
public class PlainTextPasswordUpdater {
    
    public static void main(String[] args) {
        updateAllPasswordsToPlainText();
    }
    
    public static void updateAllPasswordsToPlainText() {
        String url = "jdbc:dm://localhost:5236?schema=gongwen";
        String username = "SYSDBA";
        String password = "051001Hzy";
        String driverClass = "dm.jdbc.driver.DmDriver";
        
        try {
            Class.forName(driverClass);
            System.out.println("✓ DM JDBC Driver loaded successfully");
        } catch (ClassNotFoundException e) {
            System.err.println("✗ Failed to load DM JDBC Driver: " + e.getMessage());
            return;
        }
        
        try (Connection connection = DriverManager.getConnection(url, username, password)) {
            System.out.println("✓ Database connection successful!");
            
            // First, show current users
            System.out.println("\n=== Current Users ===");
            showCurrentUsers(connection);
            
            // Update all passwords to plain text "123456"
            String plainTextPassword = "123456";
            String updateSql = "UPDATE SYS_USER SET PASSWORD = ?";
            
            try (PreparedStatement pstmt = connection.prepareStatement(updateSql)) {
                pstmt.setString(1, plainTextPassword);
                int updatedRows = pstmt.executeUpdate();
                System.out.println("\n✓ Successfully updated " + updatedRows + " user passwords to plain text");
            }
            
            // Show updated users
            System.out.println("\n=== Updated Users ===");
            showCurrentUsers(connection);
            
            System.out.println("\n✅ All users can now login with:");
            System.out.println("   Password: " + plainTextPassword);
            System.out.println("   Authentication: Plain text comparison");
            System.out.println("   No encryption/hashing applied");
            
        } catch (SQLException e) {
            System.err.println("✗ Database operation failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void showCurrentUsers(Connection connection) throws SQLException {
        String selectSql = "SELECT USERNAME, REAL_NAME, PASSWORD FROM SYS_USER ORDER BY USERNAME";
        try (PreparedStatement pstmt = connection.prepareStatement(selectSql);
             ResultSet rs = pstmt.executeQuery()) {
            
            System.out.printf("%-15s %-15s %-20s%n", "Username", "Real Name", "Password");
            System.out.println("--------------------------------------------------------");
            
            while (rs.next()) {
                String username = rs.getString("USERNAME");
                String realName = rs.getString("REAL_NAME");
                String pwd = rs.getString("PASSWORD");
                
                // Truncate long passwords for display
                String displayPwd = pwd.length() > 20 ? pwd.substring(0, 17) + "..." : pwd;
                System.out.printf("%-15s %-15s %-20s%n", username, realName, displayPwd);
            }
        }
    }
}
