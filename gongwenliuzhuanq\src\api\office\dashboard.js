import request from '@/utils/request'

// 获取办公室主任仪表板数据
export function getOfficeDashboard() {
  return request({
    url: '/api/office/dashboard',
    method: 'get'
  })
}

// 获取待处理公文列表
export function getPendingDocuments(params) {
  return request({
    url: '/api/office/pending',
    method: 'get',
    params
  })
}

// 获取流转状态概览
export function getFlowStatusOverview() {
  return request({
    url: '/api/office/flowStatus',
    method: 'get'
  })
}
