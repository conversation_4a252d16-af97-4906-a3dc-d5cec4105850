<template>
  <div class="user-workspace">
    <!-- 用户信息头部 -->
    <el-card class="user-info-card" shadow="never">
      <div class="user-header">
        <div class="user-avatar">
          <el-avatar :size="60" :src="userInfo.avatar">
            {{ userInfo.realName?.charAt(0) }}
          </el-avatar>
        </div>
        <div class="user-details">
          <h2>{{ userInfo.realName }}</h2>
          <p class="user-role">{{ userInfo.roleName }}</p>
          <p class="user-dept">{{ userInfo.deptName }}</p>
        </div>
        <div class="user-stats">
          <div class="stat-item">
            <span class="stat-number">{{ statistics.pendingCount || 0 }}</span>
            <span class="stat-label">待处理</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ statistics.processedCount || 0 }}</span>
            <span class="stat-label">已处理</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ statistics.draftCount || 0 }}</span>
            <span class="stat-label">草稿</span>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 功能模块 -->
    <div class="function-modules">
      <el-row :gutter="20">
        <!-- 公文拟制 -->
        <el-col :span="8">
          <el-card class="module-card" shadow="hover" @click="goToModule('draft')">
            <div class="module-content">
              <div class="module-icon">
                <el-icon size="48" color="#409EFF">
                  <EditPen />
                </el-icon>
              </div>
              <h3>公文拟制</h3>
              <p>创建和提交新公文</p>
              <div class="module-stats">
                <span>草稿：{{ statistics.draftCount || 0 }}</span>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 签收公文 -->
        <el-col :span="8">
          <el-card class="module-card" shadow="hover" @click="goToModule('receive')">
            <div class="module-content">
              <div class="module-icon">
                <el-icon size="48" color="#67C23A">
                  <Finished />
                </el-icon>
              </div>
              <h3>签收公文</h3>
              <p>处理待签收的公文</p>
              <div class="module-stats">
                <span>待处理：{{ statistics.pendingCount || 0 }}</span>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 浏览公文 -->
        <el-col :span="8">
          <el-card class="module-card" shadow="hover" @click="goToModule('browse')">
            <div class="module-content">
              <div class="module-icon">
                <el-icon size="48" color="#E6A23C">
                  <Document />
                </el-icon>
              </div>
              <h3>浏览公文</h3>
              <p>查看已签收的历史公文</p>
              <div class="module-stats">
                <span>已处理：{{ statistics.processedCount || 0 }}</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 最近公文 -->
    <el-card class="recent-docs-card">
      <template #header>
        <div class="card-header">
          <span>最近公文</span>
          <el-button type="text" @click="goToModule('browse')">查看更多</el-button>
        </div>
      </template>
      
      <el-table :data="recentDocs" style="width: 100%" v-loading="loading">
        <el-table-column prop="docTitle" label="公文标题" min-width="200" show-overflow-tooltip />
        <el-table-column prop="docType" label="类型" width="100" />
        <el-table-column prop="docLevel" label="紧急程度" width="100">
          <template #default="{ row }">
            <el-tag :type="getLevelType(row.docLevel)" size="small">
              {{ row.docLevel }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="docStatus" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.docStatus)" size="small">
              {{ getStatusText(row.docStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="150" />
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="viewDocument(row)">
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import { EditPen, Finished, Document } from '@element-plus/icons-vue'
import { getUserStatistics, getRecentDocuments } from '@/api/user/workspace'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const userInfo = ref({
  realName: '',
  roleName: '',
  deptName: '',
  avatar: ''
})

const statistics = reactive({
  pendingCount: 0,
  processedCount: 0,
  draftCount: 0
})

const recentDocs = ref([])

// 方法
const loadUserInfo = async () => {
  try {
    // 从用户store获取用户信息
    const user = userStore.userInfo
    if (user) {
      userInfo.value = {
        realName: user.realName || '用户',
        roleName: user.roleName || '用户',
        deptName: user.deptName || '部门',
        avatar: user.avatar || ''
      }
    } else {
      // 模拟用户数据用于测试
      userInfo.value = {
        realName: '张三',
        roleName: '一分厂厂长',
        deptName: '一分厂',
        avatar: ''
      }
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
}

const loadStatistics = async () => {
  try {
    const response = await getUserStatistics()
    console.log('统计数据响应:', response)
    if (response && response.code === 200 && response.data) {
      Object.assign(statistics, response.data)
    } else if (response && response.data) {
      // 直接使用响应数据
      Object.assign(statistics, response.data)
    } else {
      // 使用模拟数据作为后备
      statistics.pendingCount = 3
      statistics.processedCount = 15
      statistics.draftCount = 2
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
    // 使用模拟数据作为后备
    statistics.pendingCount = 3
    statistics.processedCount = 15
    statistics.draftCount = 2
  }
}

const loadRecentDocs = async () => {
  loading.value = true
  try {
    const response = await getRecentDocuments({ pageNum: 1, pageSize: 5 })
    console.log('最近公文响应:', response)
    if (response && response.code === 200 && response.data) {
      recentDocs.value = response.data.records || []
    } else if (response && response.data && response.data.records) {
      // 直接使用响应数据
      recentDocs.value = response.data.records
    } else {
      // 使用模拟数据作为后备
      recentDocs.value = [
        {
          docId: '1',
          docTitle: '关于加强安全生产管理的通知',
          docType: '通知',
          docLevel: '紧急',
          docStatus: 'PROCESSING',
          createTime: '2025-01-30 10:00'
        },
        {
          docId: '2',
          docTitle: '2025年第一季度生产计划',
          docType: '计划',
          docLevel: '普通',
          docStatus: 'COMPLETED',
          createTime: '2025-01-29 14:30'
        }
      ]
    }
  } catch (error) {
    console.error('获取最近公文失败:', error)
    // 使用模拟数据作为后备
    recentDocs.value = [
      {
        docId: '1',
        docTitle: '关于加强安全生产管理的通知',
        docType: '通知',
        docLevel: '紧急',
        docStatus: 'PROCESSING',
        createTime: '2025-01-30 10:00'
      }
    ]
  } finally {
    loading.value = false
  }
}

const goToModule = (module) => {
  switch (module) {
    case 'draft':
      router.push('/user/document/draft')
      break
    case 'receive':
      router.push('/user/document/receive')
      break
    case 'browse':
      router.push('/user/document/browse')
      break
  }
}

const viewDocument = (doc) => {
  router.push(`/user/document/detail/${doc.docId}`)
}

const getLevelType = (level) => {
  const types = {
    '特急': 'danger',
    '紧急': 'warning',
    '普通': 'info'
  }
  return types[level] || 'info'
}

const getStatusType = (status) => {
  const types = {
    'DRAFT': 'info',
    'PROCESSING': 'warning',
    'APPROVED': 'success',
    'REJECTED': 'danger',
    'COMPLETED': 'success'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    'DRAFT': '草稿',
    'PROCESSING': '流转中',
    'APPROVED': '已批准',
    'REJECTED': '已退回',
    'COMPLETED': '已完成'
  }
  return texts[status] || status
}

// 生命周期
onMounted(() => {
  loadUserInfo()
  loadStatistics()
  loadRecentDocs()
})
</script>

<style scoped>
.user-workspace {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 60px);
}

.user-info-card {
  margin-bottom: 20px;
}

.user-header {
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-details h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.user-details p {
  margin: 4px 0;
  color: #606266;
}

.user-role {
  font-weight: 500;
  color: #409EFF;
}

.user-stats {
  display: flex;
  gap: 30px;
  margin-left: auto;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.function-modules {
  margin-bottom: 20px;
}

.module-card {
  cursor: pointer;
  transition: all 0.3s;
  height: 200px;
}

.module-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.module-content {
  text-align: center;
  padding: 20px;
}

.module-icon {
  margin-bottom: 15px;
}

.module-content h3 {
  margin: 0 0 10px 0;
  color: #303133;
}

.module-content p {
  margin: 0 0 15px 0;
  color: #606266;
  font-size: 14px;
}

.module-stats {
  color: #909399;
  font-size: 12px;
}

.recent-docs-card {
  background: white;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
